export declare global {
  interface AppConfig {
    clientId: string;
    clientApiId: string;
    tenantId: string;
    FE_APP_URL: string;
    FE_APP_PORT: string;
    FE_API_URL: string;
    ONETALENT_APP_ID: string;
    FE_ADMIN_GENERAL_API_URL: string;
    ENV: string;
    APP_INSIGHTS_INSTRUMENTATION_KEY: string;
    APP_INSIGHTS_INGESTION_ENDPOINT: string;
    APP_INSIGHTS_LIVE_ENDPOINT: string;
    APP_INSIGHTS_APPLICATION_ID: string;
  }
  namespace NodeJS {
    export interface ProcessEnv extends AppConfig {
      MODULE_CONFIG_NAME: string;
    }
  }

  interface Window {
    __WB_MANIFEST: [];
    APP_CONFIG?: AppConfig;
    ReactNativeWebView: {
      postMessage: (payload: string) => void;
    };
  }
}
