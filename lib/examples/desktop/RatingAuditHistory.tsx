/* eslint-disable local-rules/require-data-attribute */

import { useState } from 'react';

import uniqueId from 'lodash/uniqueId';

import {
  ACTION_HISTORY_STATUS_MAP,
  ActionHistoryItem,
  <PERSON><PERSON>,
  Drawer,
  IllustrationMessage,
  IllustrationVariant,
  Tabs
} from '../../components';
import { cn } from '../../utils';

export const RatingAuditHistory = () => {
  const [isOpen, setIsOpen] = useState(false);
  const id = uniqueId('drawer-');

  const [activeTabId, setActiveTabId] = useState('1');

  const handleTabClick = (id: string) => {
    setActiveTabId(id);
  };

  const items = [
    {
      action: 1,
      date: '13:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: '<PERSON>',
      position: 'Senior Specialist',
      comment:
        'Please verify skill description, it is to narrow on certain aspects, please make it more generic as mentioned by <PERSON><PERSON>. Please verify skill description, it is to narrow on certain aspects, please make it more generic as mentioned by <PERSON><PERSON>'
    },
    {
      action: 3,
      date: '10:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: '<PERSON>shi <PERSON>',
      position: 'On behalf of <PERSON> Blooshi',
      comment:
        'Skill description verified and corrected, please review the new version'
    },
    {
      action: 2,
      date: '13:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Mohammed Al Hashemi',
      position: 'Senior Specialist',
      comment:
        'Please verify skill description, it is to narrow on certain aspects, please make it more generic as mentioned by Khali Al Mansouri'
    },
    {
      action: 4,
      date: '12:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Khalid Al Mansouri',
      position: 'Senior Specialist',
      comment:
        'Please verify skill description, it is to narrow on certain aspects, please make it more generic'
    },
    {
      action: 5,
      date: '11:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Mohammed Al Hashemi',
      position: 'Senior Specialist'
    },
    {
      action: 6,
      date: '10:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Rashi Ali',
      position: 'Senior Specialist'
    }
  ];

  return (
    <div className={cn('min-h-[700px] p-4', id)}>
      <Button onClick={() => setIsOpen(true)}>Open Drawer</Button>
      <Drawer
        isOpen={isOpen}
        header="Rating Audit History"
        onBack={() => setIsOpen(false)}
        onClose={() => setIsOpen(false)}
        portalSelector={`.${id}`}
        className="p-0"
      >
        <Tabs
          activeTabId={activeTabId}
          size="Large"
          items={[
            {
              id: '1',
              label: 'Self Assessment'
            },
            {
              id: '2',
              label: 'My Team Assessment'
            }
          ]}
          onChange={handleTabClick}
        />

        {activeTabId === '1' && (
          <div className="flex flex-col p-20">
            {items.map((item, index, array) => (
              <ActionHistoryItem
                key={item.action}
                statusMap={ACTION_HISTORY_STATUS_MAP}
                item={item}
                isLastItem={index + 1 === array.length}
              />
            ))}
          </div>
        )}

        {activeTabId === '2' && (
          <div className="flex flex-auto flex-col items-center justify-center">
            <IllustrationMessage
              illustrationVariant={IllustrationVariant.Error}
              title="Sorry, an error has occurred"
              description="We can't find the page you are looking for"
            />
          </div>
        )}
      </Drawer>
    </div>
  );
};
