import {
  FC,
  KeyboardEvent,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';

import { Placement } from '@floating-ui/react';

import { cn } from '@/utils';

import { DropdownSingleSelectList } from './DropdownSingleSelectList';
import { DropdownSingleSelectListItem } from './DropdownSingleSelectListItem';

import {
  Dropdown,
  DROPDOWN_SEARCH_MAX_LENGTH,
  dropdownHighlightMatch,
  DropdownInfiniteScroll,
  DropdownNoResultsFound,
  DropdownOption,
  DropdownSize,
  DropdownVariant
} from '../Dropdown';
import { Input, InputVariant } from '../Input';
import { DataAttributesProps } from '../types';

export type DropdownSingleSelectProps = DataAttributesProps &
  DropdownInfiniteScroll & {
    // Container
    className?: string;

    // Inputs
    name?: string;
    label?: string;
    supportingText?: string;
    size?: DropdownSize;
    inputVariant?: InputVariant;
    required?: boolean;
    disabled?: boolean;
    readOnly?: boolean;
    placeholder?: string;
    value?: DropdownOption;
    showClear?: boolean;
    badges?: ReactNode;

    // List
    listClassName?: string;
    options: DropdownOption[];
    placement?: Placement;
    fallbackPlacements?: Placement[];
    drawerHeaderTitle?: string;

    // ListItem
    variant?: DropdownVariant;
    onChange?: (option?: DropdownOption) => void;
    onSearch?: (query: string) => void;
  };

export const DropdownSingleSelect: FC<DropdownSingleSelectProps> = ({
  size = DropdownSize.Large,
  variant = DropdownVariant.Primary,
  options,
  className,
  disabled = false,
  readOnly = false,
  label = 'Select',
  supportingText,
  inputVariant = InputVariant.Primary,
  placeholder = 'Select one item',
  required = false,
  dataAttributes = 'DropdownSingleSelect',
  drawerHeaderTitle = 'Select',
  placement = 'bottom',
  fallbackPlacements = ['bottom', 'top'],
  hasMore = false,
  value = undefined,
  badges,
  showClear,
  listClassName,
  onChange,
  onSearch,
  onLoadMore
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [search, setSearch] = useState('');

  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const listRef = useRef<HTMLUListElement>(null);

  const filteredOptions = useMemo(() => {
    return options.filter((option) => {
      if (onSearch) {
        return true;
      }
      const regex = new RegExp(`(${search})`, 'i');
      return regex.test(option.title) || regex.test(option.description || '');
    });
  }, [search, options, onSearch]);

  const showNoResultsFound = filteredOptions.length === 0 && search;

  const handleSelect = useCallback(
    (option: DropdownOption) => {
      if (disabled || readOnly || option.disabled) {
        return;
      }
      setIsOpen(false);
      setSearch('');
      onSearch?.('');
      setFocusedIndex(-1);
      onChange?.(option);
    },
    [disabled, readOnly, onChange, onSearch]
  );

  const handleClear = useCallback(() => {
    setSearch('');
    onSearch?.('');
    setIsOpen(false);
    setFocusedIndex(-1);
    onChange?.();
  }, [onChange, onSearch]);

  const handleInputKeyDown = useCallback(
    (event: KeyboardEvent<HTMLInputElement>) => {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          if (!isOpen) {
            setIsOpen(true);
            setFocusedIndex(0);
          } else {
            setFocusedIndex((prevIndex) => {
              const nextIndex = prevIndex + 1;
              return nextIndex >= filteredOptions.length ? 0 : nextIndex;
            });
          }
          break;
        case 'ArrowUp':
          event.preventDefault();
          if (isOpen) {
            setFocusedIndex((prevIndex) => {
              const nextIndex = prevIndex - 1;
              return nextIndex < 0 ? filteredOptions.length - 1 : nextIndex;
            });
          }
          break;
        case 'Enter':
          event.preventDefault();
          if (
            isOpen &&
            focusedIndex >= 0 &&
            focusedIndex < filteredOptions.length
          ) {
            handleSelect(filteredOptions[focusedIndex]);
          } else if (!isOpen) {
            setIsOpen(true);
            setFocusedIndex(0);
          }
          break;
        case 'Escape':
          event.preventDefault();
          setIsOpen(false);
          setFocusedIndex(-1);
          break;
        case 'Tab':
          setIsOpen(false);
          setFocusedIndex(-1);
          break;
      }
    },
    [isOpen, filteredOptions, focusedIndex, handleSelect]
  );

  // Scroll focused item into view
  const scrollFocusedItemIntoView = useCallback(() => {
    if (listRef.current && focusedIndex >= 0) {
      const items = listRef.current.getElementsByTagName('li');
      if (items[focusedIndex]) {
        items[focusedIndex].scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [focusedIndex]);

  // Update scroll position when focused index changes
  useEffect(() => {
    if (isOpen) {
      scrollFocusedItemIntoView();
    }
  }, [isOpen, scrollFocusedItemIntoView]);

  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(event.target.value);
      onSearch?.(event.target.value);
      onChange?.();
      if (event.target.value) {
        setIsOpen(true);
      }
    },
    [onSearch, onChange]
  );

  const inputValue = value?.title || search || '';

  return (
    <Dropdown
      dataAttributes={dataAttributes}
      className={className}
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      title={drawerHeaderTitle}
      fallbackPlacements={fallbackPlacements}
      placement={placement}
      renderTrigger={({ getTriggerProps }) => (
        <Input
          {...getTriggerProps()}
          inputContentClassName="w-full"
          dataAttributes="DropdownSingleSelectTrigger"
          label={label}
          size={size}
          placeholder={placeholder}
          rightIcon={isOpen ? 'Expand_up_light' : 'Expand_down_light'}
          maxLength={DROPDOWN_SEARCH_MAX_LENGTH}
          value={inputValue}
          supportingText={supportingText}
          variant={inputVariant}
          badges={badges}
          showClear={showClear}
          onChange={handleInputChange}
          onClear={handleClear}
          onKeyDown={handleInputKeyDown}
          onRightIconClick={() =>
            !disabled && !readOnly && setIsOpen((prev) => !prev)
          }
          disabled={disabled}
          readOnly={readOnly}
          required={required}
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          role="combobox"
          aria-activedescendant={
            focusedIndex >= 0
              ? `option-${filteredOptions[focusedIndex]?.id}`
              : undefined
          }
        />
      )}
      renderDropdown={() => (
        <DropdownSingleSelectList
          ref={listRef}
          className={cn(
            { 'justify-center': showNoResultsFound },
            listClassName
          )}
          input={{
            label,
            placeholder,
            rightIcon: isOpen ? 'Expand_up_light' : 'Expand_down_light',
            maxLength: DROPDOWN_SEARCH_MAX_LENGTH,
            value: inputValue,
            supportingText,
            variant: inputVariant,
            badges,
            showClear,
            required,
            onChange: handleInputChange,
            onClear: handleClear,
            onKeyDown: handleInputKeyDown
          }}
          hasMore={hasMore}
          onLoadMore={onLoadMore}
        >
          {showNoResultsFound && (
            <DropdownNoResultsFound className="flex h-full w-full flex-auto flex-col items-center justify-center md:!min-h-[248px]" />
          )}
          {filteredOptions.map((option, index) => (
            <DropdownSingleSelectListItem
              key={option.id}
              option={option}
              variant={variant}
              title={dropdownHighlightMatch(option.title, search)}
              description={
                option.description &&
                dropdownHighlightMatch(option.description, search)
              }
              selected={value?.id === option.id}
              disabled={option.disabled}
              tooltip={option.tooltip}
              onClick={handleSelect}
              data-id={`option-${option.id}`}
              data-focused={index === focusedIndex}
            />
          ))}
        </DropdownSingleSelectList>
      )}
    />
  );
};
