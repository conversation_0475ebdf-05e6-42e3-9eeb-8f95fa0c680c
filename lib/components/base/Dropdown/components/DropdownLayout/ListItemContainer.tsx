import { FC } from 'react';

import { cva } from 'class-variance-authority';

import { cn } from '@/utils';

import { DropdownVariant } from '../../Dropdown.types';

import { Tooltip, TooltipProps } from '../../../Tooltip';

const sharedStyles =
  'group/item p-12 border-b border-solid border-divider-mid flex last:border-b-0 text-body-2-regular';

const defaultListItemVariants = cva(sharedStyles, {
  variants: {
    variant: {
      [DropdownVariant.Primary]: cn(
        'cursor-pointer',
        'bg-surface-grey_0 md:bg-dropdown-background text-text-heading',
        'hover:bg-dropdown-dropdown-hover hover:text-text-heading'
      ),
      [DropdownVariant.Secondary]: cn(
        'cursor-pointer',
        'bg-surface-grey_0 md:bg-dropdown-background text-text-heading',
        'hover:bg-surface-grey_30 hover:text-text-heading'
      ),
      [DropdownVariant.Purple]: cn(
        'cursor-pointer',
        'bg-surface-grey_0 md:bg-dropdown-background text-text-heading',
        'hover:bg-button-secondary-purple-fill-rested hover:text-text-heading'
      )
    }
  },
  defaultVariants: {
    variant: DropdownVariant.Primary
  }
});

const disabledListItemVariants = cva(
  cn(
    sharedStyles,
    'bg-surface-grey_0 md:!bg-dropdown-background text-text-secondary cursor-not-allowed'
  )
);

export type ListItemContainerProps = React.ComponentProps<'li'> & {
  variant?: DropdownVariant;
  disabled?: boolean;
  selected?: boolean;
  tooltip?: TooltipProps;
};

export const ListItemContainer: FC<ListItemContainerProps> = ({
  className,
  children,
  variant = DropdownVariant.Primary,
  disabled,
  tooltip,
  onClick,
  ...props
}) => {
  let listItemClassName = defaultListItemVariants({ variant, className });

  if (disabled) {
    listItemClassName = disabledListItemVariants({ className });
  }

  const handleClick = (e: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
    if (!disabled) {
      onClick?.(e);
    }
  };

  const renderContent = () => {
    if (tooltip && tooltip.title) {
      return (
        <Tooltip
          {...tooltip}
          classNameTrigger="min-w-0 md:!truncate md:!whitespace-nowrap"
        >
          {children}
        </Tooltip>
      );
    } else {
      return children;
    }
  };

  return (
    <li
      data-attributes="DropdownListItemContainer"
      className={listItemClassName}
      role="option"
      onClick={handleClick}
      {...props}
    >
      {renderContent()}
    </li>
  );
};
