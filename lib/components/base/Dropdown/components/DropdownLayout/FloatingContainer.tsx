import { FC, PropsWithChildren } from 'react';

import { FloatingPortal } from '@floating-ui/react';

import { cn } from '@/utils';

import { useDropdownContext } from '../../Dropdown.hooks';

import { DataAttributesProps } from '../../../types';

type FloatingContainerProps = { className?: string } & DataAttributesProps;

export const FloatingContainer: FC<
  PropsWithChildren<FloatingContainerProps>
> = ({ children, className, dataAttributes = 'DropdownFloatingContainer' }) => {
  const { refs, floatingStyles, getFloatingProps, isOpen } =
    useDropdownContext();

  if (!isOpen) {
    return null;
  }

  return (
    <FloatingPortal>
      <div
        className={cn(
          'p-12',
          'z-[1070]',
          'flex flex-col justify-center gap-y-4',
          'max-h-[464px] w-full',
          'bg-surface-grey_0 md:!bg-dropdown-background',
          'rounded-lg shadow-dropdown focus-visible:outline-none focus-visible:ring-0',
          className
        )}
        ref={refs.setFloating}
        style={floatingStyles}
        {...getFloatingProps()}
        role="listbox"
        aria-label="options"
        data-attributes={dataAttributes}
      >
        {children}
      </div>
    </FloatingPortal>
  );
};
