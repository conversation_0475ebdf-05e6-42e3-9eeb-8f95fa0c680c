import { forwardRef, PropsWithChildren, useMemo } from 'react';

import { cn, generateId } from '@/utils';

import { useDropdownScrollParent } from '../../Dropdown.hooks';
import { DropdownInfiniteScroll } from '../../Dropdown.types';
import { DropdownNoResultsFound } from '../../DropdownNoResultsFound';
import InfiniteScroll from '../../InfiniteScrollWrapper';

import { DataAttributesProps } from '../../../types';

type ListProps = DropdownInfiniteScroll &
  DataAttributesProps & {
    showSelected?: boolean;
    showNoResultsFound?: boolean;
  };

export const List = forwardRef<HTMLUListElement, PropsWithChildren<ListProps>>(
  (
    {
      dataAttributes = 'DropdownList',
      showNoResultsFound,
      showSelected,
      children,
      hasMore,
      onLoadMore
    },
    ref
  ) => {
    const id = useMemo(() => generateId(dataAttributes), [dataAttributes]);
    const getScrollParent = useDropdownScrollParent(`#${id}`);

    if (showNoResultsFound) {
      return <DropdownNoResultsFound />;
    }

    return (
      <ul
        data-attributes={dataAttributes}
        ref={ref}
        id={id}
        className={cn(
          'w-full justify-center overflow-y-auto',
          'md:!border-b md:!border-solid md:!border-divider-mid'
        )}
      >
        {showSelected ? (
          children
        ) : (
          <InfiniteScroll
            threshold={135}
            pageStart={0}
            loadMore={onLoadMore}
            hasMore={hasMore}
            loader={<div className="flex flex-auto" key={0}></div>}
            useWindow={false}
            getScrollParent={getScrollParent}
          >
            {children}
          </InfiniteScroll>
        )}
      </ul>
    );
  }
);
