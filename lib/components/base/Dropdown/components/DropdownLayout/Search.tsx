import { FC } from 'react';

import { Input, InputProps } from '../../../Input';
import { DataAttributesProps } from '../../../types';

type SearchProps = Pick<
  InputProps,
  'value' | 'onChange' | 'onClear' | 'maxLength'
> &
  DataAttributesProps;

export const Search: FC<SearchProps> = ({
  value,
  onChange,
  onClear,
  maxLength,
  dataAttributes = 'DropdownSearch'
}) => {
  return (
    <Input
      showLabel={false}
      dataAttributes={dataAttributes}
      size="Medium"
      placeholder="Search"
      leftIcon="Search_light"
      value={value}
      showClear={true}
      autoFocus
      maxLength={maxLength}
      onChange={onChange}
      onClear={onClear}
    />
  );
};
