import { FC } from 'react';

import { cn } from '@/utils';

import { Button } from '../../../Button';
import { Link } from '../../../Link';
import { Toggle } from '../../../Toggle';
import { DataAttributesProps } from '../../../types';

type FooterProps = DataAttributesProps & {
  showSelected: boolean;
  selectedOptionsCount: number;
  onClearSelectedOptions: () => void;
  onSetShowSelected: (showSelected: boolean) => void;
  onApply?: () => void;
  className?: string;
  contentClassName?: string;
};

export const Footer: FC<FooterProps> = ({
  dataAttributes = 'DropdownFooter',
  showSelected,
  selectedOptionsCount,
  onClearSelectedOptions,
  onApply,
  onSetShowSelected,
  className,
  contentClassName
}) => {
  return (
    <div
      data-attributes={dataAttributes}
      className={cn(
        'rounded-xl md:!rounded-b-xl',
        'min-h-[42px] w-full',
        'flex flex-auto items-center justify-end',
        'bg-surface-grey_0 shadow-footer md:!bg-dropdown-background md:!shadow-none',
        className
      )}
    >
      <div className={cn('flex flex-auto items-center', contentClassName)}>
        <span>
          <Toggle
            className="gap-12"
            label={`Show selected (${selectedOptionsCount})`}
            labelAlignment="End"
            checked={showSelected}
            disabled={selectedOptionsCount === 0 && !showSelected}
            onChange={onSetShowSelected}
          />
        </span>
        <span className="flex flex-auto"></span>
        <Link
          size="Medium"
          disabled={selectedOptionsCount === 0}
          onClick={onClearSelectedOptions}
        >
          Clear All
        </Link>
        {onApply ? (
          <Button
            size="Small"
            className="ml-12 min-h-[34px] min-w-[80px]"
            onClick={onApply}
          >
            Apply
          </Button>
        ) : null}
      </div>
    </div>
  );
};
