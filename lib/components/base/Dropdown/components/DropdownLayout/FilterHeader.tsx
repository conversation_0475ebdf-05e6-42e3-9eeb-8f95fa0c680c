import { FC } from 'react';

import { Link } from '../../../Link';
import { DataAttributesProps } from '../../../types';

type FilterHeaderProps = {
  label: string;
  onRemoveClick?: () => void;
} & DataAttributesProps;

export const FilterHeader: FC<FilterHeaderProps> = ({
  label,
  onRemoveClick,
  dataAttributes = 'DropdownFilterHeader'
}) => {
  return (
    <div
      data-attributes={dataAttributes}
      className="flex items-center justify-between gap-x-16 py-[10px]"
    >
      <span className="text-body-2-regular text-input-text-disabled">
        {label}:
      </span>
      {onRemoveClick && (
        <Link onClick={onRemoveClick} leftIcon="Trash">
          Remove Filter
        </Link>
      )}
    </div>
  );
};
