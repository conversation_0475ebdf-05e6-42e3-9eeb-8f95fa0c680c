import { FC, ReactNode } from 'react';

import { cn } from '@/utils';

import { ListItemContainer } from './ListItemContainer';

import { DropdownOption, DropdownVariant } from '../../Dropdown.types';

import { Avatar } from '../../../Avatar';
import { Checkbox } from '../../../Checkbox';
import { TooltipProps } from '../../../Tooltip';
import { DataAttributesProps } from '../../../types';

export type ListItemProps = DataAttributesProps & {
  title: ReactNode;
  description?: ReactNode;
  option: DropdownOption;
  variant: DropdownVariant;
  selected?: boolean;
  disabled?: boolean;
  tooltip?: TooltipProps;
  onClick?: (option: DropdownOption) => void;
  'data-id'?: string;
  'data-focused'?: boolean;
};

export const ListItem: FC<ListItemProps> = ({
  title,
  description,
  option,
  variant,
  selected,
  disabled,
  tooltip,
  onClick,
  'data-id': dataId,
  'data-focused': dataFocused,
  dataAttributes = 'DropdownListItem'
}) => {
  const handleSelect = (option: DropdownOption) => {
    onClick?.(option);
  };

  return (
    <ListItemContainer
      key={option.id}
      id={dataId}
      data-attributes={dataAttributes}
      variant={variant}
      selected={selected}
      disabled={disabled}
      tooltip={tooltip}
      className={cn(dataFocused && 'bg-dropdown-dropdown-hover')}
      onClick={() => handleSelect(option)}
    >
      <div className="flex min-w-0 flex-row items-center gap-8 md:!truncate md:!whitespace-nowrap">
        <Checkbox
          size="Medium"
          checked={selected}
          disabled={disabled}
          onChange={() => handleSelect(option)}
        />
        {option.avatar && <Avatar {...option.avatar} />}
        {description && (
          <div className="flex min-w-0 flex-auto flex-col gap-4 md:!truncate md:!whitespace-nowrap">
            <span
              className={cn(
                'md:!truncate',
                'text-body-2-medium text-text-heading',
                disabled && 'text-text-secondary'
              )}
            >
              {title}
            </span>
            <span
              className={cn(
                'md:!truncate',
                'text-body-4-regular text-text-body',
                disabled && 'text-text-secondary'
              )}
            >
              {description}
            </span>
          </div>
        )}
        {!description && (
          <span className="min-w-0 md:!truncate md:!whitespace-nowrap">
            {title}
          </span>
        )}
      </div>
    </ListItemContainer>
  );
};
