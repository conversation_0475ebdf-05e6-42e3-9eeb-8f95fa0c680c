import { createContext } from 'react';

import { ExtendedRefs, ReferenceType } from '@floating-ui/react';

export type DropdownContext = {
  isOpen: boolean;
  title?: string;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  refs: {
    reference: React.MutableRefObject<ReferenceType | null>;
    floating: React.MutableRefObject<HTMLElement | null>;
    setReference: (node: ReferenceType | null) => void;
    setFloating: (node: HTMLElement | null) => void;
  } & ExtendedRefs<ReferenceType>;
  floatingStyles: React.CSSProperties;
  getReferenceProps: (
    userProps?: React.HTMLProps<Element>
  ) => Record<string, unknown>;
  getFloatingProps: (
    userProps?: React.HTMLProps<HTMLElement>
  ) => Record<string, unknown>;
};

export const DropdownContext = createContext<DropdownContext | null>(null);
