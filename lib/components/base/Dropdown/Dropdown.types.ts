import { AvatarProps } from '../Avatar';
import { TooltipProps } from '../Tooltip';

export const DropdownVariant = {
  Primary: 'Primary',
  Secondary: 'Secondary',
  Purple: 'Purple'
} as const;

export type DropdownVariant =
  (typeof DropdownVariant)[keyof typeof DropdownVariant];

export const DropdownSize = {
  Large: 'Large',
  Medium: 'Medium',
  Small: 'Small'
} as const;

export type DropdownSize = (typeof DropdownSize)[keyof typeof DropdownSize];

export type DropdownOption = {
  id: string | number;
  title: string;
  description?: string;
  avatar?: AvatarProps;
  disabled?: boolean;
  tooltip?: TooltipProps;
};

export type DropdownTreeListOption = DropdownOption & {
  children?: DropdownOption[];
};

export type DropdownOptionUser = {
  id: string | number;
  email: string;
  fullName: string;
  position: string;
  disabled?: boolean;
  tooltip?: TooltipProps;
};

export type DropdownInfiniteScroll = {
  hasMore?: boolean;
  onLoadMore?: () => void;
};

export const DROPDOWN_SEARCH_MAX_LENGTH = 370;
