import { useState } from 'react';

import type { Meta, StoryObj } from '@storybook/react';

import { DataTable } from './DataTable';
import { TableDataSourceState } from './Table';
import { mockColumns, mockUsers } from './tableData';

const meta: Meta<typeof DataTable> = {
  title: 'Design System/Components/DataTable/DataTable',
  component: DataTable,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'A complete data table component with built-in pagination and page size selection. Extends the base Table component with pagination controls.'
      }
    }
  },
  argTypes: {
    showPagination: {
      description: 'Whether to show pagination controls',
      control: { type: 'boolean' }
    },
    showPageSizeSelector: {
      description: 'Whether to show page size selector dropdown',
      control: { type: 'boolean' }
    },
    pageSizeOptions: {
      description: 'Available page size options for the dropdown',
      control: { type: 'object' },
      defaultValue: [
        { id: 10, title: '10' },
        { id: 25, title: '25' },
        { id: 50, title: '50' }
      ]
    },
    containerClassName: {
      description: 'CSS classes for the container',
      control: { type: 'text' }
    }
  },
  args: {
    showPagination: true,
    showPageSizeSelector: true,
    containerClassName: ''
  },
  tags: ['autodocs']
};

export default meta;
type Story = StoryObj<typeof meta>;

const DefaultDataTable = (args: Record<string, unknown>) => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, undefined>
  >({
    pageNumber: 1,
    pageSize: 5,
    sortBy: undefined,
    sortDirection: 'asc',
    filters: undefined
  });

  const isLoading = args.isLoading as boolean;
  const isError = args.isError as boolean;

  const queryResult = {
    data: isError
      ? undefined
      : {
          items: mockUsers.slice(
            (dataSourceState.pageNumber - 1) * dataSourceState.pageSize,
            dataSourceState.pageNumber * dataSourceState.pageSize
          ),
          totalCount: mockUsers.length,
          pageNumber: dataSourceState.pageNumber,
          pageSize: dataSourceState.pageSize,
          hasNextPage:
            dataSourceState.pageNumber * dataSourceState.pageSize <
            mockUsers.length,
          hasPreviousPage: dataSourceState.pageNumber > 1
        },
    isLoading,
    isError,
    error: isError ? ({ message: 'Failed to load data' } as Error) : null
  };

  return (
    <DataTable
      queryResult={queryResult}
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      columns={mockColumns}
      pageSizeOptions={[
        { id: 5, title: '5' },
        { id: 10, title: '10' }
      ]}
      showPagination={args.showPagination as boolean}
      showPageSizeSelector={args.showPageSizeSelector as boolean}
      containerClassName={args.containerClassName as string}
    />
  );
};

const CustomPageSizeDataTable = (args: Record<string, unknown>) => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, undefined>
  >({
    pageNumber: 1,
    pageSize: 10,
    sortBy: undefined,
    sortDirection: 'asc',
    filters: undefined
  });

  const customPageSizeOptions = [
    { id: 1, title: '1' },
    { id: 2, title: '2' },
    { id: 3, title: '3' }
  ];

  const isLoading = args.isLoading as boolean;
  const isError = args.isError as boolean;

  const queryResult = {
    data: isError
      ? undefined
      : {
          items: mockUsers.slice(
            (dataSourceState.pageNumber - 1) * dataSourceState.pageSize,
            dataSourceState.pageNumber * dataSourceState.pageSize
          ),
          totalCount: mockUsers.length,
          pageNumber: dataSourceState.pageNumber,
          pageSize: dataSourceState.pageSize,
          hasNextPage:
            dataSourceState.pageNumber * dataSourceState.pageSize <
            mockUsers.length,
          hasPreviousPage: dataSourceState.pageNumber > 1
        },
    isLoading,
    isError,
    error: isError ? ({ message: 'Failed to load data' } as Error) : null
  };

  return (
    <DataTable
      queryResult={queryResult}
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      columns={mockColumns}
      pageSizeOptions={customPageSizeOptions}
      showPagination={args.showPagination as boolean}
      showPageSizeSelector={args.showPageSizeSelector as boolean}
      containerClassName={args.containerClassName as string}
    />
  );
};

export const Default: Story = {
  render: (args: Record<string, unknown>) => <DefaultDataTable {...args} />
};

export const WithCustomPageSizeOptions: Story = {
  render: (args: Record<string, unknown>) => (
    <CustomPageSizeDataTable {...args} />
  )
};

export const WithoutPagination: Story = {
  render: (args: Record<string, unknown>) => <DefaultDataTable {...args} />,
  args: {
    showPagination: false
  }
};

export const WithoutPageSizeSelector: Story = {
  render: (args: Record<string, unknown>) => <DefaultDataTable {...args} />,
  args: {
    showPageSizeSelector: false
  }
};

export const Loading: Story = {
  render: (args: Record<string, unknown>) => <DefaultDataTable {...args} />,
  args: {
    isLoading: true
  }
};

export const Error: Story = {
  render: (args: Record<string, unknown>) => <DefaultDataTable {...args} />,
  args: {
    isError: true
  }
};
