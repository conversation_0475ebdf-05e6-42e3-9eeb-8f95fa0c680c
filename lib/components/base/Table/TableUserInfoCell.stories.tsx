import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { TableUserInfoCell, TagAlignment } from './TableUserInfoCell';

import { AvatarSize } from '../Avatar';
import { Icon } from '../Icon';
import { Tag, TagType, TagVariant } from '../Tag';

const meta = {
  title: 'Design System/Components/Table/TableUserInfoCell',
  component: TableUserInfoCell,
  tags: ['autodocs', 'Stable'],
  parameters: {
    layout: 'padded'
  },
  args: {
    email: '<EMAIL>',
    fullName: '<PERSON>',
    position: 'Senior Operations Specialist & Project Manager, ADNOC HQ',
    company: 'ADNOC',
    size: AvatarSize.Medium
  },
  argTypes: {
    email: {
      control: 'text',
      description: 'User email address for avatar generation'
    },
    fullName: {
      control: 'text',
      description: 'User full name'
    },
    position: {
      control: 'text',
      description: 'User job position'
    },
    company: {
      control: 'text',
      description: 'User company name'
    },
    size: {
      control: 'select',
      options: Object.values(AvatarSize),
      description: 'Avatar size'
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes'
    },
    tagAlignment: {
      control: 'select',
      options: ['top', 'center'],
      description: 'Tag alignment'
    },
    dataAttributes: {
      control: 'text',
      description: 'Data attributes for testing'
    }
  }
} satisfies Meta<typeof TableUserInfoCell>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const MediumSize: Story = {
  args: {
    email: '<EMAIL>',
    fullName: 'Alice Johnson',
    position: 'Senior UX Designer',
    company: 'ADNOC Digital',
    size: AvatarSize.Medium
  }
};

export const SmallSize: Story = {
  args: {
    email: '<EMAIL>',
    fullName: 'Bob Wilson',
    position: 'Designer',
    company: 'ADNOC',
    size: AvatarSize.Small
  }
};

export const OnlyName: Story = {
  args: {
    email: '<EMAIL>',
    fullName: 'Christopher Anderson-Williams',
    company: undefined,
    position: undefined
  }
};

export const LongName: Story = {
  args: {
    email: '<EMAIL>',
    fullName:
      'Christopher Anderson-Williams Christopher Anderson-Williams Christopher Anderson-Williams Christopher Anderson-Williams Christopher Anderson-Williams Christopher Anderson-Williams',
    position: 'Senior Full Stack Developer & Technical Lead',
    company: 'ADNOC Digital Solutions'
  }
};

export const LongPosition: Story = {
  args: {
    email: '<EMAIL>',
    fullName: 'Christopher Anderson-Williams',
    position:
      'Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead',
    company: 'ADNOC Digital Solutions'
  }
};

export const LongNameAndPosition: Story = {
  args: {
    email: '<EMAIL>',
    fullName:
      'Christopher Anderson-Williams Christopher Anderson-Williams Christopher Anderson-Williams Christopher Anderson-Williams Christopher Anderson-Williams Christopher Anderson-Williams',
    position:
      'Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead Senior Full Stack Developer & Technical Lead',
    company: 'ADNOC Digital Solutions'
  }
};

export const TagsAboveTitle: Story = {
  args: {
    email: '<EMAIL>',
    fullName: 'Christopher Anderson-Williams',
    position: 'Senior Full Stack Developer & Technical Lead',
    company: 'ADNOC Digital Solutions',
    tagAlignment: TagAlignment.Top,
    tags: (
      <div className="flex items-center gap-8">
        <Tag type={TagType.Solid} variant={TagVariant.Grey}>
          Inactive
        </Tag>
        <Tag type={TagType.Light} variant={TagVariant.Grey}>
          Assigned
        </Tag>
      </div>
    )
  }
};

export const TagsWithTitle: Story = {
  args: {
    email: '<EMAIL>',
    fullName: 'Christopher Anderson-Williams',
    position: 'Senior Full Stack Developer & Technical Lead',
    company: 'ADNOC Digital Solutions',
    tagAlignment: TagAlignment.Center,
    tags: (
      <div className="flex items-center gap-8">
        <Icon
          name="Unlock"
          className="h-20 min-h-20 w-20 min-w-20 text-checkbox-icon-defualt-selected"
        />
        <Tag type={TagType.Light} variant={TagVariant.LightBlue}>
          Requested By Manager
        </Tag>
      </div>
    )
  }
};
