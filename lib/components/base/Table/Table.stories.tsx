import { useState } from 'react';

import type { TableDataSourceState } from './Table';
import { Table } from './Table';
import {
  createMockQueryResult,
  mockColumns,
  mockTeamObjectives,
  mockTeamObjectivesColumns,
  mockUsers
} from './tableData';

import { ButtonSize, ButtonVariant, IconButton } from '../Button';
import { IllustrationMessage } from '../Illustration';

const meta = {
  title: 'Design System/Components/Table/Table',
  component: Table,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'A flexible table component with sorting and configurable column widths. Pagination and filtering are handled by external components.'
      }
    }
  },
  argTypes: {
    dataSourceState: {
      description:
        'Current state of the data source including pagination and sorting',
      control: { type: 'object' }
    },
    setDataSourceState: {
      description: 'Function to update the data source state',
      control: false
    },
    queryResult: {
      description: 'Query result containing data, loading, and error states',
      control: false
    },
    columns: {
      description: 'Array of column configurations',
      control: false
    },
    columnGroups: {
      description: 'Optional column groups for header organization',
      control: false
    },
    renderRowContent: {
      description: 'Custom render function for row content',
      control: false
    },
    renderError: {
      description: 'Custom error state render function',
      control: false
    },
    renderNoResults: {
      description: 'Custom no results state render function',
      control: false
    },
    onRowClick: {
      description: 'Callback function when a row is clicked',
      control: false
    },
    getRowOptions: {
      description: 'Function to get row-specific options',
      control: false
    },
    isFoldedByDefault: {
      description: 'Function to determine if rows should be folded by default',
      control: false
    },
    styles: {
      description: 'Custom styles for the table container',
      control: { type: 'object' }
    },
    dataAttributes: {
      description: 'Data attributes for the table container',
      control: { type: 'text' }
    },
    classNames: {
      description: 'Additional CSS classes for the table container',
      control: { type: 'text' }
    },
    errorContainerClassName: {
      description: 'CSS classes for the error container',
      control: { type: 'text' }
    },
    onVisibleRowsCountChanged: {
      description: 'Callback when visible rows count changes',
      control: false
    },
    loadingStrategy: {
      description: 'Strategy for showing loading state',
      control: { type: 'select' },
      options: ['skeleton', 'spinner', 'overlay']
    },
    stickyButton: {
      description: 'Configuration for sticky action buttons',
      control: false
    },
    withCustomRow: {
      description: 'Enable custom row rendering',
      control: { type: 'boolean' }
    },
    rowDataAttributes: {
      description: 'Data attributes for table rows',
      control: { type: 'text' }
    },
    getCustomRowExtraOptions: {
      description: 'Function to get extra options for custom rows',
      control: false
    }
  },
  args: {
    dataSourceState: {
      pageNumber: 1,
      pageSize: 10
    },
    styles: {
      minHeight: 400
    },
    classNames: '',
    dataAttributes: '',
    errorContainerClassName: '',
    loadingStrategy: 'spinner',
    withCustomRow: false,
    rowDataAttributes: ''
  }
};

export default meta;

const DefaultTable = (args: Record<string, unknown>) => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >(
    (args.dataSourceState as TableDataSourceState<string, unknown>) || {
      pageNumber: 1,
      pageSize: 10
    }
  );

  const baseProps = {
    dataSourceState,
    setDataSourceState,
    queryResult: createMockQueryResult(mockUsers),
    columns: mockColumns,
    styles: args.styles as
      | { minHeight?: number | string; maxWidth?: number | string }
      | undefined,
    classNames: args.classNames as string | undefined,
    dataAttributes: args.dataAttributes as string | undefined,
    loadingStrategy: args.loadingStrategy as
      | 'skeleton'
      | 'spinner'
      | 'overlay'
      | undefined
  };

  if (args.withCustomRow) {
    return (
      <Table
        {...baseProps}
        withCustomRow={true}
        rowDataAttributes={args.rowDataAttributes as string | undefined}
      />
    );
  }

  return <Table {...baseProps} />;
};

export const Default = {
  render: (args: Record<string, unknown>) => <DefaultTable {...args} />
};

// export const Interactive = {
//   render: (args: Record<string, unknown>) => <DefaultTable {...args} />,
//   args: {
//     dataSourceState: {
//       pageNumber: 1,
//       pageSize: 10,
//       sortBy: 'name',
//       sortDirection: 'asc' as const
//     },
//     styles: {
//       minHeight: 500,
//       maxWidth: '100%'
//     },
//     classNames: 'border border-gray-200 rounded-lg shadow-sm',
//     dataAttributes: 'interactive-table',
//     loadingStrategy: 'spinner' as const,
//     withCustomRow: false,
//     rowDataAttributes: 'table-row'
//   }
// };

const LoadingTable = () => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  return (
    <Table
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      queryResult={createMockQueryResult([], true)}
      columns={mockColumns}
    />
  );
};

export const Loading = {
  name: 'Table with Loading State',
  render: () => <LoadingTable />
};

const ErrorTable = () => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  return (
    <Table
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      queryResult={createMockQueryResult([], false, true)}
      columns={mockColumns}
      renderError={() => (
        <IllustrationMessage
          illustrationVariant="Error"
          title="Sorry, an error has occurred"
          description="We can’t seem to find the page you are looking for"
        />
      )}
    />
  );
};

export const Error = {
  name: 'Table with Error State',
  render: () => <ErrorTable />
};

const EmptyTable = () => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  return (
    <Table
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      queryResult={createMockQueryResult([])}
      columns={mockColumns}
      renderNoResults={() => (
        <IllustrationMessage
          illustrationVariant="NothingFound"
          description={
            <p>
              You are all caught up!
              <br />
              No new items here!
            </p>
          }
          title="Sorry, no results found"
        />
      )}
    />
  );
};

export const EmptyState = {
  name: 'Table with Empty State',
  render: () => <EmptyTable />
};

const TeamObjectivesTable = () => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  return (
    <Table
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      queryResult={createMockQueryResult(mockTeamObjectives)}
      columns={mockTeamObjectivesColumns}
    />
  );
};

export const TeamObjectives = {
  name: 'Example: My Team Objectives',
  render: () => <TeamObjectivesTable />
};

const SmallDatasetTable = () => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  return (
    <Table
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      queryResult={createMockQueryResult(mockUsers.slice(0, 3))}
      columns={mockColumns}
    />
  );
};

export const SmallDataset = {
  render: () => <SmallDatasetTable />
};

const CustomStyledTable = (args: Record<string, unknown>) => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  return (
    <Table
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      queryResult={createMockQueryResult(mockUsers)}
      columns={mockColumns}
      styles={(args.styles as Record<string, unknown>) || { minHeight: 400 }}
      classNames={
        (args.classNames as string) || 'border-2 border-blue-200 rounded-lg'
      }
    />
  );
};

export const CustomStyling = {
  render: (args: Record<string, unknown>) => <CustomStyledTable {...args} />,
  args: {
    styles: { minHeight: 400 },
    classNames: 'border-2 border-blue-200 rounded-lg'
  }
};

const StickyButtonTable = () => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  return (
    <Table
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      queryResult={createMockQueryResult(mockUsers)}
      columns={mockColumns}
      stickyButton={{
        width: 112, // Width for 2 buttons + gap + padding
        render: (user) => (
          <div className="flex w-full flex-row items-center justify-center gap-8">
            <IconButton
              icon="Kebab"
              variant={ButtonVariant.Tertiary}
              size={ButtonSize.Medium}
              onClick={() => alert(`Context menu for: ${user.name}`)}
              tooltip={{ title: 'More options' }}
            />
            <IconButton
              icon="Edit"
              variant={ButtonVariant.Tertiary}
              size={ButtonSize.Medium}
              onClick={() => alert(`Edit user: ${user.name}`)}
              tooltip={{ title: 'Edit user' }}
            />
          </div>
        )
      }}
    />
  );
};

export const WithStickyContextMenu = {
  render: () => <StickyButtonTable />
};

const StickyButtonWithIconTable = () => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  return (
    <Table
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      queryResult={createMockQueryResult(mockUsers)}
      columns={mockColumns}
      stickyButton={{
        width: 56, // Width for 1 button + padding
        render: (user) => (
          <div className="flex w-full flex-row items-center justify-center gap-8">
            <IconButton
              icon="Menu"
              variant={ButtonVariant.Tertiary}
              size={ButtonSize.Medium}
              onClick={() => alert(`Options for: ${user.name}`)}
              tooltip={{ title: 'Menu options' }}
            />
          </div>
        )
      }}
    />
  );
};

export const WithCustomStickyIcon = {
  render: () => <StickyButtonWithIconTable />
};

const StickyButtonDisabledTable = () => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  return (
    <Table
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      queryResult={createMockQueryResult(mockUsers)}
      columns={mockColumns}
      stickyButton={{
        width: 156, // Width for 3 buttons + gap-12 (4px) + padding
        render: (user) => {
          const isDisabled =
            user.status === 'inactive' ||
            (typeof user.status === 'object' && user.status.id === 'inactive');

          return (
            // 152px
            <div className="flex w-full flex-row items-center justify-center gap-8">
              <IconButton
                icon="Setting_line"
                variant={ButtonVariant.Tertiary}
                size={ButtonSize.Medium}
                onClick={() =>
                  !isDisabled && alert(`Settings for: ${user.name}`)
                }
                disabled={isDisabled}
                tooltip={{
                  title: isDisabled ? 'User is inactive' : 'User settings'
                }}
              />
              <IconButton
                icon="Trash"
                variant={ButtonVariant.Tertiary}
                size={ButtonSize.Medium}
                onClick={() =>
                  !isDisabled && alert(`Delete user: ${user.name}`)
                }
                disabled={isDisabled}
                tooltip={{
                  title: isDisabled ? 'User is inactive' : 'Delete user'
                }}
              />
              <IconButton
                icon="Eye"
                variant={ButtonVariant.Tertiary}
                size={ButtonSize.Medium}
                onClick={() => alert(`View details for: ${user.name}`)}
                tooltip={{ title: 'View details' }}
              />
            </div>
          );
        }
      }}
    />
  );
};

export const WithConditionalStickyMenu = {
  render: () => <StickyButtonDisabledTable />
};
