import type { Meta, StoryObj } from '@storybook/react';

import { TableTextCell } from './TableTextCell';

const meta = {
  title: 'Design System/Components/Table/TableTextCell',
  component: TableTextCell,
  tags: ['autodocs', 'Stable'],
  parameters: {
    layout: 'padded'
  },
  args: {
    children: 'Sample table cell text content',
    lineClamp: 2,
    dataAttributes: 'TableTextCell',
    classNames: ''
  },
  argTypes: {
    children: {
      control: 'text',
      description: 'The text content to display in the cell'
    },
    lineClamp: {
      control: { type: 'number', min: 1, max: 10 },
      description: 'Maximum number of lines to display before truncating'
    },
    dataAttributes: {
      control: 'text',
      description: 'Data attribute for testing and identification'
    },
    classNames: {
      control: 'text',
      description: 'Additional CSS classes to apply to the cell'
    }
  }
} satisfies Meta<typeof TableTextCell>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const SingleLine: Story = {
  args: {
    children: 'Single line text content',
    lineClamp: 1
  }
};

export const MultiLine: Story = {
  args: {
    children:
      'This is a longer text content that will span multiple lines to demonstrate how the TableTextCell component handles text wrapping and line clamping functionality.',
    lineClamp: 3
  }
};

export const LongText: Story = {
  args: {
    children:
      'This is a very long text content that should be truncated after the specified number of lines. The text will continue beyond what is visible and should show an ellipsis when truncated. This demonstrates the line clamping functionality of the TableTextCell component. This is a very long text content that should be truncated after the specified number of lines. The text will continue beyond what is visible and should show an ellipsis when truncated. This demonstrates the line clamping functionality of the TableTextCell component.'
  }
};
