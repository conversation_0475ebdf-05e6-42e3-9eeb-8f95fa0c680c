import { FC, PropsWithChildren } from 'react';

import { TableCell } from './TableCell';

import { Text } from '../Text';
import { DataAttributesProps } from '../types';

type TableTextCellProps = PropsWithChildren &
  DataAttributesProps & {
    classNames?: string;
    lineClamp?: number;
  };

export const TableTextCell: FC<TableTextCellProps> = ({
  children,
  classNames,
  lineClamp = 2,
  dataAttributes = 'TableTextCell'
}) => {
  return (
    <TableCell classNames={classNames} dataAttributes={dataAttributes}>
      <Text data-attributes={dataAttributes} lineClamp={lineClamp}>
        {children}
      </Text>
    </TableCell>
  );
};
