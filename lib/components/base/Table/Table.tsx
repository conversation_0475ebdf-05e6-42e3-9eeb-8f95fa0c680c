import React, {
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef
} from 'react';

import { cn } from '@/utils';

import { Icon } from '../Icon';
import { IllustrationMessage } from '../Illustration';
import { Loader } from '../Loader';

// Placeholder functions for mockColumns compatibility
export const getRequestedCaption = (_type: unknown): string => 'Requested';
export const formatToFullDate = (date: string): string =>
  new Date(date).toLocaleDateString();
export const getSubjectSubType = (_subject: unknown, _topic: unknown): string =>
  'Subject';

export const getToggleApprovalText = (): string => 'Toggle Approval';
export const getToggleText = (): string => 'Toggle';

// TooltipContent component to match expected interface
export const TooltipContent = ({
  children,
  ...props
}: {
  children: React.ReactNode;
  [key: string]: unknown;
}) => <span {...props}>{children}</span>;

export const formatToShortDate = (date: string): string =>
  new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  });

// Placeholder variables for mockColumns compatibility
export const isSubordinateReceived = false;
export const isFeedbackReceived = false;
export const isFeedbackGiven = false;
export const isFeedbackRequested = false;
export const isRequested = true;
export const topicColumnWidth = 200;
export const type = 'default';
export const actions = [
  { key: 'edit', label: 'Edit', onClick: () => {} },
  { key: 'delete', label: 'Delete', onClick: () => {} }
];

// Types for React Query (simplified)
export interface UseQueryResult<TData> {
  data?: TData;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
}

// Types
export interface PageModel<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface TableDataSourceState<_TId, TFilters = unknown> {
  pageNumber: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  filters?: TFilters;
}

export interface DataSourceState<TFilters, _TId> {
  pageNumber: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  filters?: TFilters;
}

export interface DataColumnProps<TItem, _TId = unknown> {
  key: string;
  title?: string;
  caption?: string;
  sortable?: boolean;
  width?: number;
  minWidth?: string | number;
  grow?: number;
  render?: (item: TItem, index: number) => ReactNode;
  className?: string;
  headerClassName?: string;
  canCopy?: boolean;
  fixed?: 'left' | 'right';
}

export interface StickyButtonProps<TItem> {
  render: (item: TItem, index: number) => ReactNode;
  width: number;
  minWidth?: string | number;
}

export interface DataColumnGroupProps {
  title: string;
  columns: string[];
  className?: string;
}

export interface DataRowProps<TItem, TId> {
  item: TItem;
  index: number;
  id: TId;
}

export interface DataRowOptions<_TItem, _TId> {
  className?: string;
  disabled?: boolean;
  folded?: boolean;
  customData?: Record<string, unknown>;
}

export type NonSortable<T> = T;

export type CustomRowExtraOptionsGetter<TItem> = (
  item: TItem,
  index: number
) => Record<string, unknown>;

export type TableLoadingStrategy = 'skeleton' | 'spinner' | 'overlay';

// Additional interfaces for DataTableCore
export interface IEditable<T> {
  value: T;
  onValueChange: (value: T) => void;
}

export interface IHasCX {
  cx?: string;
}

export interface DataSourceListProps {
  [key: string]: unknown;
}

export interface DataTableColumnsConfigOptions {
  [key: string]: unknown;
}

export interface VirtualListProps {
  onScroll?: (event: React.UIEvent<HTMLDivElement>) => void;
}

export interface DataTableState {
  // Add table state properties
  visibleColumnsIds?: string[];
  columnsConfig?: Record<string, unknown>;
}

export interface DataTableRowProps<TItem, TId> {
  item: TItem;
  id: TId;
  index: number;
  columns: DataColumnProps<TItem, TId>[];
}

export interface TableFiltersConfig<TFilter> {
  columnKey: string;
  filter: TFilter;
}

export interface DataTableSelectedCellData<TItem, TId, TFilter> {
  item: TItem;
  id: TId;
  columnKey: string;
  value: unknown;
  filter?: TFilter;
}

export interface ColumnsConfigurationModalProps<TItem, TId, _TFilter> {
  columns: DataColumnProps<TItem, TId>[];
  onClose: () => void;
  onApply: (config: unknown) => void;
}

export interface DataTableFocusManager<TId> {
  focusedId?: TId;
  setFocusedId: (id: TId) => void;
}

export interface DataTableCoreProps<TItem, TId, TFilter = unknown>
  extends IEditable<DataTableState>,
    IHasCX,
    DataSourceListProps,
    DataTableColumnsConfigOptions,
    Pick<VirtualListProps, 'onScroll'> {
  getRows?(): DataRowProps<TItem, TId>[];
  rows?: DataRowProps<TItem, TId>[];
  columnGroups?: DataColumnGroupProps[];
  columns: DataColumnProps<TItem, TId>[];
  renderRow?(props: DataTableRowProps<TItem, TId>): React.ReactNode;
  renderNoResultsBlock?(): React.ReactNode;
  showColumnsConfig?: boolean;
  filters?: TableFiltersConfig<TFilter>[];
  onCopy?: (
    copyFrom: DataTableSelectedCellData<TItem, TId, TFilter>,
    selectedCells: DataTableSelectedCellData<TItem, TId, TFilter>[]
  ) => void;
  renderColumnsConfigurationModal?: (
    props: ColumnsConfigurationModalProps<TItem, TId, TFilter>
  ) => React.ReactNode;
  dataTableFocusManager?: DataTableFocusManager<TId>;
  showFoldAll?: boolean;
}

export type TableProps<TItem, TId, TFilters = unknown> = {
  dataSourceState: TableDataSourceState<TId, TFilters>;
  setDataSourceState: Dispatch<
    SetStateAction<TableDataSourceState<TId, TFilters>>
  >;
  queryResult: UseQueryResult<PageModel<TItem>>;
  columns: DataColumnProps<TItem>[];
  columnGroups?: DataColumnGroupProps[];
  renderRowContent?: (item: TItem) => React.ReactNode;
  renderError?: () => ReactNode;
  renderNoResults?: () => ReactNode;
  onRowClick?: (id: TId, props: DataRowProps<TItem, TId>) => void;
  getRowOptions?: (
    item: TItem,
    index?: number
  ) => DataRowOptions<NonSortable<TItem>, TId>;
  isFoldedByDefault?(
    item: NonSortable<TItem>,
    state: DataSourceState<TFilters, TId>
  ): boolean;
  styles?: {
    minHeight?: number | string;
    maxWidth?: number | string;
  };
  dataAttributes?: string;
  classNames?: string;
  errorContainerClassName?: string;
  onVisibleRowsCountChanged?: (visibleRows: number | undefined) => void;
  loadingStrategy?: TableLoadingStrategy;
  stickyButton?: StickyButtonProps<TItem>;
} & (
  | {
      withCustomRow: true;
      rowDataAttributes?: string;
      getCustomRowExtraOptions?: CustomRowExtraOptionsGetter<TItem>;
    }
  | {
      withCustomRow?: never;
      rowDataAttributes?: never;
      getCustomRowExtraOptions?: never;
    }
);

// Table Header Component
const TableHeader = <TItem, TId, TFilters>({
  columns,
  dataSourceState,
  setDataSourceState,
  showColumnsConfig: _showColumnsConfig,
  onColumnsConfig: _onColumnsConfig,
  stickyButton,
  onHorizontalScroll,
  registerScrollElement
}: {
  columns: DataColumnProps<TItem, TId>[];
  columnGroups?: DataColumnGroupProps[];
  dataSourceState: TableDataSourceState<TId, TFilters>;
  setDataSourceState: Dispatch<
    SetStateAction<TableDataSourceState<TId, TFilters>>
  >;
  showColumnsConfig?: boolean;
  onColumnsConfig?: () => void;
  stickyButton?: StickyButtonProps<TItem>;
  onHorizontalScroll?: (scrollLeft: number, source: HTMLDivElement) => void;
  registerScrollElement?: (element: HTMLDivElement | null) => () => void;
}) => {
  const headerScrollRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const cleanup = registerScrollElement?.(headerScrollRef.current);
    return cleanup;
  }, [registerScrollElement]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (onHorizontalScroll && headerScrollRef.current) {
      onHorizontalScroll(e.currentTarget.scrollLeft, headerScrollRef.current);
    }
  };
  const handleSort = (columnKey: string) => {
    const column = columns.find((col) => col.key === columnKey);
    if (!column?.sortable) return;

    setDataSourceState((prev) => ({
      ...prev,
      sortBy: columnKey,
      sortDirection:
        prev.sortBy === columnKey && prev.sortDirection === 'asc'
          ? 'desc'
          : 'asc'
    }));
  };

  return (
    <div data-attributes="TableHeader">
      <div className="flex flex-row justify-between">
        <div
          ref={headerScrollRef}
          className={cn(
            'flex h-40 w-full overflow-x-auto bg-surface-grey_20 scrollbar-none',
            stickyButton ? 'rounded-md' : 'rounded-md'
          )}
          onScroll={handleScroll}
        >
          {columns.map((column) => (
            <div
              key={column.key}
              className={cn(
                'flex h-40 items-center whitespace-nowrap px-16 text-label-m1-regular text-text-heading',
                !column.width && !column.grow && 'flex-1',
                column.sortable && 'cursor-pointer hover:bg-surface-grey_10',
                column.headerClassName
              )}
              style={{
                ...(column.width
                  ? {
                      width: column.width,
                      minWidth: column.minWidth || column.width,
                      flexGrow: column.grow || 0
                    }
                  : {
                      minWidth: column.minWidth || '200px',
                      flexGrow: column.grow || 1
                    })
                // This feature can be applied if we want to hide empty header cell for sticky column
                // To be able to show column names over sticky column if table is scrollable by x axis
                //
                // ...(stickyButton &&
                //   index === columns.length - 1 && {
                //     width: column.width
                //       ? (column.width as number) + stickyButton.width
                //       : (column.minWidth as number) + stickyButton.width
                //   })
              }}
              onClick={() => handleSort(column.key)}
            >
              <div className="flex items-center">
                {column.caption || column.title}
                {column.sortable && (
                  <span className="ml-4 flex-shrink-0">
                    {dataSourceState.sortBy === column.key ? (
                      dataSourceState.sortDirection === 'asc' ? (
                        <Icon
                          name="Sort_up_light"
                          className="h-20 min-h-20 w-20 min-w-20 stroke-[0.5px]"
                        />
                      ) : (
                        <Icon
                          name="Sort_down_light"
                          className="h-20 min-h-20 w-20 min-w-20 stroke-[0.5px]"
                        />
                      )
                    ) : (
                      <Icon
                        name="Sort_list_light"
                        className="h-20 min-h-20 w-20 min-w-20 stroke-[0.5px]"
                      />
                    )}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
        {/* Sticky Button Column Header */}
        {stickyButton && (
          <div
            className="sticky right-0 z-10 flex items-center justify-center rounded-tr-xl bg-surface-grey_20 px-4"
            style={{
              boxShadow: '-16px 0px 16px 0px #00000014',
              width: stickyButton.width || '80px',
              minWidth: stickyButton.width || '80px',
              height: '40px'
            }}
          ></div>
        )}
      </div>
    </div>
  );
};

// Table Row Component
const TableRow = <TItem, TId>({
  item,
  index,
  columns,
  rowOptions,
  onRowClick,
  renderRowContent,
  withCustomRow,
  rowDataAttributes,
  getCustomRowExtraOptions,
  stickyButton,
  onHorizontalScroll,
  registerScrollElement
}: {
  item: TItem;
  index: number;
  columns: DataColumnProps<TItem, TId>[];
  rowOptions?: DataRowOptions<NonSortable<TItem>, TId>;
  onRowClick?: (id: TId, props: DataRowProps<TItem, TId>) => void;
  renderRowContent?: (item: TItem) => React.ReactNode;
  withCustomRow?: boolean;
  rowDataAttributes?: string;
  getCustomRowExtraOptions?: CustomRowExtraOptionsGetter<TItem>;
  stickyButton?: StickyButtonProps<TItem>;
  onHorizontalScroll?: (scrollLeft: number, source: HTMLDivElement) => void;
  registerScrollElement?: (element: HTMLDivElement | null) => () => void;
}) => {
  const tableRowRef = useRef<HTMLDivElement | null>(null);

  const handleScroll = useCallback(
    (e: Event) => {
      const target = e.target as HTMLDivElement;
      if (target && onHorizontalScroll) {
        onHorizontalScroll(target.scrollLeft, target);
      }
    },
    [onHorizontalScroll]
  );

  useEffect(() => {
    const cleanup = registerScrollElement?.(tableRowRef.current);
    const rowEl = tableRowRef.current;

    if (rowEl) {
      rowEl.addEventListener('scroll', handleScroll);
    }

    return () => {
      cleanup?.();
      if (rowEl) {
        rowEl.removeEventListener('scroll', handleScroll);
      }
    };
  }, [registerScrollElement, handleScroll]);
  const handleClick = () => {
    if (onRowClick) {
      const rowProps: DataRowProps<TItem, TId> = {
        item,
        index,
        id: (item as Record<string, unknown>).id as TId
      };
      onRowClick((item as Record<string, unknown>).id as TId, rowProps);
    }
  };

  const customOptions =
    withCustomRow && getCustomRowExtraOptions
      ? getCustomRowExtraOptions(item, index)
      : {};

  return (
    <div
      className={cn(
        'flex flex-row justify-between border-b border-divider-mid',
        rowOptions?.disabled && 'cursor-not-allowed opacity-50',
        rowOptions?.className
      )}
      style={{ height: '68px' }} // 67px content + 1px border
      onClick={!rowOptions?.disabled ? handleClick : undefined}
      data-attributes={rowDataAttributes}
      {...customOptions}
    >
      <div
        ref={tableRowRef}
        className="flex flex-1 overflow-x-auto scrollbar-none"
      >
        {renderRowContent ? (
          <div
            className="py-3 flex flex-1 items-center pl-4 pr-4"
            style={{ height: '67px' }}
          >
            {renderRowContent(item)}
          </div>
        ) : (
          columns.map((column, index) => (
            <div
              key={column.key}
              className={cn(
                'flex min-w-full items-center',
                index > 0 ? 'px-12' : 'px-8',
                'text-body-2-regular text-input-text-filled',
                column.grow && `flex-grow-${column.grow}`,
                !column.width && !column.grow && 'flex-1',
                column.className
              )}
              style={{
                height: '67px', // Cell height excluding border
                ...(column.width
                  ? {
                      width: column.width,
                      minWidth: column.minWidth || column.width,
                      flexGrow: column.grow || 0
                    }
                  : {
                      minWidth: column.minWidth || '120px',
                      flexGrow: column.grow || 1
                    })
              }}
            >
              {column.render
                ? column.render(item, index)
                : String((item as Record<string, unknown>)[column.key])}
            </div>
          ))
        )}
      </div>

      {/* Sticky Button Column */}
      {stickyButton && (
        <div
          className="sticky right-0 z-10 flex flex-shrink-0 items-center"
          style={{
            boxShadow: '-16px 0px 16px 0px #00000014',
            width: stickyButton.width || '92px',
            minWidth: stickyButton.minWidth || '48px',
            maxWidth: stickyButton.width || '92px',
            height: '67px'
          }}
        >
          {stickyButton.render(item, index)}
        </div>
      )}
    </div>
  );
};

// Main Table Component
export const Table = <TItem, TId, TFilters = unknown>({
  dataSourceState,
  setDataSourceState,
  queryResult,
  columns,
  columnGroups,
  renderRowContent,
  renderError,
  renderNoResults,
  onRowClick,
  getRowOptions,
  isFoldedByDefault,
  styles,
  dataAttributes,
  classNames,
  errorContainerClassName,
  onVisibleRowsCountChanged,
  withCustomRow,
  rowDataAttributes,
  getCustomRowExtraOptions,
  stickyButton
}: TableProps<TItem, TId, TFilters>) => {
  const { data: pageModel, isLoading, isError } = queryResult;
  const scrollRefs = useRef<Set<HTMLDivElement>>(new Set());
  const isScrolling = useRef(false);

  const handleHorizontalScroll = (
    scrollLeft: number,
    source: HTMLDivElement
  ) => {
    if (isScrolling.current) return;
    isScrolling.current = true;

    scrollRefs.current.forEach((element) => {
      if (element && element !== source) {
        element.scrollLeft = scrollLeft;
      }
    });

    // Use requestAnimationFrame to reset the flag
    requestAnimationFrame(() => {
      isScrolling.current = false;
    });
  };

  const registerScrollElement = (element: HTMLDivElement | null) => {
    if (element) {
      scrollRefs.current.add(element);
    }
    return () => {
      if (element) {
        scrollRefs.current.delete(element);
      }
    };
  };

  const visibleItems = useMemo(() => {
    if (!pageModel?.items) return [];
    return pageModel.items.filter((item) => {
      if (isFoldedByDefault) {
        return !isFoldedByDefault(
          item,
          dataSourceState as DataSourceState<TFilters, TId>
        );
      }
      return true;
    });
  }, [pageModel?.items, isFoldedByDefault, dataSourceState]);

  useEffect(() => {
    onVisibleRowsCountChanged?.(visibleItems.length);
  }, [visibleItems.length, onVisibleRowsCountChanged]);

  return (
    <div
      className={cn('rounded-xl bg-surface-grey_0 p-12', classNames)}
      style={{
        ...styles,
        maxWidth: '100%',
        overflow: 'hidden'
      }}
      data-attributes={dataAttributes}
    >
      <div className="relative overflow-x-auto">
        <div className="">
          <TableHeader
            columns={columns}
            columnGroups={columnGroups}
            dataSourceState={dataSourceState}
            setDataSourceState={setDataSourceState}
            stickyButton={stickyButton}
            onHorizontalScroll={handleHorizontalScroll}
            registerScrollElement={registerScrollElement}
          />

          <div>
            {isError && (
              <div
                className={cn(
                  'flex min-h-[566px] w-full items-center justify-center',
                  errorContainerClassName
                )}
                data-attributes="TableError"
              >
                {renderError ? (
                  renderError()
                ) : (
                  <IllustrationMessage
                    illustrationVariant={'Error'}
                    title="Sorry, an error has occurred"
                    description="We can’t seem to find the page you are looking for"
                  />
                )}
              </div>
            )}
            {isLoading ? (
              <Loader className="flex min-h-[566px] w-full items-center justify-center" />
            ) : !visibleItems.length && !isError ? (
              <div className="flex min-h-[566px] w-full items-center justify-center">
                {renderNoResults ? (
                  renderNoResults()
                ) : (
                  <IllustrationMessage
                    illustrationVariant="NothingFound"
                    description={
                      <p>
                        You are all caught up!
                        <br />
                        No new items here!
                      </p>
                    }
                    title="Sorry, no results found"
                  />
                )}
              </div>
            ) : (
              visibleItems.map((item, index) => {
                const itemId = (item as Record<string, unknown>).id as TId;
                const rowOptions = getRowOptions?.(item, index);

                return (
                  <TableRow
                    key={`${itemId}-${index}`}
                    item={item}
                    index={index}
                    columns={columns}
                    rowOptions={rowOptions}
                    onRowClick={onRowClick}
                    renderRowContent={renderRowContent}
                    withCustomRow={withCustomRow}
                    rowDataAttributes={rowDataAttributes}
                    getCustomRowExtraOptions={getCustomRowExtraOptions}
                    stickyButton={stickyButton}
                    onHorizontalScroll={handleHorizontalScroll}
                    registerScrollElement={registerScrollElement}
                  />
                );
              })
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
