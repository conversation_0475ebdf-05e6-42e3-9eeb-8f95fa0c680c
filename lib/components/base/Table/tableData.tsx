/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  type DataColumnProps,
  type PageModel,
  type UseQueryResult
} from './Table';
import { TableCell } from './TableCell';
import { TableTextCell } from './TableTextCell';
import { TableUserInfoCell } from './TableUserInfoCell';

import { StarRating, StarRatingSize } from '../StarRating';

// Mock User interface for demonstration - Extended to match mockColumns expectations
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status:
    | 'active'
    | 'inactive'
    | 'pending'
    | {
        id: string;
        name: string;
      };
  joinDate: string;
  department: string;
  // Extended properties for mockColumns compatibility
  employee?: {
    email?: string;
    fullNameEnglish?: string;
    companyName?: string;
    positionName?: string;
  };
  tags?: string[];
  date?: string;
  topic?: string;
  subject?: {
    title?: string;
  };
  rating?: number;
  feedbackText?: string;
  requestDetails?: string;
}

// Mock data - Extended to match mockColumns expectations
export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    status: { id: 'active', name: 'Active' },
    joinDate: '2023-01-15',
    department: 'Engineering',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'John Doe',
      companyName: 'TechCorp Inc.',
      positionName: 'Senior Engineer'
    },
    tags: ['Senior', 'Lead'],
    date: '2023-01-15',
    topic: 'Performance Review',
    subject: { title: 'Q1 Performance Evaluation' },
    rating: 4.5,
    feedbackText: 'Excellent performance this quarter.',
    requestDetails: 'Request for promotion consideration.'
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'User',
    status: { id: 'active', name: 'Active' },
    joinDate: '2023-02-20',
    department: 'Marketing',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Jane Smith',
      companyName: 'TechCorp Inc.',
      positionName: 'Marketing Manager'
    },
    tags: ['Manager', 'Creative'],
    date: '2023-02-20',
    topic: 'Campaign Review',
    subject: { title: 'Spring Campaign Analysis' },
    rating: 4.2,
    feedbackText: 'Great work on the recent campaign.',
    requestDetails: 'Request for budget increase.'
  },
  {
    id: '3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: 'Moderator',
    status: { id: 'inactive', name: 'Inactive' },
    joinDate: '2023-03-10',
    department: 'Sales',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Bob Johnson',
      companyName: 'TechCorp Inc.',
      positionName: 'Sales Representative'
    },
    tags: ['Senior', 'B2B'],
    date: '2023-03-10',
    topic: 'Sales Review',
    subject: { title: 'Q1 Sales Performance' },
    rating: 3.8,
    feedbackText: 'Good sales numbers but needs improvement.',
    requestDetails: 'Request for additional training.'
  },
  {
    id: '4',
    name: 'Alice Brown',
    email: '<EMAIL>',
    role: 'User',
    status: { id: 'pending', name: 'Pending' },
    joinDate: '2023-04-05',
    department: 'HR',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Alice Brown',
      companyName: 'TechCorp Inc.',
      positionName: 'HR Specialist'
    },
    tags: ['New', 'Onboarding'],
    date: '2023-04-05',
    topic: 'Onboarding Review',
    subject: { title: 'New Employee Feedback' },
    rating: 4.0,
    feedbackText: 'Adapting well to the company culture.',
    requestDetails: 'Request for mentorship program.'
  },
  {
    id: '5',
    name: 'Charlie Wilson',
    email: '<EMAIL>',
    role: 'Admin',
    status: { id: 'active', name: 'Active' },
    joinDate: '2023-05-12',
    department: 'Engineering',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Charlie Wilson',
      companyName: 'TechCorp Inc.',
      positionName: 'DevOps Engineer'
    },
    tags: ['DevOps', 'Infrastructure'],
    date: '2023-05-12',
    topic: 'Infrastructure Review',
    subject: { title: 'Cloud Migration Progress' },
    rating: 4.7,
    feedbackText: 'Outstanding work on infrastructure.',
    requestDetails: 'Request for cloud certification budget.'
  },
  {
    id: '6',
    name: 'Diana Davis',
    email: '<EMAIL>',
    role: 'User',
    status: { id: 'active', name: 'Active' },
    joinDate: '2023-06-18',
    department: 'Design',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Diana Davis',
      companyName: 'TechCorp Inc.',
      positionName: 'UX Designer'
    },
    tags: ['Inactive', 'Assigned'],
    date: '2023-06-18',
    topic: 'Design Review',
    subject: { title: 'User Experience Improvements' },
    rating: 4.3,
    feedbackText: 'Innovative design solutions.',
    requestDetails: 'Request for design tool licenses.'
  }
];

// Mock columns configuration for user table (matches original workingColumns structure)
export const mockColumns: DataColumnProps<User>[] = [
  {
    key: 'name',
    caption: 'Full Name',
    width: 320,
    minWidth: 320,
    sortable: true,
    render: (user) => (
      <TableUserInfoCell
        email={user.email}
        fullName={user.name}
        company={user.employee?.companyName || user.department}
        position={user.employee?.positionName || user.role}
        // tags={user.tags}
      />
    )
  },
  {
    key: 'date',
    caption: 'Date',
    width: 120,
    minWidth: 120,
    sortable: true,
    render: (user) => (
      <TableTextCell>
        {user.date ? new Date(user.date).toLocaleDateString() : ''}
      </TableTextCell>
    )
  },
  {
    key: 'topic',
    caption: 'Topic',
    width: 200,
    minWidth: 200,
    sortable: true,
    render: (user) => (
      <TableTextCell>{user.topic || user.subject?.title || ''}</TableTextCell>
    )
  },
  {
    key: 'rating',
    caption: 'Rating',
    width: 120,
    minWidth: 120,
    sortable: true,
    render: (user) => (
      <TableCell>
        {user.rating ? (
          <StarRating
            value={user.rating}
            disabled
            size={StarRatingSize.Small}
          />
        ) : null}
      </TableCell>
    )
  },
  {
    key: 'preview',
    caption: 'Preview',
    minWidth: 200,
    width: 300,
    grow: 1,
    sortable: true,
    render: (user) => (
      <TableTextCell>
        {user.feedbackText || user.requestDetails || ''}
      </TableTextCell>
    )
  }
];

export const mockTeamObjectives: any[] = [
  {
    employeeId: '99905158',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'David Johnson',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 100,
      skillsCovered: 10
    }
  },
  {
    employeeId: '99905159',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'Khalid Al Mansoori',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 120,
      skillsCovered: 10
    }
  },
  {
    employeeId: '99905160',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'Latifa Al Suwaidi',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 100,
      skillsCovered: 10
    }
  },
  {
    employeeId: '99905161',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'Olivia Martinez',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 100,
      skillsCovered: 10
    }
  },
  {
    employeeId: '99905157',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'Sophia Lee',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 100,
      skillsCovered: 10
    }
  }
];

export const mockTeamObjectivesColumns: DataColumnProps<any>[] = [
  {
    key: 'EmployeeName',
    caption: 'Employee Name',
    width: 720,
    minWidth: 180,
    sortable: true,
    grow: 1,
    render: (data) => (
      <TableUserInfoCell
        size="Medium"
        email={data.email}
        fullName={data.fullNameEnglish}
        company={data.companyName}
        position={data.jobTitle}
      />
    )
  },
  {
    key: 'Completedobjectives',
    caption: 'Completed objectives',
    width: 180,
    minWidth: 180,
    sortable: false,
    render: (data) => (
      <TableCell>
        {data.objectiveInfo.completedObjectivesCount} of{' '}
        {data.objectiveInfo.totalObjectivesCount}
      </TableCell>
    )
  },
  {
    key: 'TotalWeightage',
    caption: 'Total Weightage',
    width: 180,
    minWidth: 180,
    sortable: false,
    render: (data) => (
      <TableCell>{data.objectiveInfo.totalWeightage}%</TableCell>
    )
  },
  {
    key: 'SkillsCovered',
    caption: 'Skills Covered',
    width: 180,
    minWidth: 180,
    sortable: false,
    render: (data) => <TableCell>{data.objectiveInfo.skillsCovered}%</TableCell>
  }
];

export const createMockQueryResult = (
  data: any[],
  loading = false,
  error = false
): UseQueryResult<PageModel<any>> => ({
  data: {
    items: data,
    totalCount: data.length,
    pageNumber: 1,
    pageSize: 1,
    hasNextPage: false,
    hasPreviousPage: false
  },
  isLoading: loading,
  isError: error,
  error: error ? ({} as Error) : null
});
