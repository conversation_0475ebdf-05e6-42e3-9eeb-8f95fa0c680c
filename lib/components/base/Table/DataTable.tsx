import { useMemo } from 'react';

import {
  HistoryAdaptedRouter,
  IHistory4,
  useUuiServices,
  UuiContext
} from '@epam/uui-core';
import { createBrowserHistory } from 'history';

import { PageModel, Table, TableProps, UseQueryResult } from './Table';

import { DropdownOption } from '../Dropdown';
import { DropdownSingleSelect } from '../DropdownSingleSelect';
import { Pagination } from '../Pagination';

export const DEFAULT_PAGE_SIZE_OPTIONS: DropdownOption[] = [
  { id: 10, title: '10' },
  { id: 25, title: '25' },
  { id: 50, title: '50' }
];

export interface DataTableProps<TItem, TId, TFilters = unknown>
  extends Omit<TableProps<TItem, TId, TFilters>, 'queryResult'> {
  queryResult: UseQueryResult<PageModel<TItem>>;

  showPagination?: boolean;
  showPageSizeSelector?: boolean;
  pageSizeOptions?: DropdownOption[];

  containerClassName?: string;
  paginationClassName?: string;
}

export const DataTable = <TItem, TId, TFilters = unknown>({
  queryResult,
  dataSourceState,
  setDataSourceState,
  showPagination = true,
  showPageSizeSelector = true,
  pageSizeOptions = DEFAULT_PAGE_SIZE_OPTIONS,
  containerClassName,
  paginationClassName,
  withCustomRow,
  rowDataAttributes,
  getCustomRowExtraOptions,
  ...restTableProps
}: DataTableProps<TItem, TId, TFilters>) => {
  const history = createBrowserHistory();
  const router = new HistoryAdaptedRouter(history as unknown as IHistory4);
  const { services } = useUuiServices({ router });

  const { data: pageModel } = queryResult;

  const totalPages = useMemo(() => {
    if (!pageModel?.totalCount || !dataSourceState.pageSize) return 1;
    return Math.ceil(pageModel.totalCount / dataSourceState.pageSize);
  }, [pageModel?.totalCount, dataSourceState.pageSize]);

  const handlePageChange = (newPage: number) => {
    setDataSourceState((prev) => ({
      ...prev,
      pageNumber: newPage
    }));
  };

  const handlePageSizeChange = (option?: DropdownOption) => {
    if (option) {
      setDataSourceState((prev) => ({
        ...prev,
        pageSize: Number(option.id),
        pageNumber: 1
      }));
    }
  };

  const currentPageSizeOption = useMemo(() => {
    return (
      pageSizeOptions.find(
        (option) => Number(option.id) === dataSourceState.pageSize
      ) || pageSizeOptions[0]
    );
  }, [pageSizeOptions, dataSourceState.pageSize]);

  const tableProps = withCustomRow
    ? {
        queryResult,
        dataSourceState,
        setDataSourceState,
        withCustomRow: true as const,
        rowDataAttributes,
        getCustomRowExtraOptions,
        ...restTableProps
      }
    : {
        queryResult,
        dataSourceState,
        setDataSourceState,
        ...restTableProps
      };

  return (
    <UuiContext.Provider value={services}>
      <div className={containerClassName} data-attributes="DataTable">
        <Table<TItem, TId, TFilters> {...tableProps} />

        {/* Pagination and page size selector in a single row */}
        {(showPagination || showPageSizeSelector) && (
          <div className="flex items-center justify-between">
            {/* Left spacer to help center pagination */}
            <div className="flex-1">{/* Empty div for spacing */}</div>

            {/* Center - Pagination */}
            <div className="flex justify-center">
              {showPagination && totalPages > 1 && (
                <Pagination
                  className={paginationClassName}
                  totalPages={totalPages}
                  currentPage={dataSourceState.pageNumber}
                  onChange={handlePageChange}
                />
              )}
            </div>

            {/* Right - Page size selector */}
            <div className="flex flex-1 justify-end">
              {showPageSizeSelector && (
                <DropdownSingleSelect
                  value={currentPageSizeOption}
                  onChange={handlePageSizeChange}
                  options={pageSizeOptions}
                  size="Small"
                  className="w-72"
                  label=""
                  showClear={false}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </UuiContext.Provider>
  );
};
