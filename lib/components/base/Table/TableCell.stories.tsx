import type { Meta, StoryObj } from '@storybook/react';

import { TableCell } from './TableCell';

const meta = {
  title: 'Design System/Components/Table/TableCell',
  component: TableCell,
  tags: ['autodocs', 'Stable'],
  parameters: {
    layout: 'padded'
  },
  args: {
    children: 'Sample table cell content',
    dataAttributes: 'TableCell'
  },
  argTypes: {
    children: {
      control: 'text',
      description: 'Content to display inside the table cell'
    },
    classNames: {
      control: 'text',
      description: 'Additional CSS classes to apply to the table cell'
    },
    dataAttributes: {
      control: 'text',
      description: 'Data attributes for testing and identification'
    }
  }
} satisfies Meta<typeof TableCell>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};
