import { forwardRef, useCallback, useMemo, useRef } from 'react';

import { cn, generateId } from '@/utils';

import { DropdownMultiselectListProps } from './DropdownMultiselectList';

import { Drawer } from '../Drawer';
import {
  DROPDOWN_SEARCH_MAX_LENGTH,
  useDropdownContext,
  useDropdownScrollParent
} from '../Dropdown';
import InfiniteScroll from '../Dropdown/InfiniteScrollWrapper';
import { Input } from '../Input';

export const DropdownMultiselectListMobile = forwardRef<
  HTMLUListElement,
  DropdownMultiselectListProps
>(
  (
    {
      className,
      children,
      input: inputProps,
      hasMore,
      showSearch,
      searchValue,
      onLoadMore,
      onSearchChange,
      onSearchClear
    },
    ref
  ) => {
    const { isOpen, title, setIsOpen, refs } = useDropdownContext();
    const searchRef = useRef<HTMLInputElement>(null);

    const id = useMemo(() => generateId('DropdownMultiselectListMobile'), []);
    const getScrollParent = useDropdownScrollParent(`#${id}`);

    const handleCloseDrawer = useCallback(() => {
      setIsOpen(false);
    }, [setIsOpen]);

    const handleMainInputFocus = useCallback(() => {
      if (!showSearch) {
        return;
      }

      if (searchRef.current) {
        searchRef.current.focus();
      }
    }, [showSearch]);

    if (!isOpen) {
      return null;
    }

    const handleLoadMore = () => {
      onLoadMore?.();
    };

    return (
      <Drawer
        ref={refs.setFloating}
        isOpen={isOpen}
        header={title}
        fullHeight={true}
        onClose={handleCloseDrawer}
        footer={undefined}
        className="overflow-hidden px-0"
        data-attributes="DropdownMultiselectList"
      >
        <Input
          containerClassName="w-full mb-16 px-20"
          {...inputProps}
          onFocus={handleMainInputFocus}
          onRightIconClick={handleCloseDrawer}
          readOnly
        />
        {showSearch && (
          <Input
            ref={searchRef}
            containerClassName="mb-4 px-20"
            showLabel={false}
            dataAttributes="DropdownMultiselectSearch"
            size="Medium"
            placeholder="Search"
            leftIcon="Search_light"
            value={searchValue}
            showClear={true}
            autoFocus
            maxLength={DROPDOWN_SEARCH_MAX_LENGTH}
            onChange={onSearchChange}
            onClear={onSearchClear}
          />
        )}
        <ul
          ref={ref}
          id={id}
          className={cn(
            'flex w-full flex-col justify-start overflow-y-auto px-20',
            className
          )}
        >
          <InfiniteScroll
            pageStart={0}
            loadMore={handleLoadMore}
            hasMore={hasMore}
            loader={<div className="flex flex-auto" key={0}></div>}
            useWindow={false}
            getScrollParent={getScrollParent}
          >
            {children}
          </InfiniteScroll>
        </ul>
      </Drawer>
    );
  }
);
