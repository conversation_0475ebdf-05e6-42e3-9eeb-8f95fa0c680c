import {
  FC,
  KeyboardEvent,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';

import { Placement } from '@floating-ui/react';

import { useIsMobile } from '@/hooks';

import { DropdownMultiselectList } from './DropdownMultiselectList';
import { DropdownMultiselectListItem } from './DropdownMultiselectListItem';

import { Badge, BadgeList, TruncatorProps } from '../Badge';
import { Button } from '../Button';
import {
  Dropdown,
  dropdownHighlightMatch,
  DropdownInfiniteScroll,
  DropdownNoResultsFound,
  DropdownOption,
  DropdownSize,
  DropdownVariant
} from '../Dropdown';
import { Input, InputVariant } from '../Input';
import { Link } from '../Link';
import { Toggle } from '../Toggle';
import { DataAttributesProps } from '../types';

export type DropdownMultiselectProps = DataAttributesProps &
  DropdownInfiniteScroll & {
    // Container
    className?: string;

    // Inputs
    name?: string;
    label?: string;
    supportingText?: string;
    size?: DropdownSize;
    inputVariant?: InputVariant;
    required?: boolean;
    disabled?: boolean;
    readOnly?: boolean;
    placeholder?: string;
    value?: DropdownOption[];
    showClear?: boolean;
    badges?: ReactNode;

    // List
    listClassName?: string;
    options: DropdownOption[];
    placement?: Placement;
    fallbackPlacements?: Placement[];
    showSearch?: boolean;
    truncatorProps?: TruncatorProps;
    drawerHeaderTitle?: string;

    // ListItem
    variant?: DropdownVariant;
    onChange?: (options: DropdownOption[]) => void;
    onSearch?: (query: string) => void;
  };

export const DropdownMultiselect: FC<DropdownMultiselectProps> = ({
  size = DropdownSize.Large,
  variant = DropdownVariant.Primary,
  value,
  options,
  className,
  disabled = false,
  readOnly = false,
  label = 'Select',
  supportingText,
  placeholder = 'Select one or more item(s)',
  required = false,
  showClear,
  inputVariant,
  dataAttributes = 'DropdownMultiselect',
  drawerHeaderTitle = 'Select',
  placement = 'bottom',
  fallbackPlacements = ['bottom', 'top'],
  hasMore = false,
  showSearch = true,
  truncatorProps,
  onChange,
  onLoadMore,
  onSearch
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [showSelected, setShowSelected] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState<DropdownOption[]>([]);
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const listRef = useRef<HTMLUListElement>(null);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (value) {
      setSelectedOptions(value);
    } else {
      setSelectedOptions([]);
    }
  }, [value, options]);

  useEffect(() => {
    if (!isOpen) {
      setSearch('');
      onSearch?.('');
      setShowSelected(false);
    }
  }, [isOpen, onSearch]);

  const filteredOptions = useMemo(() => {
    if (showSelected) {
      if (onSearch && search) {
        const selectedIds = selectedOptions.map((option) => option.id);
        return options.filter((option) => selectedIds.includes(option.id));
      }

      return selectedOptions.filter((option) => {
        const regex = new RegExp(`(${search})`, 'i');
        return regex.test(option.title) || regex.test(option.description || '');
      });
    } else {
      return options.filter((option) => {
        if (onSearch) {
          return true;
        }

        const regex = new RegExp(`(${search})`, 'i');
        return regex.test(option.title) || regex.test(option.description || '');
      });
    }
  }, [options, selectedOptions, search, showSelected, onSearch]);

  const showNoResultsFound = filteredOptions.length === 0 && search;

  const handleSelect = useCallback(
    (option: DropdownOption) => {
      if (disabled || readOnly || option.disabled) {
        return;
      }

      const selectOptions = (list: DropdownOption[], item: DropdownOption) => {
        const isSelected = list.find((listItem) => listItem.id === item.id);
        if (isSelected) {
          return list.filter((listItem) => listItem.id !== item.id);
        } else {
          return [...list, item];
        }
      };

      const newSelectedOptions = selectOptions(selectedOptions, option);

      setSelectedOptions(newSelectedOptions);
      setFocusedIndex(-1);
      onChange?.(newSelectedOptions);

      if (newSelectedOptions.length === 0) {
        setShowSelected(false);
      }
    },
    [disabled, readOnly, selectedOptions, onChange]
  );

  const handleClear = useCallback(() => {
    setSelectedOptions([]);
    setFocusedIndex(-1);
    setShowSelected(false);
    onChange?.([]);
  }, [onChange]);

  const handleInputKeyDown = useCallback(
    (event: KeyboardEvent<HTMLInputElement>) => {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          if (!isOpen) {
            setIsOpen(true);
            setFocusedIndex(0);
          } else {
            setFocusedIndex((prevIndex) => {
              const nextIndex = prevIndex + 1;
              return nextIndex >= filteredOptions.length ? 0 : nextIndex;
            });
          }
          break;
        case 'ArrowUp':
          event.preventDefault();
          if (isOpen) {
            setFocusedIndex((prevIndex) => {
              const nextIndex = prevIndex - 1;
              return nextIndex < 0 ? filteredOptions.length - 1 : nextIndex;
            });
          }
          break;
        case 'Enter':
          event.preventDefault();
          if (
            isOpen &&
            focusedIndex >= 0 &&
            focusedIndex < filteredOptions.length
          ) {
            handleSelect(filteredOptions[focusedIndex]);
          } else if (!isOpen) {
            setIsOpen(true);
            setFocusedIndex(0);
          }
          break;
        case 'Escape':
          event.preventDefault();
          setIsOpen(false);
          setFocusedIndex(-1);
          break;
        case 'Tab':
          setIsOpen(false);
          setFocusedIndex(-1);
          break;
      }
    },
    [isOpen, filteredOptions, focusedIndex, handleSelect]
  );

  // Scroll focused item into view
  const scrollFocusedItemIntoView = useCallback(() => {
    if (listRef.current && focusedIndex >= 0) {
      const items = listRef.current.getElementsByTagName('li');
      if (items[focusedIndex]) {
        items[focusedIndex].scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [focusedIndex]);

  // Update scroll position when focused index changes
  useEffect(() => {
    if (isOpen) {
      scrollFocusedItemIntoView();
    }
  }, [isOpen, scrollFocusedItemIntoView]);

  const handleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (event.target.value) {
        setIsOpen(true);
      }
    },
    []
  );

  const handleApply = useCallback(() => {
    setIsOpen(false);
    setSearch('');
    onSearch?.('');
    onChange?.(selectedOptions);
  }, [selectedOptions, onChange, onSearch]);

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      // Limit input to 1000 characters
      if (event.target.value.length < 1000) {
        setSearch(event.target.value);
        onSearch?.(event.target.value);
      }
    },
    [onSearch]
  );

  const handleSearchClear = useCallback(() => {
    setSearch('');
    onSearch?.('');
  }, [onSearch]);

  const handleRemoveBadge = useCallback(
    (option: DropdownOption) => {
      handleSelect(option);
    },
    [handleSelect]
  );

  let badges = undefined;
  let inputContentClassName = '';

  if (selectedOptions.length > 0) {
    badges = (
      <BadgeList
        className="flex h-40 max-h-40 w-full flex-auto flex-row gap-8"
        truncatorProps={{
          tooltipMode: isMobile ? 'off' : 'always',
          ...truncatorProps
        }}
      >
        {selectedOptions.map((option, index) => (
          <Badge
            tooltipMode={isMobile ? 'off' : 'truncated'}
            className="text-left"
            key={index}
            label={option.title}
            avatar={isMobile ? undefined : option.avatar}
            onRemove={(e) => {
              e.stopPropagation();
              handleRemoveBadge(option);
            }}
          />
        ))}
      </BadgeList>
    );
    inputContentClassName = 'max-w-0';
  }

  return (
    <Dropdown
      dataAttributes={dataAttributes}
      className={className}
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      title={drawerHeaderTitle}
      fallbackPlacements={fallbackPlacements}
      placement={placement}
      renderTrigger={({ getTriggerProps }) => (
        <div {...getTriggerProps()} className="w-full">
          <Input
            supportingText={supportingText}
            dataAttributes="DropdownMultiSelectTrigger"
            label={label}
            size={size}
            placeholder={placeholder}
            rightIcon={isOpen ? 'Expand_up_light' : 'Expand_down_light'}
            value={selectedOptions.length > 0 ? ' ' : ''}
            inputContentClassName={inputContentClassName}
            variant={inputVariant}
            badges={badges}
            showClear={showClear}
            onChange={handleChange}
            onClear={handleClear}
            onKeyDown={handleInputKeyDown}
            onRightIconClick={() =>
              !disabled && !readOnly && setIsOpen((prev) => !prev)
            }
            disabled={disabled}
            readOnly={true}
            required={required}
            aria-expanded={isOpen}
            aria-haspopup="listbox"
            role="combobox"
            aria-activedescendant={
              focusedIndex >= 0
                ? `option-${filteredOptions[focusedIndex]?.id}`
                : undefined
            }
          />
        </div>
      )}
      renderDropdown={() => (
        <DropdownMultiselectList
          ref={listRef}
          className="mb-48"
          input={{
            label,
            placeholder,
            rightIcon: isOpen ? 'Expand_up_light' : 'Expand_down_light',
            value: selectedOptions.length > 0 ? ' ' : '',
            inputContentClassName,
            variant: inputVariant,
            badges,
            showClear,
            required,
            onChange: handleSearchChange,
            onClear: handleClear,
            onKeyDown: handleInputKeyDown
          }}
          hasMore={hasMore && !showSelected}
          showSearch={showSearch}
          searchValue={search}
          showSelected={showSelected}
          onSearchChange={handleSearchChange}
          onSearchClear={handleSearchClear}
          onLoadMore={onLoadMore}
        >
          {showNoResultsFound && (
            <DropdownNoResultsFound className="flex h-full min-h-[334px] w-full flex-auto flex-col justify-center md:!min-h-[248px]" />
          )}
          {filteredOptions.map((option, index) => (
            <DropdownMultiselectListItem
              key={option.id}
              option={option}
              variant={variant}
              title={dropdownHighlightMatch(option.title, search)}
              description={
                option.description &&
                dropdownHighlightMatch(option.description, search)
              }
              selected={
                !!selectedOptions.find((listItem) => listItem.id === option.id)
              }
              disabled={option.disabled}
              tooltip={option.tooltip}
              onClick={handleSelect}
              data-id={`option-${option.id}`}
              data-focused={index === focusedIndex}
            />
          ))}
          <div className="fixed bottom-0 left-0 right-0 flex min-h-[104px] w-full flex-auto items-center justify-end rounded-xl bg-surface-grey_0 px-20 pb-48 pt-16 shadow-footer md:!min-h-[42px] md:!rounded-b-xl md:!bg-dropdown-background md:!px-8 md:!pb-8 md:!pl-0 md:!pt-0 md:!shadow-none">
            <div className="flex flex-auto items-center md:!ml-8 md:!border-t md:!border-solid md:!border-divider-mid md:!pl-8 md:!pt-8">
              <span>
                <Toggle
                  label={`Show selected (${selectedOptions.length})`}
                  labelAlignment="End"
                  checked={showSelected}
                  disabled={selectedOptions.length === 0}
                  onChange={setShowSelected}
                />
              </span>
              <span className="flex flex-auto"></span>
              <Link
                size="Medium"
                disabled={selectedOptions.length === 0}
                onClick={handleClear}
              >
                Clear All
              </Link>
              <Button
                size="Small"
                className="ml-12 min-h-[34px] min-w-[80px]"
                onClick={handleApply}
              >
                Apply
              </Button>
            </div>
          </div>
        </DropdownMultiselectList>
      )}
    />
  );
};
