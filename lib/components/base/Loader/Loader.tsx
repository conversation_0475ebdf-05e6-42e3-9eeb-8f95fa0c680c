import { FC, ReactNode, useLayoutEffect, useRef } from 'react';

import { DataAttributesProps } from '../types';

import { cn } from '../../../utils';

export const DEFAULT_DIAMETER = 149;

const DEFAULT_STROKE_WIDTH = 14;
const DEFAULT_GRADIENT_X_ONE = 38.7939;
const DEFAULT_GRADIENT_Y_ONE = 24.9486;
const DEFAULT_GRADIENT_X_TWO = 99.8488;
const DEFAULT_GRADIENT_Y_TWO = 165.162;

const DEFAULT_DIAMETER_PERCENT = DEFAULT_DIAMETER / 100;
export const STROKE_WIDTH_PERCENTAGE =
  DEFAULT_STROKE_WIDTH / DEFAULT_DIAMETER_PERCENT;
export const GRADIENT_X_ONE_PERCENTAGE =
  DEFAULT_GRADIENT_X_ONE / DEFAULT_DIAMETER_PERCENT;
export const GRADIENT_Y_ONE_PERCENTAGE =
  DEFAULT_GRADIENT_Y_ONE / DEFAULT_DIAMETER_PERCENT;
export const GRADIENT_X_TWO_PERCENTAGE =
  DEFAULT_GRADIENT_X_TWO / DEFAULT_DIAMETER_PERCENT;
export const GRADIENT_Y_TWO_PERCENTAGE =
  DEFAULT_GRADIENT_Y_TWO / DEFAULT_DIAMETER_PERCENT;

export const calculateCircleAttributes = (diameter: number) => {
  const diameterPercentValue = diameter / 100;

  const circumference = 2 * Math.PI * (diameter / 2);
  const strokeWidth = diameterPercentValue * STROKE_WIDTH_PERCENTAGE;
  const gradientXOne = diameterPercentValue * GRADIENT_X_ONE_PERCENTAGE;
  const gradientYOne = diameterPercentValue * GRADIENT_Y_ONE_PERCENTAGE;
  const gradientXTwo = diameterPercentValue * GRADIENT_X_TWO_PERCENTAGE;
  const gradientYTwo = diameterPercentValue * GRADIENT_Y_TWO_PERCENTAGE;
  const radius = Math.floor(diameter / 2 - 1) - Math.floor(strokeWidth / 2);

  return {
    circumference,
    strokeWidth,
    gradientXOne,
    gradientYOne,
    gradientXTwo,
    gradientYTwo,
    radius
  };
};

const ROTATION_START_OFFSET = 0.5;
const ROTATION_END_OFFSET = 0.8;

const invertDegreeValue = (degree: number) => 360 - degree;

export const getCircleKeyframes = (circumference: number): Keyframe[] => {
  const circumferenceDegree = circumference / 360;

  const getDashoffsetValue = (value: number) =>
    circumferenceDegree * invertDegreeValue(value);

  const getDasharrayValue = (value: number) => {
    const dashArrayGap = getDashoffsetValue(value);

    return `${circumference - dashArrayGap} ${dashArrayGap}`;
  };

  return [
    {
      strokeDashoffset: getDashoffsetValue(45),
      strokeDasharray: getDasharrayValue(70)
    },
    {
      strokeDashoffset: getDashoffsetValue(10),
      strokeDasharray: getDasharrayValue(40),
      easing: 'ease-out',
      offset: 0.2
    },
    {
      strokeDashoffset: getDashoffsetValue(30),
      strokeDasharray: getDasharrayValue(110),
      offset: ROTATION_START_OFFSET
    },
    {
      strokeDashoffset: getDashoffsetValue(110),
      strokeDasharray: getDasharrayValue(40)
    },
    {
      strokeDashoffset: getDashoffsetValue(90),
      strokeDasharray: getDasharrayValue(40),
      offset: ROTATION_END_OFFSET
    },
    {
      strokeDashoffset: getDashoffsetValue(45),
      strokeDasharray: getDasharrayValue(70)
    }
  ];
};

export const svgKeyframes: Keyframe[] = [
  {
    transform: 'rotate(0deg)'
  },
  {
    transform: 'rotate(0deg)'
  },
  {
    transform: 'rotate(360deg)',
    offset: ROTATION_START_OFFSET,
    easing: 'ease-in-out'
  },
  {
    transform: 'rotate(360deg)',
    offset: ROTATION_END_OFFSET
  },
  {
    transform: 'rotate(360deg)'
  }
];

export const animationOptions = {
  duration: 1600,
  iterations: Infinity
};

export const LoaderSize = {
  ExtraSmall: 'ExtraSmall',
  Small: 'Small',
  Medium: 'Medium'
} as const;

export type LoaderSize = (typeof LoaderSize)[keyof typeof LoaderSize];

export type LoaderProps = DataAttributesProps & {
  className?: string;
  size?: LoaderSize;
  description?: ReactNode;
  descriptionClassName?: string;
};

export const Loader: FC<LoaderProps> = ({
  className,
  size = LoaderSize.Medium,
  dataAttributes = 'Loader',
  description,
  descriptionClassName
}) => {
  const diameter =
    size === LoaderSize.ExtraSmall ? 24 : size === LoaderSize.Small ? 40 : 80;

  const circleRef = useRef<SVGCircleElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  const {
    circumference,
    strokeWidth,
    gradientXOne,
    gradientYOne,
    gradientXTwo,
    gradientYTwo,
    radius
  } = calculateCircleAttributes(diameter);

  useLayoutEffect(() => {
    if (circleRef.current) {
      circleRef.current.animate(
        getCircleKeyframes(circumference),
        animationOptions
      );
    }
  }, [circumference]);

  useLayoutEffect(() => {
    if (svgRef.current) {
      svgRef.current.animate(svgKeyframes, animationOptions);
    }
  }, []);

  return (
    <div
      data-attributes={dataAttributes}
      className={cn(
        'flex flex-auto flex-col items-center justify-center',
        className
      )}
    >
      <div data-attributes="Loader" className="animate-fade-in">
        <svg
          ref={svgRef}
          className="align-middle"
          width={diameter}
          height={diameter}
          viewBox={`0 0 ${diameter} ${diameter}`}
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="50%"
            cy="50%"
            r={radius}
            strokeWidth={strokeWidth}
            stroke="#d1dfff"
            fill="none"
          />
          <circle
            ref={circleRef}
            cy="50%"
            cx="50%"
            r={radius}
            stroke="url(#paint0_linear_6748_41223)"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
          />
          <defs>
            <linearGradient
              id="paint0_linear_6748_41223"
              x1={gradientXOne}
              y1={gradientYOne}
              x2={gradientXTwo}
              y2={gradientYTwo}
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#205CDF" />
              <stop offset="1" stopColor="#753BBD" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      {description && (
        <p
          className={cn(
            'mt-16 text-body-xl-medium text-text-heading md:text-header-3-medium',
            descriptionClassName
          )}
        >
          {description}
        </p>
      )}
    </div>
  );
};
