import { ElementType, PropsWithChildren, useRef } from 'react';

import { cn } from '@/utils';

import { TruncationStateChangeHandler, useTruncatedText } from './hooks';

import { DataAttributesProps } from '../types';

const DEFAULT_ELEMENT = 'span';

export type TruncatedTextProps<T extends ElementType> = DataAttributesProps &
  PropsWithChildren & {
    as?: T;
    lineClamp?: number;
    onTruncationStateChange?: TruncationStateChangeHandler;
    className?: string;
  };

export const TruncatedText = <T extends ElementType = typeof DEFAULT_ELEMENT>({
  as,
  lineClamp,
  onTruncationStateChange,
  className,
  children,
  dataAttributes = 'TruncatedText'
}: PropsWithChildren<TruncatedTextProps<T>>) => {
  const Comp = as || DEFAULT_ELEMENT;
  const ref = useRef<HTMLElement>(null);

  useTruncatedText({
    ref,
    onTruncationStateChange
  });

  return (
    <Comp
      data-attributes={dataAttributes}
      ref={ref}
      className={cn('overflow-hidden whitespace-normal', className)}
      style={
        lineClamp
          ? {
              display: '-webkit-box',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: lineClamp
            }
          : undefined
      }
    >
      {children}
    </Comp>
  );
};
