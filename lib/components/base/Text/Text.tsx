import { ElementType, PropsWithChildren, useState } from 'react';

import { TooltipMode, useTooltipProps } from './hooks/useTooltipProps';
import { TruncatedText } from './TruncatedText';

import { Tooltip, TooltipProps } from '../Tooltip';

export type TextProps<T extends ElementType> = PropsWithChildren & {
  className?: string;
  tooltip?: TooltipProps | string;
  lineClamp?: number;
  tooltipMode?: TooltipMode;
  as?: T;
};

export const Text = <T extends ElementType>({
  className,
  as,
  lineClamp,
  tooltipMode,
  tooltip,
  children
}: TextProps<T>) => {
  const [isTruncated, setTruncated] = useState(false);

  const mode: TooltipMode = lineClamp ? tooltipMode || 'truncated' : 'off';

  const tooltipProps = useTooltipProps({
    tooltip,
    tooltipMode: mode,
    isTruncated,
    children
  });

  return (
    <Tooltip {...tooltipProps}>
      <TruncatedText
        as={as}
        lineClamp={lineClamp}
        className={className}
        onTruncationStateChange={setTruncated}
      >
        {children}
      </TruncatedText>
    </Tooltip>
  );
};
