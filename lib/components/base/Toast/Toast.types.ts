export const ToastVariant = {
  Info: 'Info',
  Success: 'Success',
  Warning: 'Warning',
  Error: 'Error'
} as const;

export type ToastVariant = (typeof ToastVariant)[keyof typeof ToastVariant];

export interface SwipeConfig {
  enabled?: boolean;
  /**
   * Distance threshold in pixels. Toast will be dismissed if user swipes beyond this distance.
   * Alternatively, toast will be dismissed if user swipes beyond half the threshold with a velocity greater than `velocityThreshold`.
   * @default 100
   */
  threshold?: number;
  /**
   * Velocity threshold in pixels/ms. Toast will be dismissed if user swipes beyond half the threshold with a velocity greater than this value.
   * @default 0.5
   */
  velocityThreshold?: number;
  /**
   * Resistance factor after threshold. Value between 0 and 1. Lower values result in more resistance.
   * @default 0.3
   */
  resistanceThreshold?: number;
  /**
   * Duration of the dismissal animation in milliseconds.
   * @default 300
   */
  animationDuration?: number;
}

export type Toast = {
  id: number;
  variant?: ToastVariant;
  title: string;
  description?: string;
  actionTitle?: string;
  showActionButton?: boolean;
  onActionClick?: () => void;
};

export type ToastContextType = {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: number) => void;
  pauseAllToasts: () => void;
  resumeAllToasts: () => void;
};
