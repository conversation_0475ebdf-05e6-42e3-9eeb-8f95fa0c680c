import { createContext, PropsWithChildren, useCallback, useState } from 'react';

import { Toast, ToastContextType, ToastVariant } from './Toast.types';
import { ToastContainer } from './ToastContainer';
import { useToastTimers } from './useToastTimers';

let toastId = 0;
export const ToastContext = createContext<ToastContextType | undefined>(
  undefined
);
export const ToastProvider = ({ children }: PropsWithChildren) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const onToastExpire = useCallback((id: number) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  }, []);

  const { startTimer, clearTimer, pauseAllToasts, resumeAllToasts } =
    useToastTimers(onToastExpire);

  const addToast = ({
    title,
    variant = ToastVariant.Info,
    description,
    actionTitle = 'View',
    onActionClick,
    showActionButton = false
  }: Omit<Toast, 'id'>) => {
    const id = toastId++;

    setToasts((prevToasts: Toast[]) => [
      ...prevToasts,
      {
        id,
        title,
        variant,
        description,
        showActionButton,
        actionTitle,
        onActionClick
      }
    ]);

    // Start the auto-dismiss timer
    startTimer(id);
  };

  const removeToast = useCallback(
    (id: number) => {
      clearTimer(id);
      setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
    },
    [clearTimer]
  );

  return (
    <ToastContext.Provider
      value={{
        toasts,
        addToast,
        removeToast,
        pauseAllToasts,
        resumeAllToasts
      }}
    >
      <ToastContainer />
      {children}
    </ToastContext.Provider>
  );
};
