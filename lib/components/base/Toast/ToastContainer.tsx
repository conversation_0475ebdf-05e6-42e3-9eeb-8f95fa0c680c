import { FC } from 'react';
import ReactDOM from 'react-dom';

import { DataAttributesProps } from '@/components/base/types';
import { getPortalElement } from '@/utils';

import { ToastItem } from './ToastItem';
import { useToast } from './useToast';

import { useUIKitModalsSelectors } from '../UIKitProvider';

export const ToastContainer: FC<DataAttributesProps> = ({ dataAttributes }) => {
  const { portalClassName } = useUIKitModalsSelectors();
  const { removeToast, toasts, pauseAllToasts, resumeAllToasts } = useToast();

  const renderToasts = () => {
    return (
      <>
        {toasts.map((toast) => (
          <ToastItem
            key={toast.id}
            variant={toast.variant}
            title={toast.title}
            description={toast.description}
            onCloseClick={() => removeToast(toast.id)}
            showActions={toast.showActionButton}
            actionTitle={toast.actionTitle}
            onActionClick={toast.onActionClick}
            onPause={pauseAllToasts}
            onResume={resumeAllToasts}
          />
        ))}
      </>
    );
  };

  const portalRoot = getPortalElement(portalClassName);

  return ReactDOM.createPortal(
    <div
      data-attributes={dataAttributes}
      className="fixed right-4 top-4 z-[1090] flex flex-col gap-12 bg-transparent pt-16 md:pr-16"
    >
      {renderToasts()}
    </div>,
    portalRoot
  );
};
