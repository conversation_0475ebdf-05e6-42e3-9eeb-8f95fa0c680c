import { useRef, useCallback } from 'react';

const DEFAULT_TOAST_DELAY = 5000;

interface ToastTimer {
  timeoutId: NodeJS.Timeout | null;
  remainingTime: number;
  startTime: number;
  isPaused: boolean;
}

export const useToastTimers = (onToastExpire: (id: number) => void) => {
  const timersRef = useRef<Map<number, ToastTimer>>(new Map());
  const globalPauseStateRef = useRef<boolean>(false);

  const startTimer = useCallback(
    (id: number, duration: number = DEFAULT_TOAST_DELAY) => {
      const timer = timersRef.current.get(id);
      if (timer?.isPaused) {
        // Resume with remaining time
        const timeoutId = setTimeout(() => {
          onToastExpire(id);
          timersRef.current.delete(id);
        }, timer.remainingTime);

        timersRef.current.set(id, {
          ...timer,
          timeoutId,
          startTime: Date.now(),
          isPaused: false
        });
      } else {
        // Start new timer
        const timeoutId = setTimeout(() => {
          onToastExpire(id);
          timersRef.current.delete(id);
        }, duration);

        timersRef.current.set(id, {
          timeoutId,
          remainingTime: duration,
          startTime: Date.now(),
          isPaused: false
        });
      }
    },
    [onToastExpire]
  );

  const clearTimer = useCallback((id: number) => {
    const timer = timersRef.current.get(id);
    if (timer?.timeoutId) {
      clearTimeout(timer.timeoutId);
    }
    timersRef.current.delete(id);
  }, []);

  const pauseAllToasts = useCallback(() => {
    if (globalPauseStateRef.current) {
      return; // Already paused globally
    }

    globalPauseStateRef.current = true;

    // Pause all active timers
    timersRef.current.forEach((timer, id) => {
      if (!timer.isPaused && timer.timeoutId) {
        const elapsed = Date.now() - timer.startTime;
        const remainingTime = Math.max(0, timer.remainingTime - elapsed);

        clearTimeout(timer.timeoutId);
        timersRef.current.set(id, {
          ...timer,
          timeoutId: null,
          remainingTime,
          isPaused: true
        });
      }
    });
  }, []);

  const resumeAllToasts = useCallback(() => {
    if (!globalPauseStateRef.current) {
      return; // Not globally paused
    }

    globalPauseStateRef.current = false;

    // Resume all paused timers
    timersRef.current.forEach((timer, id) => {
      if (timer.isPaused) {
        startTimer(id, timer.remainingTime);
      }
    });
  }, [startTimer]);

  return {
    startTimer,
    clearTimer,
    pauseAllToasts,
    resumeAllToasts
  };
};
