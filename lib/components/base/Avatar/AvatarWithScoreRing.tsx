import * as React from 'react';

import { cn } from '@/utils';

import { TooltipProps } from '../Tooltip';
import { DataAttributesProps } from '../types';

import { Avatar } from '.';

export const AvatarWithScoreRingVariant = {
  Primary: 'Primary',
  Danger: 'Danger',
  Warning: 'Warning',
  Success: 'Success'
} as const;

export type AvatarWithScoreRingVariant =
  (typeof AvatarWithScoreRingVariant)[keyof typeof AvatarWithScoreRingVariant];

const AvatarWithScoreRingColorMap = {
  [AvatarWithScoreRingVariant.Danger]: 'stroke-toast-icon-error',
  [AvatarWithScoreRingVariant.Warning]: 'stroke-toast-icon-warning',
  [AvatarWithScoreRingVariant.Success]: 'stroke-toast-icon-success',
  [AvatarWithScoreRingVariant.Primary]: 'stroke-tag-light_blue-text'
};

export type AvatarWithScoreRingProps = React.ComponentProps<'div'> &
  DataAttributesProps & {
    value: number;
    variant?: AvatarWithScoreRingVariant;
    className?: string;
    lowThreshold?: number; // A%
    highThreshold?: number; // B%
    inverted?: boolean;
    fullName?: string;
    email?: string;
    tooltip?: TooltipProps;
  };

export const AvatarWithScoreRing: React.FC<AvatarWithScoreRingProps> = ({
  className,
  value,
  variant,
  lowThreshold,
  highThreshold,
  inverted = false,
  fullName,
  email,
  tooltip,
  dataAttributes = 'AvatarWithScoreRingVariant',
  ...props
}) => {
  const Comp = 'div';

  const ringWidth = 72;

  const ringStrokeWidth = 4;

  const radius = (ringWidth - ringStrokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const progressNormalized = Math.min(100, Math.max(0, value));
  const strokeDashoffset =
    circumference - (progressNormalized / 100) * circumference;

  const resolvedStrokeColor = React.useMemo(() => {
    if (variant) return AvatarWithScoreRingColorMap[variant];
    if (!lowThreshold && !highThreshold)
      return AvatarWithScoreRingColorMap[AvatarWithScoreRingVariant.Primary];
    const low = lowThreshold ?? 33;
    const high = highThreshold ?? 66;

    const getColor = (value: number) => {
      if (inverted) {
        if (value <= low)
          return AvatarWithScoreRingColorMap[
            AvatarWithScoreRingVariant.Success
          ];
        if (value <= high)
          return AvatarWithScoreRingColorMap[
            AvatarWithScoreRingVariant.Warning
          ];
        return AvatarWithScoreRingColorMap[AvatarWithScoreRingVariant.Danger];
      } else {
        if (value <= low)
          return AvatarWithScoreRingColorMap[AvatarWithScoreRingVariant.Danger];
        if (value <= high)
          return AvatarWithScoreRingColorMap[
            AvatarWithScoreRingVariant.Warning
          ];
        return AvatarWithScoreRingColorMap[AvatarWithScoreRingVariant.Success];
      }
    };

    return getColor(progressNormalized);
  }, [progressNormalized, variant, lowThreshold, highThreshold, inverted]);

  const Ring = (
    <div className="relative">
      <svg width={ringWidth} height={ringWidth}>
        <circle
          cx={ringWidth / 2}
          cy={ringWidth / 2}
          r={radius}
          strokeWidth={ringStrokeWidth}
          className="fill-none stroke-divider-dark"
        />
        <circle
          cx={ringWidth / 2}
          cy={ringWidth / 2}
          r={radius}
          strokeWidth={ringStrokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn('fill-none', resolvedStrokeColor)}
          transform={`rotate(-90 ${ringWidth / 2} ${ringWidth / 2})`}
        />
        <text
          x="50%"
          y="50%"
          dy=".3em"
          textAnchor="middle"
          className="fill-text-heading text-body-2-medium"
        >
          {progressNormalized}%
        </text>
      </svg>
      <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform">
        <Avatar
          size="Large"
          fullName={fullName}
          email={email}
          tooltip={tooltip}
        />
      </div>
    </div>
  );

  return (
    <Comp
      data-attributes={dataAttributes}
      className={cn('flex items-center gap-8', className)}
      {...props}
    >
      {Ring}
    </Comp>
  );
};
