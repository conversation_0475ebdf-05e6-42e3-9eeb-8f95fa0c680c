import { ComponentProps, FC, useEffect, useState } from 'react';

import { cva } from 'class-variance-authority';

import { cn } from '@/utils';

import { getAvatarInitials, getAvatarVariant } from './Avatar.utils';
import { useAvatar } from './AvatarProvider';

import { Tooltip, TooltipProps } from '../Tooltip';
import { DataAttributesProps } from '../types';

export const AvatarSize = {
  Large: 'Large',
  Medium: 'Medium',
  Small: 'Small'
} as const;

export type AvatarSize = (typeof AvatarSize)[keyof typeof AvatarSize];

export const AvatarVariant = {
  LightBlue: 'LightBlue',
  Electric: 'Electric',
  Green: 'Green',
  Orange: 'Orange',
  PacificBlue: 'PacificBlue',
  PurpleHeart: 'PurpleHeart',
  Supernova: 'Supernova',
  Neutral: 'Neutral'
} as const;

export type AvatarVariant = (typeof AvatarVariant)[keyof typeof AvatarVariant];

export const avatarVariants = cva(
  'flex items-center justify-center rounded-full',
  {
    variants: {
      variant: {
        [AvatarVariant.LightBlue]: cn(
          'bg-avatar-light_blue-fill text-avatar-light_blue-text'
        ),
        [AvatarVariant.Electric]: cn(
          'bg-avatar-electric-fill text-avatar-electric-text'
        ),
        [AvatarVariant.Green]: cn(
          'bg-avatar-green-fill text-avatar-green-text'
        ),
        [AvatarVariant.Orange]: cn(
          'bg-avatar-orange-fill text-avatar-orange-text'
        ),
        [AvatarVariant.PacificBlue]: cn(
          'bg-avatar-pacific_blue-fill text-avatar-pacific_blue-text'
        ),
        [AvatarVariant.PurpleHeart]: cn(
          'bg-avatar-purple_heart-fill text-avatar-purple_heart-text'
        ),
        [AvatarVariant.Supernova]: cn(
          'bg-avatar-supernova-fill text-avatar-supernova-text'
        ),
        [AvatarVariant.Neutral]: cn(
          'bg-avatar-neutral-fill text-avatar-neutral-text'
        )
      },
      size: {
        [AvatarSize.Large]:
          'h-60 min-h-60 max-h-60 w-60 min-w-60 max-w-60 text-header-3-regular',
        [AvatarSize.Medium]:
          'h-40 min-h-40 max-h-40 w-40 min-w-40 max-w-40 text-body-1-regular',
        [AvatarSize.Small]:
          'h-24 min-h-24 max-h-24 w-24 min-w-24 max-w-24 text-body-4-regular'
      }
    },
    defaultVariants: {
      variant: AvatarVariant.LightBlue,
      size: AvatarSize.Medium
    }
  }
);

export type AvatarProps = ComponentProps<'div'> &
  DataAttributesProps & {
    variant?: AvatarVariant;
    size?: AvatarSize;
    email?: string;
    fullName?: string;
    count?: string;
    tooltip?: TooltipProps;
  };

export const Avatar: FC<AvatarProps> = ({
  className,
  variant,
  email,
  size = AvatarSize.Medium,
  fullName,
  count,
  tooltip,
  dataAttributes = 'Avatar',
  ...props
}) => {
  const Comp = 'div';

  const [url, setUrl] = useState('');
  const { loadImage } = useAvatar();

  useEffect(() => {
    loadImage(email || '')
      .then((data) => setUrl(data))
      .catch(() => setUrl(''));
  }, [email, loadImage]);

  const initials = getAvatarInitials(fullName);
  const avatarVariant = variant ? variant : getAvatarVariant(fullName);

  const renderAvatar = () => {
    if (url) {
      return (
        <img
          data-attributes={dataAttributes}
          src={url}
          alt={fullName}
          className={cn(
            'select-none object-cover',
            avatarVariants({ variant: avatarVariant, size, className }),
            className
          )}
        />
      );
    } else {
      return (
        <Comp
          data-attributes={dataAttributes}
          className={cn(
            avatarVariants({ variant: avatarVariant, size, className })
          )}
          {...props}
        >
          {count ?? initials}
        </Comp>
      );
    }
  };

  if (tooltip && tooltip.title) {
    return <Tooltip {...tooltip}>{renderAvatar()}</Tooltip>;
  }

  return renderAvatar();
};
