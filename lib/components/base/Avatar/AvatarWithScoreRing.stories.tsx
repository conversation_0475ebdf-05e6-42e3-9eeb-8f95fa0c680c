import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import {
  AvatarWithScoreRing,
  AvatarWithScoreRingVariant
} from './AvatarWithScoreRing';

const meta = {
  title: 'Design System/Components/Avatar/AvatarWithScoreRing',
  component: AvatarWithScoreRing,
  tags: ['autodocs', 'Stable'],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'ScoreRingAvatar will display Avatar with Score Ring functionality'
      }
    }
  },
  args: {
    value: 80,
    inverted: false,
    fullName: '<PERSON>',
    email: '<EMAIL>',
    dataAttributes: 'AvatarWithScoreRingVariant'
  },
  argTypes: {
    value: {
      control: { type: 'range', min: 0, max: 100, step: 1 },
      description: 'Progress value in percentage (0-100)'
    },
    variant: {
      control: 'select',
      options: Object.values(AvatarWithScoreRingVariant),
      description: 'Custom stroke color class'
    },
    lowThreshold: {
      control: { type: 'range', min: 0, max: 100, step: 1 },
      description: 'Threshold for low score (affects color)'
    },
    highThreshold: {
      control: { type: 'range', min: 0, max: 100, step: 1 },
      description: 'Threshold for high score (affects color)'
    },
    inverted: {
      control: 'boolean',
      description: 'Invert the color logic for thresholds'
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes'
    },
    fullName: {
      control: 'text',
      description: 'Full name for the avatar'
    },
    email: {
      control: 'text',
      description: 'Email address for the avatar'
    },
    tooltip: {
      control: 'object',
      description: 'Tooltip configuration object'
    },
    dataAttributes: {
      control: 'text',
      description: 'Data attributes for the component'
    }
  }
} satisfies Meta<typeof AvatarWithScoreRing>;

export default meta;
type Story = StoryObj<typeof meta>;

const avatars = [
  {
    email: '<EMAIL>',
    fullName: 'Al Hashemi'
  },
  {
    email: '<EMAIL>',
    fullName: 'Rashid Al-Mansoori'
  },
  {
    email: '<EMAIL>',
    fullName: 'Emily Williams'
  },
  {
    email: '<EMAIL>',
    fullName: 'Al Hashemi'
  }
];

export const Default: Story = {
  args: {
    value: 80,
    email: '<EMAIL>',
    fullName: 'Rashid Al-Mansoori'
  }
};

export const WithTooltip: Story = {
  args: {
    variant: 'Primary',
    value: 80,
    email: avatars[0].email,
    fullName: avatars[0].fullName,
    tooltip: {
      title: 'Rashid Al-Mansoori'
    }
  }
};

export const AllVariants: Story = {
  name: 'Variants',
  args: {
    value: 75,
    email: avatars[0].email,
    fullName: avatars[0].fullName
  },
  render: (args) => (
    <div style={{ display: 'flex', gap: '16px' }}>
      {Object.values(AvatarWithScoreRingVariant).map((variant, index) => (
        <AvatarWithScoreRing
          key={variant}
          {...args}
          email={avatars[index].email}
          fullName={avatars[index].fullName}
          variant={variant}
        />
      ))}
    </div>
  )
};

export const WithThresholds: Story = {
  name: 'Min & Max Thresholds',
  args: {
    value: 50,
    lowThreshold: 30,
    highThreshold: 70,
    email: avatars[0].email,
    fullName: avatars[0].fullName
  },
  render: (args) => (
    <div style={{ display: 'flex', gap: '16px' }}>
      <AvatarWithScoreRing
        {...args}
        email={avatars[0].email}
        fullName={avatars[0].fullName}
        value={20}
      />
      <AvatarWithScoreRing
        {...args}
        email={avatars[1].email}
        fullName={avatars[1].fullName}
        value={50}
      />
      <AvatarWithScoreRing
        {...args}
        email={avatars[2].email}
        fullName={avatars[2].fullName}
        value={90}
      />
    </div>
  )
};

export const InvertedThresholds: Story = {
  name: 'Inverted Min & Max Thresholds',
  args: {
    value: 50,
    lowThreshold: 30,
    highThreshold: 70,
    inverted: true,
    email: '<EMAIL>',
    fullName: 'Rashid Al-Mansoori'
  },
  render: (args) => (
    <div style={{ display: 'flex', gap: '16px' }}>
      <AvatarWithScoreRing
        {...args}
        email={avatars[0].email}
        fullName={avatars[0].fullName}
        value={20}
      />
      <AvatarWithScoreRing
        {...args}
        email={avatars[1].email}
        fullName={avatars[1].fullName}
        value={50}
      />
      <AvatarWithScoreRing
        {...args}
        email={avatars[2].email}
        fullName={avatars[2].fullName}
        value={90}
      />
    </div>
  )
};
