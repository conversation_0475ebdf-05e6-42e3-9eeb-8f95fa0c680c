import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { AvatarGroup } from './AvatarGroup';

import { AvatarSize } from '../Avatar';
import { TooltipSize, TooltipVariant } from '../Tooltip';

//TODO: Add and clarify all argTypes
const meta = {
  title: 'Design System/Components/Avatar/Avatar Group',
  component: AvatarGroup,
  tags: ['autodocs', 'Stable'],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'AvatarGroup will display group of Avatar components.'
      }
    }
  },
  args: {
    items: [
      {
        email: '<EMAIL>',
        fullName: '<PERSON>'
      },
      {
        email: '<EMAIL>',
        fullName: '<PERSON>'
      },
      {
        email: '<EMAIL>',
        fullName: '<PERSON>'
      }
    ],
    size: AvatarSize.Medium,
    maxVisible: 2
  },
  argTypes: {
    items: {
      description: 'List of users to display Avatars',
      control: { type: 'object' }
    },
    className: {
      description:
        'Additional CSS class names for custom styling of the AvatarGroup component.',
      control: 'text'
    },
    size: {
      description:
        'Specifies individual size of the Avatar (e.g., small, medium, large).',
      control: 'radio',
      options: Object.values(AvatarSize) as AvatarSize[]
    },
    maxVisible: {
      description:
        'Maximum number of avatars to display before showing a count indicator',
      control: { type: 'number', min: 0, max: 10 }
    },
    tooltip: {
      description: 'Custom tooltip configuration for the avatar group',
      control: { type: 'object' }
    }
  }
} satisfies Meta<typeof AvatarGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

const items = [
  {
    email: '<EMAIL>',
    fullName: 'Al Hashemi'
  },
  {
    email: '<EMAIL>',
    fullName: 'Rashid Al-Mansoori'
  },
  {
    email: '<EMAIL>',
    fullName: 'Emily Williams'
  },
  {
    email: '<EMAIL>',
    fullName: 'John Doe'
  },
  {
    email: '<EMAIL>',
    fullName: 'Alice Smith'
  },
  {
    email: '<EMAIL>',
    fullName: 'Bob Wilson'
  }
];
export const Default: Story = {
  args: {
    size: AvatarSize.Medium,
    items: [items[0], items[1], items[2]]
  }
};

export const Small: Story = {
  args: {
    items: [items[0], items[1], items[2]],
    size: AvatarSize.Small
  }
};

export const Medium: Story = {
  args: {
    items: [items[0], items[1], items[2]],
    size: AvatarSize.Medium
  }
};

export const Large: Story = {
  args: {
    items: [items[0], items[1], items[2]],
    size: AvatarSize.Large
  }
};

export const OneItem: Story = {
  args: {
    items: [items[0]],
    size: AvatarSize.Medium
  }
};
export const TwoItems: Story = {
  args: {
    items: [items[0], items[1]],
    size: AvatarSize.Medium
  }
};
export const ManyItems: Story = {
  args: {
    items: [items[0], items[1], items[2]],
    size: AvatarSize.Medium
  }
};

// MaxVisible Stories
export const MaxVisibleOne: Story = {
  args: {
    items: items,
    size: AvatarSize.Medium,
    maxVisible: 1
  }
};

export const MaxVisibleThree: Story = {
  args: {
    items: items,
    size: AvatarSize.Medium,
    maxVisible: 3
  }
};

export const MaxVisibleFive: Story = {
  args: {
    items: items,
    size: AvatarSize.Medium,
    maxVisible: 5
  }
};

export const MaxVisibleAll: Story = {
  args: {
    items: items,
    size: AvatarSize.Medium,
    maxVisible: 10
  }
};

// Custom Tooltip Stories
export const CustomTooltip: Story = {
  args: {
    items: items,
    size: AvatarSize.Medium,
    maxVisible: 2,
    tooltip: {
      title: 'Team Members',
      heading: 'Project Team',
      variant: TooltipVariant.Light,
      size: TooltipSize.Large
    }
  }
};

export const CustomTooltipSmall: Story = {
  args: {
    items: items,
    size: AvatarSize.Small,
    maxVisible: 1,
    tooltip: {
      title: 'Team',
      variant: TooltipVariant.Dark,
      size: TooltipSize.Small
    }
  }
};
