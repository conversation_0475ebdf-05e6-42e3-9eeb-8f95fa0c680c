import { FunctionComponent, HTMLAttributes } from 'react';

import { cva } from 'class-variance-authority';
import _clamp from 'lodash/clamp';

import { cn } from '@/utils';

import { Avatar, AvatarProps, AvatarSize, AvatarVariant } from './Avatar';

import { Tooltip, TooltipProps } from '../Tooltip';
import { DataAttributesProps } from '../types';

export const AVATAR_GROUP_MAX_USERS_VISIBLE = 2;

type BaseAvatarGroupProps = HTMLAttributes<HTMLDivElement> &
  DataAttributesProps;

export interface AvatarGroupProps extends BaseAvatarGroupProps {
  items: AvatarProps[];
  size?: AvatarSize;
  maxVisible?: number;
  tooltip?: TooltipProps;
}

export const AvatarGroup: FunctionComponent<AvatarGroupProps> = ({
  items,
  size = AvatarSize.Medium,
  maxVisible = AVATAR_GROUP_MAX_USERS_VISIBLE,
  className,
  dataAttributes = 'AvatarGroup',
  tooltip,
  ...restProps
}) => {
  const positiveMaxVisible = _clamp(maxVisible, 0, Number.MAX_SAFE_INTEGER);
  const visibleUsers = items.slice(0, positiveMaxVisible);
  const hiddenCount = items.length - positiveMaxVisible;

  const sizeVariants = cva('', {
    variants: {
      size: {
        [AvatarSize.Large]: '-ml-32',
        [AvatarSize.Medium]: '-ml-20',
        [AvatarSize.Small]: '-ml-12'
      }
    },
    defaultVariants: {
      size: AvatarSize.Medium
    }
  });

  const tooltipSizeVariants = cva('', {
    variants: {
      size: {
        [AvatarSize.Large]: '-ml-16',
        [AvatarSize.Medium]: '-ml-[10px]',
        [AvatarSize.Small]: '-ml-[6px]'
      }
    },
    defaultVariants: {
      size: AvatarSize.Medium
    }
  });

  const renderAvatarGroup = () => {
    return (
      <div
        className={cn('relative flex items-center justify-center', className)}
        data-attributes={dataAttributes}
        {...restProps}
      >
        {visibleUsers?.map((item) => (
          <Avatar
            email={item.email}
            fullName={item.fullName}
            key={item.email}
            className={cn(sizeVariants({ size }))}
            size={size}
          />
        ))}
        {hiddenCount > 0 && (
          <Avatar
            count={`+${hiddenCount}`}
            className={cn(sizeVariants({ size }))}
            size={size}
            variant={AvatarVariant.Neutral}
          />
        )}
      </div>
    );
  };

  if (tooltip) {
    return (
      <Tooltip
        {...tooltip}
        className={cn(
          tooltipSizeVariants({ size, className: tooltip.className })
        )}
      >
        {renderAvatarGroup()}
      </Tooltip>
    );
  }

  return (
    <Tooltip
      className={cn(tooltipSizeVariants({ size }))}
      title={
        <div>
          {items.map((item) => (
            <div key={item.email}>{item.fullName}</div>
          ))}
        </div>
      }
    >
      {renderAvatarGroup()}
    </Tooltip>
  );
};
