export type FilterOption = {
  id: string | number;
  displayName: string;
  disabled?: boolean;
};

export type FilterChangeHandler<TKey extends string> = (
  id: TKey
) => (filter?: FilterOption[]) => void;

export type VisibilityMode = 'always' | 'shownByDefault' | 'hiddenByDefault';

export type CommonFilterProps<TKey extends string> = {
  id: TKey;
  label: string;
  visibilityMode?: VisibilityMode;
  basis?: string;
  className?: string;
};

export type MultiselectFilterModel<TKey extends string> =
  CommonFilterProps<TKey> & {
    onLoadMore?: () => void;
    hasMore?: boolean;
    options: FilterOption[];
    onClear?: () => void;
    onSearch?: (searchQuery: string) => void;
    onSelect?: (option: FilterOption & { isSelected: boolean }) => void;
  };

export type Filter<TKey extends string> = MultiselectFilterModel<TKey>;

export type FilterState<TKey extends string> = Pick<
  Filter<TKey>,
  'id' | 'label' | 'visibilityMode'
> & {
  selected: boolean;
  disabled: boolean;
};

export type FilterStateMap<TKey extends string> = Record<
  TKey,
  FilterState<TKey>
>;
