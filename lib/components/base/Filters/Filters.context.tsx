import { createContext, useContext } from 'react';

import {
  Filter,
  FilterChangeHandler,
  FilterOption,
  FilterState
} from './Filters.types';

export type FiltersContext<TKey extends string> = {
  selectedFilters: Partial<Record<TKey, FilterOption[]>>;
  filtersState: FilterState<TKey>[];
  onFilterChanged: FilterChangeHandler<TKey>;
  onRemoveFilter: (id: TKey) => () => void;
  onRegisterFilter: (
    props: Pick<Filter<TKey>, 'id' | 'visibilityMode' | 'label'>
  ) => void;
  onUnregisterFilter: (props: Pick<Filter<TKey>, 'id'>) => void;
};

// TODO: Find a way how to create generic context
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const FiltersContext = createContext<FiltersContext<any>>({
  selectedFilters: {},
  filtersState: [],
  onFilterChanged: () => () => null,
  onRemoveFilter: () => () => null,
  onRegisterFilter: () => null,
  onUnregisterFilter: () => null
});

export const useFiltersContext = () => useContext(FiltersContext);
