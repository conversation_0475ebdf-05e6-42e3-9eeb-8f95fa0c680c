import { useEffect, useMemo } from 'react';

import { useFiltersContext } from './Filters.context';
import { Filter } from './Filters.types';

export const useFilterController = <TKey extends string>({
  id,
  visibilityMode,
  label
}: Pick<Filter<TKey>, 'id' | 'visibilityMode' | 'label'>) => {
  const {
    onRegisterFilter,
    onUnregisterFilter,
    onFilterChanged,
    selectedFilters,
    onRemoveFilter,
    filtersState
  } = useFiltersContext();

  useEffect(() => {
    onRegisterFilter({ id, visibilityMode, label });

    return () => {
      onUnregisterFilter({ id });
    };
  }, [onRegisterFilter, onUnregisterFilter, id, visibilityMode, label]);

  return useMemo(
    () => ({
      onChange: onFilterChanged(id),
      onRemove: onRemoveFilter(id),
      value: selectedFilters[id],
      filterState: filtersState.find((filter) => filter.id === id)
    }),
    [onFilterChanged, id, onRemoveFilter, selectedFilters, filtersState]
  );
};

export const useFilterContext = <TKey extends string>(id: TKey) => {
  const { selectedFilters, filtersState, onFilterChanged } =
    useFiltersContext();
  return useMemo(
    () => ({
      onChange: onFilterChanged(id),
      value: selectedFilters[id],
      filterState: filtersState.find((filter) => filter.id === id)
    }),
    [onFilterChanged, id, selectedFilters, filtersState]
  );
};
