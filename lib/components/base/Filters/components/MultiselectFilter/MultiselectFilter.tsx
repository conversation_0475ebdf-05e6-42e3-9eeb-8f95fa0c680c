import {
  memo,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState
} from 'react';

import isEmpty from 'lodash/isEmpty';

import { cn } from '@/utils';

import { useFilterController } from '../../Filters.hooks';
import { MultiselectFilterModel } from '../../Filters.types';

import {
  Dropdown,
  dropdownHighlightMatch,
  DropdownLayout
} from '../../../Dropdown';
import { Input } from '../../../Input';
import { Text } from '../../../Text';

export type MultiselectFilterProps<TKey extends string> =
  MultiselectFilterModel<TKey>;

export const MultiselectFilter = memo(
  <TKey extends string>({
    id,
    label,
    options,
    className,
    visibilityMode = 'shownByDefault',
    basis,
    hasMore,
    onLoadMore,
    onSearch,
    onSelect,
    onClear
  }: MultiselectFilterProps<TKey>) => {
    const {
      filterState,
      value = [],
      onChange,
      onRemove
    } = useFilterController({
      id,
      visibilityMode,
      label
    });

    const [isOpen, setIsOpen] = useState(false);
    const [selectedOptions, setSelectedOptions] = useState(value);
    const [isSelectedShownMode, setSelectedShownMode] = useState(false);
    const [search, setSearch] = useState('');

    const handleSearch = useCallback(
      (searchQuery: string) => {
        setSearch(searchQuery);
        onSearch?.(searchQuery);
      },
      [onSearch]
    );

    useEffect(() => {
      if (!filterState?.selected) {
        setIsOpen(false);
      }
    }, [filterState?.selected]);

    const handleToggleDropdown = useCallback(
      (isOpen: SetStateAction<boolean>) => {
        setIsOpen(isOpen);
        if (!isOpen) {
          handleSearch('');
          onChange?.(isEmpty(selectedOptions) ? undefined : selectedOptions);
        } else {
          setSelectedOptions(value);
        }
      },
      [onChange, selectedOptions, value, handleSearch]
    );

    const handleApply = useCallback(() => {
      handleToggleDropdown(false);
    }, [handleToggleDropdown]);

    const handleClear = useCallback(() => {
      setSelectedOptions([]);
      onChange();
      onClear?.();
    }, [onChange, onClear]);

    const filteredOptions = useMemo(() => {
      if (isSelectedShownMode) {
        if (onSearch && search) {
          const selectedIds = selectedOptions.map((option) => option.id);
          return options.filter((option) => selectedIds.includes(option.id));
        }

        return selectedOptions.filter((option) => {
          const regex = new RegExp(`(${search})`, 'i');
          return regex.test(option.displayName);
        });
      } else {
        if (onSearch) {
          return options;
        }

        return options.filter((option) => {
          const regex = new RegExp(`(${search})`, 'i');

          return regex.test(option.displayName);
        });
      }
    }, [onSearch, isSelectedShownMode, options, search, selectedOptions]);

    const inputContent = useMemo(() => {
      const selectedItemsStr = value
        .map(
          (item, index, arr) =>
            `${item.displayName}${arr.length - (index + 1) === 0 ? '' : ', '}`
        )
        .join('');
      return value.length ? (
        <Text
          lineClamp={1}
          className="grow text-body-2-regular"
          tooltip={selectedItemsStr}
        >
          <span className="text-input-text-disabled">{label}: </span>
          <span className="text-input-text-filled">{selectedItemsStr}</span>
        </Text>
      ) : undefined;
    }, [label, value]);

    if (!filterState?.selected) {
      return null;
    }

    return (
      <Dropdown
        style={{ flexBasis: `${basis}px` }}
        placement="bottom-start"
        dropdownOptions={{ width: '364px' }}
        isOpen={isOpen}
        setIsOpen={handleToggleDropdown}
        className={cn('max-w-full grow @md:max-w-[332px]', className)}
        renderTrigger={({ getTriggerProps }) => (
          <div {...getTriggerProps()}>
            <Input
              inputClassName="justify-start"
              inputContentClassName={cn({ 'max-w-0': !isEmpty(value) })}
              readOnly
              size="Medium"
              label=""
              placeholder={value.length ? '' : label}
              rightIcon={isOpen ? 'Expand_up_light' : 'Expand_down_light'}
              badges={inputContent}
            />
          </div>
        )}
        renderDropdown={() => (
          <DropdownLayout.FloatingContainer>
            <DropdownLayout.FilterHeader
              label={label}
              onRemoveClick={visibilityMode !== 'always' ? onRemove : undefined}
            />
            <DropdownLayout.Search
              value={search}
              onChange={(e) => handleSearch(e.target.value)}
              onClear={() => handleSearch('')}
            />
            <DropdownLayout.List hasMore={hasMore} onLoadMore={onLoadMore}>
              {filteredOptions.map(({ displayName, id, disabled }) => {
                const isSelected = !!selectedOptions?.find(
                  (listItem) => listItem.id === id
                );
                return (
                  <DropdownLayout.ListItem
                    onClick={({ id, title }) => {
                      onSelect?.({
                        disabled,
                        id,
                        displayName: title,
                        isSelected: !isSelected
                      });
                      if (isSelected) {
                        return setSelectedOptions(
                          selectedOptions?.filter(
                            ({ id: selectedId }) => id !== selectedId
                          )
                        );
                      }
                      setSelectedOptions([
                        ...selectedOptions,
                        { id, displayName: title }
                      ]);
                    }}
                    variant="Primary"
                    title={dropdownHighlightMatch(displayName, search)}
                    key={id}
                    selected={isSelected}
                    option={{
                      id,
                      disabled,
                      title: displayName
                    }}
                  />
                );
              })}
            </DropdownLayout.List>
            <DropdownLayout.Footer
              showSelected={isSelectedShownMode}
              onApply={handleApply}
              selectedOptionsCount={selectedOptions.length}
              onSetShowSelected={setSelectedShownMode}
              onClearSelectedOptions={handleClear}
            />
          </DropdownLayout.FloatingContainer>
        )}
      />
    );
  }
);
