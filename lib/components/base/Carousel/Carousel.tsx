/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, ReactNode, useRef } from 'react';

import clsx from 'clsx';
import 'swiper/css';
import { Keyboard, Navigation, Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Swiper as SwiperType } from 'swiper/types';

import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';

import { ButtonVariant, IconButton } from '../Button';
import { Text } from '../Text';
import { DataAttributesProps } from '../types';

import { useIsMobile } from '../../../hooks';

export const CarouselSize = {
  Small: 'small',
  Medium: 'medium',
  Large: 'large'
} as const;

export type CarouselSize = (typeof CarouselSize)[keyof typeof CarouselSize];

export type CarouselItem<T> = {
  id: string | number;
  data: T;
};

type CarouselProps<T> = DataAttributesProps & {
  title?: string;
  items?: CarouselItem<T>[];
  renderItem: (item: CarouselItem<T>, index: number) => ReactNode;
  showNavigation?: boolean;
  showPagination?: boolean;
  loop?: boolean;
  size?: CarouselSize;
  containerClassName?: string;
  paginationClassName?: string;
  carouselClassName?: string;
  slidesPerView?: number;
};

const getSizeConfig = (
  size: CarouselSize,
  items: CarouselItem<any>[],
  customSlidesPerView?: number
) => {
  if (customSlidesPerView) {
    return {
      height: size === CarouselSize.Large ? 'h-[400px]' : 'h-120',
      breakpoints: {
        375: {
          slidesPerView: customSlidesPerView,
          spaceBetween: 16
        },
        1440: {
          slidesPerView: customSlidesPerView,
          spaceBetween: 20
        },
        1920: {
          slidesPerView: customSlidesPerView,
          spaceBetween: 24
        }
      }
    };
  }

  switch (size) {
    case CarouselSize.Small:
      return {
        height: 'h-120',
        breakpoints: {
          375: {
            slidesPerView: Math.min(2.6, items.length),
            spaceBetween: 16
          },
          1440: {
            slidesPerView: Math.min(9, items.length),
            spaceBetween: 20
          },
          1920: {
            slidesPerView: Math.min(10, items.length),
            spaceBetween: 24
          }
        }
      };
    case CarouselSize.Medium:
      return {
        height: 'h-120',
        breakpoints: {
          375: {
            slidesPerView: Math.min(2, items.length),
            spaceBetween: 16
          },
          1440: {
            slidesPerView: Math.min(4, items.length),
            spaceBetween: 20
          },
          1920: {
            slidesPerView: Math.min(5, items.length),
            spaceBetween: 24
          }
        }
      };
    case CarouselSize.Large:
      return {
        height: 'h-[400px]',
        breakpoints: {
          375: {
            slidesPerView: 1.2,
            spaceBetween: 16
          },
          1440: {
            slidesPerView: Math.min(3, items.length),
            spaceBetween: 20
          },
          1920: {
            slidesPerView: Math.min(3, items.length),
            spaceBetween: 24
          }
        }
      };
    default:
      return {
        height: 'h-120',
        breakpoints: {
          375: {
            slidesPerView: Math.min(2, items.length),
            spaceBetween: 16
          },
          1440: {
            slidesPerView: Math.min(4, items.length),
            spaceBetween: 20
          },
          1920: {
            slidesPerView: Math.min(5, items.length),
            spaceBetween: 24
          }
        }
      };
  }
};

export const Carousel: FC<CarouselProps<any>> = ({
  title,
  items = [],
  renderItem,
  dataAttributes = 'Carousel',
  showNavigation = true,
  showPagination = false,
  loop = true,
  size = CarouselSize.Medium,
  containerClassName = '',
  paginationClassName = '',
  carouselClassName = '',
  slidesPerView
}) => {
  const isMobile = useIsMobile();

  const shouldShowNavigation = showNavigation && !isMobile;

  const swiperRef = useRef<SwiperType>();
  const sizeConfig = getSizeConfig(size, items, slidesPerView);

  const paginationSettings = {
    clickable: true,
    el: '.custom-pagination'
  };

  return (
    <div className={containerClassName} data-attributes={dataAttributes}>
      {title && (
        <Text className="text-header-3-medium text-text-heading">{title}</Text>
      )}
      <div className="relative mt-20 grid">
        {shouldShowNavigation && (
          <>
            <IconButton
              className={
                'absolute -left-20 top-1/2 z-10 flex h-40 w-40 -translate-y-1/2 items-center justify-center rounded-full bg-modal-button-fill-rested shadow-button'
              }
              variant={ButtonVariant.Tertiary}
              onClick={() => swiperRef.current?.slidePrev()}
              icon="Expand_left_light"
              name="Expand_left_light"
            />

            <IconButton
              className={
                'absolute -right-20 top-1/2 z-10 flex h-40 w-40 -translate-y-1/2 items-center justify-center rounded-full bg-modal-button-fill-rested shadow-button'
              }
              onClick={() => swiperRef.current?.slideNext()}
              variant={ButtonVariant.Tertiary}
              aria-label="Next slide"
              icon="Expand_right_light"
            />
          </>
        )}

        <Swiper
          className={clsx(
            `min-h-0 w-full min-w-0 !overflow-visible ${sizeConfig.height}`,
            carouselClassName
          )}
          modules={[Navigation, Pagination, Keyboard]}
          keyboard={{ enabled: true }}
          spaceBetween={16}
          slidesPerView={1}
          breakpoints={sizeConfig.breakpoints}
          navigation={false}
          pagination={showPagination ? paginationSettings : false}
          onBeforeInit={(swiper) => {
            swiperRef.current = swiper;
          }}
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
          }}
          watchOverflow={true}
          centeredSlides={false}
          loop={loop}
        >
          {items.map((item, index) => (
            <SwiperSlide
              key={item.id}
              className={`border-1 ${sizeConfig.height} w-auto flex-shrink-0 rounded-xl border-solid border-divider-dark bg-surface-grey_0 shadow-dropdown`}
            >
              {renderItem(item, index)}
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      <div
        className={clsx(
          'custom-pagination flex justify-center pt-20',
          paginationClassName
        )}
      />
    </div>
  );
};
