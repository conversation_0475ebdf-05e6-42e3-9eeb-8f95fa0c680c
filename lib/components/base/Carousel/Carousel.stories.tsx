import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Carousel, CarouselItem } from './Carousel';

const meta = {
  title: 'Design System/Components/Carousel/Carousel',
  component: Carousel,
  tags: ['autodocs', 'Stable'],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'Carousel component for displaying a horizontal list of items with navigation controls.'
      }
    }
  }
} satisfies Meta<typeof Carousel>;

export default meta;
type Story = StoryObj<typeof meta>;

const BaseStory: Story = {
  args: {
    items: Array.from({ length: 10 }).map((_, index) => ({
      id: index,
      data: { title: `Item ${index + 1}` }
    })),
    renderItem: (item: CarouselItem<{ title: string }>) => (
      <div className="flex h-full w-full flex-col items-center justify-center p-16">
        <span className="text-lg font-medium">{item.data.title}</span>
      </div>
    )
  },
  render: (args) => <Carousel {...args} />
};

export const Default: Story = { ...BaseStory };

export const WithTitle: Story = {
  ...BaseStory,
  args: {
    ...BaseStory.args,
    title: 'Carousel Title',
    showPagination: true
  }
};

export const WithoutPagination: Story = {
  ...BaseStory,
  args: {
    ...BaseStory.args
  }
};

export const SmallSize: Story = {
  ...BaseStory,
  args: {
    ...BaseStory.args,
    size: 'small',
    showPagination: true
  }
};

export const MediumSize: Story = {
  ...BaseStory,
  args: {
    ...BaseStory.args,
    size: 'medium',
    showPagination: true
  }
};

export const LargeSize: Story = {
  ...BaseStory,
  args: {
    ...BaseStory.args,
    size: 'large',
    showPagination: true
  }
};

export const CustomSlidesPerView: Story = {
  ...BaseStory,
  args: {
    ...BaseStory.args,
    slidesPerView: 1,
    showPagination: true
  }
};
