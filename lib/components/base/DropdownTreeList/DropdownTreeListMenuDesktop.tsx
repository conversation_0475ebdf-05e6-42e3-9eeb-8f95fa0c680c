import { forwardRef, useMemo } from 'react';
import ReactDOM from 'react-dom';

import { cn, generateId, getPortalElement } from '@/utils';

import { DropdownTreeListMenuProps } from './DropdownTreeListMenu';

import {
  DROPDOWN_SEARCH_MAX_LENGTH,
  useDropdownContext,
  useDropdownScrollParent
} from '../Dropdown';
import InfiniteScroll from '../Dropdown/InfiniteScrollWrapper';
import { Input } from '../Input';
import { useUIKitModalsSelectors } from '../UIKitProvider';

export const DropdownTreeListMenuDesktop = forwardRef<
  HTMLUListElement,
  DropdownTreeListMenuProps
>(
  (
    {
      className,
      children,
      hasMore,
      showSearch,
      searchValue,
      showSelected,
      onLoadMore,
      onSearchChange,
      onSearchClear
    },
    ref
  ) => {
    const { isOpen, refs, floatingStyles, getFloatingProps } =
      useDropdownContext();
    const { portalClassName } = useUIKitModalsSelectors();

    const id = useMemo(() => generateId('DropdownMultiselectListDesktop'), []);
    const getScrollParent = useDropdownScrollParent(`#${id}`);

    if (!isOpen) {
      return null;
    }

    const handleLoadMore = () => {
      onLoadMore?.();
    };

    return ReactDOM.createPortal(
      <div
        className={cn(
          'z-[1070] flex max-h-[350px] w-full flex-col justify-center rounded-lg bg-surface-grey_0 p-8 shadow-dropdown focus-visible:outline-none focus-visible:ring-0 md:!bg-dropdown-background',
          className
        )}
        ref={refs.setFloating}
        style={floatingStyles}
        {...getFloatingProps()}
        role="listbox"
        aria-label="options"
        data-attributes="DropdownMultiselectList"
      >
        {showSearch && (
          <Input
            containerClassName="mb-4"
            showLabel={false}
            dataAttributes="DropdownMultiselectSearch"
            size="Medium"
            placeholder="Search"
            leftIcon="Search_light"
            value={searchValue}
            showClear={true}
            autoFocus
            maxLength={DROPDOWN_SEARCH_MAX_LENGTH}
            onChange={onSearchChange}
            onClear={onSearchClear}
          />
        )}
        <ul
          ref={ref}
          id={id}
          className="mb-[42px] w-full justify-center overflow-y-auto"
        >
          {showSelected ? (
            children
          ) : (
            <InfiniteScroll
              pageStart={0}
              loadMore={handleLoadMore}
              hasMore={hasMore}
              loader={<div className="flex flex-auto" key={0}></div>}
              useWindow={false}
              getScrollParent={getScrollParent}
            >
              {children}
            </InfiniteScroll>
          )}
        </ul>
      </div>,
      getPortalElement(portalClassName)
    );
  }
);
