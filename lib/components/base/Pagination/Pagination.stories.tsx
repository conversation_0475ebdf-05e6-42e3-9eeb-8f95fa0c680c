import {
  HistoryAdaptedRouter,
  IHistory4,
  useUuiServices,
  UuiContext
} from '@epam/uui-core';
import type { Meta, StoryObj } from '@storybook/react';
import { createBrowserHistory } from 'history';
import noop from 'lodash/noop';

import { Pagination } from './Pagination';

const Template = (args: React.ComponentProps<typeof Pagination>) => {
  const history = createBrowserHistory();
  const router = new HistoryAdaptedRouter(history as unknown as IHistory4);
  const { services } = useUuiServices({ router });
  return (
    <UuiContext.Provider value={services}>
      <Pagination {...args} />
    </UuiContext.Provider>
  );
};

const meta = {
  title: 'Design System/Components/Pagination/Pagination',
  component: Template,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered'
  },
  args: {
    className: undefined,
    totalPages: 10,
    currentPage: 5,
    onChange: noop,
    dataAttributes: 'Pagination'
  },
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS class name for custom styling'
    },
    totalPages: {
      control: { type: 'number', min: 1, max: 100 },
      description: 'Total number of pages available'
    },
    currentPage: {
      control: { type: 'number', min: 1, max: 100 },
      description: 'Current active page number'
    },
    onChange: {
      action: 'page changed',
      description: 'Callback function called when page changes'
    },
    dataAttributes: {
      control: 'text',
      description: 'Custom data attribute for testing and identification'
    }
  }
} satisfies Meta<typeof Pagination>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: 'Default',
  args: {
    ...meta.args
  }
};

export const FirstPage: Story = {
  name: 'First Page',
  args: {
    ...meta.args,
    totalPages: 15,
    currentPage: 1
  }
};

export const LastPage: Story = {
  name: 'Last Page',
  args: {
    ...meta.args,
    totalPages: 8,
    currentPage: 8
  }
};

export const ManyPages: Story = {
  name: 'Many Pages',
  args: {
    ...meta.args,
    totalPages: 50,
    currentPage: 25
  }
};

export const FewPages: Story = {
  name: 'Few Pages',
  args: {
    ...meta.args,
    totalPages: 3,
    currentPage: 2
  }
};

export const SinglePage: Story = {
  name: 'Single Page',
  args: {
    ...meta.args,
    totalPages: 1,
    currentPage: 1
  }
};
