import { FC } from 'react';

import clsx from 'clsx';

import { useIsOverflowed } from '@/hooks/useIsOverflowed';

import { CustomFile, CustomFileStatuses } from '../types';
import { formatFileSize, getFileExtension } from '../utils';

import { ButtonVariant, IconButton } from '../../Button';
import { FileIcon, FileIconName } from '../../FileIcon';
import { Tooltip, TooltipVariant } from '../../Tooltip';

interface UploadedFileRowProps {
  className?: string;
  file: CustomFile;
  viewOnly?: boolean;
  isDownloadBtnDisabled?: boolean;
  isRemoveBtnDisabled?: boolean;
  onRemove: (
    file: CustomFile,
    event?: unknown
  ) => void | boolean | Promise<void | boolean>;
  onRetryUpload: (
    file: CustomFile,
    event?: unknown
  ) => void | boolean | Promise<void | boolean>;
  onDownload?: (file: CustomFile) => void;
}

export const UploadedFileRow: FC<UploadedFileRowProps> = ({
  className,
  file,
  viewOnly,
  isDownloadBtnDisabled,
  isRemoveBtnDisabled,

  onRemove,
  onRetryUpload,
  onDownload
}) => {
  const fileSizeObj = formatFileSize(file.size || 0);

  const { overflowContainerRef, isOverflowed } =
    useIsOverflowed<HTMLDivElement>();

  return (
    <div
      data-attributes="UploadedFileRow"
      className={clsx('flex items-center gap-12 px-20 py-16', className)}
    >
      <div className="flex min-w-0 flex-1 items-center gap-12">
        <FileIcon
          extension={getFileExtension(file.name) as FileIconName}
          className="h-[28px] w-[28px]"
        />

        <Tooltip
          variant={TooltipVariant.Dark}
          title={isOverflowed && file.name}
          className="!rounded-4 !max-w-[210px] !px-8 !py-4 text-body-4-regular [overflow-wrap:anywhere]"
          classNameTrigger="flex-1 truncate "
        >
          <div
            ref={overflowContainerRef}
            className="truncate text-body-1-medium text-text-heading"
          >
            {file.name}
          </div>
        </Tooltip>
        <span className="text-body-2-regular text-text-heading">
          {file.status === CustomFileStatuses.FAILURE
            ? 'Upload Failure'
            : fileSizeObj.total + ' ' + fileSizeObj.size.toUpperCase()}
        </span>
      </div>

      {!viewOnly && (
        <div className="flex items-center gap-8">
          <IconButton
            variant={ButtonVariant.Tertiary}
            icon={
              file.status === CustomFileStatuses.FAILURE
                ? 'Refresh_light'
                : 'Download_light'
            }
            disabled={
              file.status !== CustomFileStatuses.FAILURE &&
              isDownloadBtnDisabled
            }
            onClick={(e) =>
              file.status === CustomFileStatuses.FAILURE
                ? onRetryUpload(file, e)
                : onDownload?.(file)
            }
            className="min-w-[40px] rounded-[12px] [&>svg]:h-[24px] [&>svg]:w-[24px]"
          />

          {!file.isRemoveBtnNotVisible && (
            <IconButton
              variant={ButtonVariant.Tertiary}
              disabled={isRemoveBtnDisabled}
              icon={
                file.status === CustomFileStatuses.FAILURE
                  ? 'Close_round_light'
                  : 'Trash_light'
              }
              onClick={(e) => onRemove(file, e)}
              className="min-w-[40px] rounded-[12px] [&>svg]:h-[24px] [&>svg]:w-[24px]"
            />
          )}
        </div>
      )}
    </div>
  );
};
