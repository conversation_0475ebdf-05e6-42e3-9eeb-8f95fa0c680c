import { Controller, FieldValues, Path, useFormContext } from 'react-hook-form';

import {
  NumericRatingInput,
  NumericRatingInputProps
} from './NumericRatingInput';

import { DataAttributesProps } from '../types';

import './NumericRating.styles.scss';

export type NumericRatingProps<T extends FieldValues = FieldValues> =
  NumericRatingInputProps &
    DataAttributesProps & {
      name?: Path<T>;
      rules?: object;
    };

export const NumericRating = <T extends FieldValues>({
  name,
  rules,
  dataAttributes = 'NumericRating',
  ...props
}: NumericRatingProps<T>) => {
  const formContext = useFormContext<T>();

  const renderNumericRating = (field?: {
    value?: number;
    onChange?: (value: number) => void;
  }) => {
    const currentValue = field?.value ?? props.value;

    return (
      <NumericRatingInput
        {...props}
        value={currentValue}
        onChange={(value) => {
          field?.onChange?.(value);
          props.onChange?.(value);
        }}
        dataAttributes={dataAttributes}
      />
    );
  };

  if (formContext?.control && name) {
    return (
      <Controller
        name={name}
        control={formContext.control}
        rules={rules}
        render={({ field }) => renderNumericRating(field)}
      />
    );
  }

  return renderNumericRating();
};
