import { FC, HTMLAttributes } from 'react';

import clsx from 'clsx';
import partial from 'lodash/partial';

import { NumericRatingCellVariant } from './constants';

import { Tooltip, TooltipProps } from '../Tooltip';

import './NumericRating.styles.scss';

type NumericRatingCellProps = Omit<
  HTMLAttributes<HTMLButtonElement>,
  'onClick' | 'onMouseEnter'
> & {
  className?: string;
  value: number;
  variant: NumericRatingCellVariant;
  disabled?: boolean;
  tooltip?: TooltipProps;
  onClick?: (value: number) => void;
  onMouseEnter?: (value: number) => void;
};

const BUTTON_CLASS_NAMES =
  'relative h-20 w-40 text-label-xs-regular active:!border-none disabled:pointer-events-none';

export const NumericRatingCell: FC<NumericRatingCellProps> = ({
  className,
  value,
  variant,
  disabled,
  tooltip,
  onClick,
  onMouseEnter,
  ...props
}) => {
  const renderCell = () => {
    return (
      <div
        data-attributes="NumericRatingCell"
        className="ot-numeric-rating-cell"
      >
        <button
          className={clsx(
            BUTTON_CLASS_NAMES,
            'numeric-rating__cell',
            `numeric-rating__cell--${variant}`,
            className
          )}
          type="button"
          onClick={onClick ? partial(onClick, value) : undefined}
          onMouseEnter={onMouseEnter ? partial(onMouseEnter, value) : undefined}
          disabled={disabled}
          {...props}
        >
          {value}
        </button>
      </div>
    );
  };

  if (tooltip && tooltip.title) {
    return <Tooltip {...tooltip}>{renderCell()}</Tooltip>;
  }

  return renderCell();
};
