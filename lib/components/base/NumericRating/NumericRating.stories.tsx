import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { action } from '@storybook/addon-actions';
import type { Meta, StoryObj } from '@storybook/react';

import { NumericRating as NumericRatingComponent } from './NumericRating';

import { Button } from '../Button';

const NumericRating = (
  args: React.ComponentProps<typeof NumericRatingComponent>
) => {
  const [value, setValue] = useState(args.value);

  useEffect(() => {
    setValue(args.value);
  }, [args.value]);

  return (
    <NumericRatingComponent
      {...args}
      value={value}
      outdated={args.outdated && args.value === value}
      onChange={(e) => setValue(e)}
    />
  );
};

const meta = {
  title: 'Design System/Components/Numeric Rating/Numeric Rating',
  component: NumericRating,
  tags: ['autodocs', 'Forms', 'Stable'],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'The Numeric Rating Component enables users to select a numeric rating ranging from 1 to 5. It is designed for scenarios such as self-evaluation or setting ratings against defined benchmarks. The component displays values sequentially as interactive boxes, e.g., [1], [2], [3], [4], [5], with each value having specific states and behaviors based on user interaction and defined criteria.'
      }
    }
  },
  args: {
    value: 3,
    maxRating: 5,
    target: undefined,
    outdated: false,
    disabled: false,
    stayOn: false,
    name: undefined,
    dataAttributes: 'NumericRating'
  },
  argTypes: {
    maxRating: { control: { type: 'number', min: 0, max: 5 } },
    value: { control: { type: 'number', min: 0, max: 5 } },
    previousSelected: { control: { type: 'number', min: 0, max: 5 } },
    target: { control: { type: 'number', min: 1, max: 5 } },
    outdated: { control: 'boolean' },
    disabled: { control: 'boolean' },
    stayOn: { control: 'boolean' },
    resetValue: { control: 'number' },
    name: {
      control: 'text',
      description: 'Form field name for react-hook-form integration'
    },
    rules: {
      control: 'object',
      description: 'Validation rules for react-hook-form'
    },
    dataAttributes: {
      control: 'text',
      description: 'Data attributes for testing'
    },
    onChange: action('onChange')
  }
} satisfies Meta<typeof NumericRating>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const EmptyState: Story = {
  name: 'No Rating',
  args: {
    value: 0
  }
};

export const SelectedState: Story = {
  name: 'Set Rating',
  render: (args) => {
    return (
      <div className="flex flex-col gap-16">
        <NumericRating {...args} value={0} />
        <NumericRating {...args} value={1} />
        <NumericRating {...args} value={2} />
        <NumericRating {...args} value={3} />
        <NumericRating {...args} value={4} />
        <NumericRating {...args} value={5} />
      </div>
    );
  }
};

export const Target: Story = {
  name: 'Set Target',
  render: (args) => {
    return (
      <div className="flex flex-col gap-16">
        <NumericRating {...args} value={0} target={4} />
        <NumericRating {...args} value={1} target={4} />
        <NumericRating {...args} value={2} target={4} />
        <NumericRating {...args} value={3} target={4} />
        <NumericRating {...args} value={4} target={4} />
        <NumericRating {...args} value={5} target={4} />
      </div>
    );
  }
};

export const Outdated: Story = {
  name: 'Outdated Target',
  render: (args) => {
    return (
      <div className="flex flex-col gap-16">
        <NumericRating {...args} value={0} outdated target={4} />
        <NumericRating {...args} value={1} outdated target={4} />
        <NumericRating {...args} value={2} outdated target={4} />
        <NumericRating {...args} value={3} outdated target={4} />
        <NumericRating {...args} value={4} outdated target={4} />
        <NumericRating {...args} value={5} outdated target={4} />
      </div>
    );
  }
};

export const Disabled: Story = {
  name: 'Disabled',
  render: (args) => {
    return (
      <div className="flex flex-col gap-16">
        <NumericRating {...args} value={0} disabled outdated target={4} />
        <NumericRating {...args} value={1} disabled outdated target={4} />
        <NumericRating {...args} value={2} disabled outdated target={4} />
        <NumericRating {...args} value={3} disabled outdated target={4} />
        <NumericRating {...args} value={4} disabled outdated target={4} />
        <NumericRating {...args} value={5} disabled outdated target={4} />
        <NumericRating {...args} value={0} disabled target={4} />
        <NumericRating {...args} value={1} disabled target={4} />
        <NumericRating {...args} value={2} disabled target={4} />
        <NumericRating {...args} value={3} disabled target={4} />
        <NumericRating {...args} value={4} disabled target={4} />
        <NumericRating {...args} value={5} disabled target={4} />
        <NumericRating {...args} value={0} disabled />
        <NumericRating {...args} value={1} disabled />
        <NumericRating {...args} value={2} disabled />
        <NumericRating {...args} value={3} disabled />
        <NumericRating {...args} value={4} disabled />
        <NumericRating {...args} value={5} disabled />
      </div>
    );
  }
};

export const Tooltip: Story = {
  name: 'Tooltip',
  args: {
    tooltips: [
      {
        title:
          'Expected to demonstrate sound academic knowledge and understanding of basic concepts. The employee can contribute, explain, work within guidelines and describe terminologies and interdependencies between processes'
      },
      {
        title:
          'Expected to be at fundamental working proficiency level. Employee has experience of performing relevant activities and can demonstrate carrying out range of activities and straightforward tasks to the required standard under supervision.'
      },
      {
        title:
          'Expected to demonstrate the ability to carry out standard relevant tasks and basic troubleshooting confidently, consistently and independently without supervision. Employee supervises others in carrying out basic activities. For complex nonstandard tasks, employee will likely need to seek guidance.'
      },
      {
        title:
          'Expected to demonstrate a high skill level in carrying out more complex and broad-based activities that are nonstandard or specialized and does so confidently and consistently. Expected to suggest and drive alternative approaches, is analytical, makes decisions that influence project (initiative) success, provide guidance, instructions, advice and coaching to others'
      },
      {
        title:
          'Expected to be widely recognized by the organization as an authority in their area.  Expected to demonstrate the ability to influence stakeholders, define standards, drive change and deploy strategies and decisions to solve organization-wide problems'
      }
    ]
  },
  render: (args) => {
    return (
      <div className="flex flex-col gap-16">
        <NumericRating {...args} />
      </div>
    );
  }
};

const ReactHookFormTemplate = (
  args: React.ComponentProps<typeof NumericRatingComponent>
) => {
  const methods = useForm({
    defaultValues: {
      createTrust: 0,
      deliverResults: 0,
      actSafely: 0
    }
  });

  const onSubmit = (data: unknown) => {
    alert(JSON.stringify(data));
  };
  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <h3 className="text-header-3-medium">Behavioral Skills</h3>
        <div className="flex min-w-[400px] flex-auto flex-col items-start gap-4 pb-12">
          <div className="flex w-full items-center justify-between">
            <span className="text-label-m1-regular text-text-body">
              Create Trust
            </span>
            <NumericRatingComponent {...args} name="createTrust" />
          </div>
          <div className="flex w-full items-center justify-between">
            <span className="text-label-m1-regular text-text-body">
              Deliver Results
            </span>
            <NumericRatingComponent {...args} name="deliverResults" />
          </div>
          <div className="flex w-full items-center justify-between">
            <span className="text-label-m1-regular text-text-body">
              Act Safely
            </span>
            <NumericRatingComponent {...args} name="actSafely" />
          </div>
        </div>
        <Button type="submit" className="py-2 rounded">
          Submit
        </Button>
      </form>
    </FormProvider>
  );
};

export const FormIntegration: Story = {
  name: 'React Hook Form',
  render: ReactHookFormTemplate,
  parameters: {
    docs: {
      source: {
        code: `
const ReactHookFormTemplate = (
  args: React.ComponentProps<typeof NumericRatingComponent>
) => {
  const methods = useForm({
    defaultValues: {
      createTrust: 0,
      deliverResults: 0,
      actSafely: 0
    }
  });

  const onSubmit = (data: unknown) => {
    alert(JSON.stringify(data));
  };
  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <h3 className="text-header-3-medium">Behavioral Skills</h3>
        <div className="flex min-w-[400px] flex-auto flex-col items-start gap-4 pb-12">
          <div className="flex w-full items-center justify-between">
            <span className="text-label-m1-regular text-text-body">
              Create Trust
            </span>
            <NumericRating {...args} name="createTrust" />
          </div>
          <div className="flex w-full items-center justify-between">
            <span className="text-label-m1-regular text-text-body">
              Deliver Results
            </span>
            <NumericRating {...args} name="deliverResults" />
          </div>
          <div className="flex w-full items-center justify-between">
            <span className="text-label-m1-regular text-text-body">
              Act Safely
            </span>
            <NumericRating {...args} name="actSafely" />
          </div>
        </div>
        <Button type="submit" className="py-2 rounded">
          Submit
        </Button>
      </form>
    </FormProvider>
  );
};`
      }
    }
  }
};
