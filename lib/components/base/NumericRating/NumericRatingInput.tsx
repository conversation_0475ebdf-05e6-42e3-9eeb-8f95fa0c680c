import React, {
  Fragment,
  memo,
  ReactElement,
  useCallback,
  useState
} from 'react';

import clsx from 'clsx';

import { NumericRatingCell } from './NumericRatingCell';
import { getCellVariant } from './utils';

import { TooltipProps } from '../Tooltip';
import './NumericRating.styles.scss';

export type NumericRatingInputProps = {
  name?: string;
  value?: number;
  maxRating?: number;
  className?: string;

  /** Previously selected level to show the diff between prev and current values **/
  previousSelected?: number;
  tooltips?: TooltipProps[];
  descriptions?: (string | ReactElement)[];
  disabled?: boolean;
  target?: number;
  outdated?: boolean;
  onChange?: (value: number) => void;
  /** Flag that is responsible for preventing disabling (editing to zero) current level **/
  stayOn?: boolean;
  /** The value that is reset to when selected level is clicked again, default value is zero **/
  resetValue?: number;
  dataAttributes?: string;
};

export const NumericRatingInput: React.FC<NumericRatingInputProps> = memo(
  ({
    value = 0,
    maxRating = 5,
    previousSelected,
    stayOn = false,
    resetValue,
    tooltips,
    target,
    disabled,
    className,
    outdated,
    dataAttributes,
    onChange
  }) => {
    const [hoveredRating, setHoveredRating] = useState<number>(0);

    const handleMouseEnter = useCallback(
      (level: number) => {
        setHoveredRating(level);
      },

      []
    );

    const handleMouseLeave = useCallback(() => {
      setHoveredRating(0);
    }, []);

    const handleClick = useCallback(
      (newValue: number) => {
        if (onChange) {
          onChange(value === newValue ? (resetValue ?? 0) : newValue);
        }
      },

      [onChange, resetValue, value]
    );

    return (
      <div
        className={clsx(
          'flex h-min gap-4',
          {
            'pointer-events-none': disabled
          },

          className
        )}
        data-attributes={dataAttributes}
      >
        {Array.from(
          {
            length: maxRating
          },

          (_, index) => {
            const level = index + 1;

            const variant = getCellVariant({
              target,
              hoveredRating,
              selectedRating: value,
              previousSelected,
              level,
              outdated,
              readonly: disabled
            });

            return (
              <Fragment key={index}>
                <NumericRatingCell
                  key={level}
                  value={level}
                  variant={variant}
                  tooltip={tooltips?.[index]}
                  onClick={handleClick}
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                  className={clsx({
                    'pointer-events-none': stayOn && value === level
                  })}
                />
                {target === level && (
                  <div className="h-20 w-[2px] rounded-[4px] bg-text-heading" />
                )}
              </Fragment>
            );
          }
        )}
      </div>
    );
  }
);
