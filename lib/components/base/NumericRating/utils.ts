import { NumericRatingCellVariant } from './constants';

export const getCellVariant = ({
  target = 0,
  hoveredRating,
  selectedRating,
  previousSelected,
  level,
  outdated,
  readonly
}: {
  target?: number;
  hoveredRating: number;
  selectedRating: number;
  previousSelected?: number;
  level: number;
  outdated?: boolean;
  readonly?: boolean;
}): NumericRatingCellVariant => {
  if (hoveredRating) {
    if (level > hoveredRating) {
      return NumericRatingCellVariant.Default;
    }
    if (hoveredRating >= target) {
      return NumericRatingCellVariant.Success;
    }

    return NumericRatingCellVariant.Warning;
  }

  if (!selectedRating || level > selectedRating) {
    return readonly
      ? NumericRatingCellVariant.Disabled
      : NumericRatingCellVariant.Default;
  }

  if (outdated) {
    return NumericRatingCellVariant.Outdated;
  }

  if (previousSelected) {
    if (
      (level < previousSelected && selectedRating < previousSelected) ||
      level > previousSelected
    ) {
      return NumericRatingCellVariant.Outdated;
    }
  }

  if (selectedRating < target) {
    return NumericRatingCellVariant.Warning;
  }

  return NumericRatingCellVariant.Success;
};
