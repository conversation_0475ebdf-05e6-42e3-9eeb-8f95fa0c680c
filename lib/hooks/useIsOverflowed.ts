import { useCallback, useLayoutEffect, useRef, useState } from 'react';

export const useIsOverflowed = <T extends HTMLElement>() => {
  const overflowContainerRef = useRef<T>(null);

  const [isOverflowedVertically, setOverflowedVertically] = useState(false);
  const [isOverflowedHorizontally, setOverflowedHorizontally] = useState(false);

  const handleResize = useCallback(() => {
    const element = overflowContainerRef.current;

    if (!element) return;

    setOverflowedVertically(element.offsetHeight < element.scrollHeight);
    setOverflowedHorizontally(element.offsetWidth < element.scrollWidth);
  }, []);

  useLayoutEffect(() => {
    const element = overflowContainerRef.current;

    if (!element) return;

    const observer = new ResizeObserver(handleResize);

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [handleResize]);

  return {
    overflowContainerRef,
    isOverflowedVertically,
    isOverflowedHorizontally,
    isOverflowed: isOverflowedVertically || isOverflowedHorizontally
  };
};
