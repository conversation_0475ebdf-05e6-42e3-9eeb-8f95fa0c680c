@echo off

REM === Check for container runtime
set _command=docker
where /q %_command%
if errorlevel 1 (
  set _command=podman
)
echo Runtime ot be used: %_command%
REM ==============

set tag=%1
set image=one-talent/be-feedback-service

if not defined tag set tag=0.0.1-beta

echo %image%:%tag% will be used

%_command% build ../../ ^
  --file ../../src/WebAPI/Dockerfile-local ^
  --tag %image%:%tag% ^
  --build-arg ADNOC_FEED_TOKEN=%ADNOC_FEED_TOKEN% ^
	--build-arg VERSION=%tag% ^
  --network=host
