@echo off

REM === Check for container runtime
set _command=docker
where /q %_command%
if errorlevel 1 (
  set _command=podman
)
echo Runtime to be used: %_command%
REM ==============

set MSSQL_SA_PASSWORD=P@ssword11

set tag=%1
set image=one-talent/be-feedback-service

if not defined tag set tag=0.0.1-beta

echo %image%:%tag% will be used

echo Starting service. URL: http://localhost:8080/swagger

%_command% compose ^
  -f ../../compose.yml ^
  up ^
  --abort-on-container-exit
