### Check for container runtime
_command=podman
if ! command -v $_command &> /dev/null
then
    _command=podman
fi
echo Runtime to be used: $_command
#################

tag=0.0.1-beta
image=one-talent/be-atr-admin-service

echo $image:$tag will be used

$_command build ../../ \
  --file ../../src/WebAPI/Dockerfile-local \
  --tag $image:$tag \
  --build-arg ADNOC_FEED_TOKEN=$ADNOC_FEED_TOKEN \
  --build-arg VERSION=$tag \
  --network=host