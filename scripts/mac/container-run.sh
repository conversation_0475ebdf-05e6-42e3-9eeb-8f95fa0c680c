### Check for container runtime
_command=podman
if ! command -v $_command &> /dev/null
then
    _command=podman
fi
echo Runtime to be used: $_command
#################

export MSSQL_SA_PASSWORD=P@ssword11

tag=0.0.1-beta
image=one-talent/be-feedback-service

echo $image:$tag will be used

echo Starting service. URL: http://localhost:8080/swagger

$_command compose \
  -f ../../compose.yml \
  up \
  --abort-on-container-exit