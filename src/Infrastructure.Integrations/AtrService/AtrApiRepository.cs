using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Repositories;
using OneTalent.AtrService.Client;
using OneTalent.Common.Extensions.Paging;
using PaginationData = OneTalent.Common.Extensions.Paging.PaginationData;

namespace OneTalent.AtrAdminService.Infrastructure.Integrations.AtrService;

public class AtrApiRepository(
    IAtrServiceClient atrServiceClient
) : IAtrApiRepository
{
    public async Task<PerformanceFormStatus[]> GetAssessmentStatusesAsync(CancellationToken cancellationToken)
    {
        var statuses = await atrServiceClient.V1ExposureServicePerformanceFormsStatusesAsync(cancellationToken);

        return statuses
            .ToPerformanceFormStatuses()
            .ToArray();
    }

    public async Task<PagedListResult<AssessmentForm>> SearchAssessmentsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken)
    {
        var query = searchQuery.ToPerformanceFormSearchQuery(employeeId);
        var result = await atrServiceClient.V1ExposureServicePerformanceFormsSearchAsync(query, cancellationToken);

        var mappedForms = result.Items.Select(
            x => x.ToAssessmentForm())
            .ToArray();

        return new PagedListResult<AssessmentForm>(new OffsetPage(result.Paging.PageNumber, result.Paging.PageSize), mappedForms, result.Paging.TotalResults ?? 0);
    }

    public async Task<PagedListResult<PerformanceFormHistoryRecord>> GetPerformanceFormHistoryAsync(PerformanceFormHistoryQuery query, CancellationToken cancellationToken)
    {
        var history = await atrServiceClient.V1ExposureServicePerformanceFormsHistoryAsync(
            query.PerformanceFormId.ToString(), 
            new GetPerformanceFormHistoryRequestV1(
                null, 
                query.PageNumber, 
                query.PageSize
            ),cancellationToken);

        return new PagedListResult<PerformanceFormHistoryRecord>(
               new PaginationData(
                   history.Paging.PageNumber, 
                   history.Paging.PageSize, 
                   history.Paging.Count,
                   history.Paging.TotalResults),
               history.Items.Select(Mappers.ToPerformanceFormHistory));
    }
}
