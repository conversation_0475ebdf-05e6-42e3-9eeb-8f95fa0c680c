using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.Contracts.Constants;
using OneTalent.AtrService.Client;
using OneTalent.Common.Extensions.ItemId;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace OneTalent.AtrAdminService.Infrastructure.Integrations.AtrService;

internal static class Mappers
{
    public static IEnumerable<PerformanceFormStatus> ToPerformanceFormStatuses(this IReadOnlyCollection<StatusDto> statuses) =>
        statuses.Select(x => new PerformanceFormStatus(
            Id: MapStatusEnum(x.Id),
            Name: x.Name)
        );

    public static PerformanceFormStatuses MapStatusEnum(Statuses status) =>
        status switch
        {
            Statuses.Draft => PerformanceFormStatuses.Draft,
            Statuses.SelfAssessmentNotStarted => PerformanceFormStatuses.SelfAssessmentNotStarted,
            Statuses.SelfAssessmentInProgress => PerformanceFormStatuses.SelfAssessmentInProgress,
            Statuses.ManagerAssessmentNotStarted => PerformanceFormStatuses.ManagerAssessmentNotStarted,
            Statuses.ManagerAssessmentInProgress => PerformanceFormStatuses.ManagerAssessmentInProgress,
            Statuses.ManagerAssessmentCompleted => PerformanceFormStatuses.ManagerAssessmentCompleted,
            Statuses.DottedLineManagerEndorsementNotStarted => PerformanceFormStatuses.DottedLineManagerEndorsementNotStarted,
            Statuses.SecondLineManagerEndorsementNotStarted => PerformanceFormStatuses.SecondLineManagerEndorsementNotStarted,
            Statuses.SecondLineManagerEndorsementCompleted => PerformanceFormStatuses.SecondLineManagerEndorsementCompleted,
            Statuses.NormalizationNotStarted => PerformanceFormStatuses.NormalizationNotStarted,
            Statuses.NormalizationInProgress => PerformanceFormStatuses.NormalizationInProgress,
            Statuses.NormalizationCompleted => PerformanceFormStatuses.NormalizationCompleted,
            Statuses.RatingApprovalPending => PerformanceFormStatuses.RatingApprovalPending,
            Statuses.RatingApprovalApproved => PerformanceFormStatuses.RatingApprovalApproved,
            Statuses.Completed => PerformanceFormStatuses.Completed,
            _ => throw new InvalidOperationException($"Unsupported Statuses value: {status}")
        };

    public static PerformanceFormMinorStatus MapMinorEnum(PerformanceFormInfoStatus status) =>
       status switch
       {
           PerformanceFormInfoStatus.Empty => PerformanceFormMinorStatus.Empty,
           PerformanceFormInfoStatus.NotCompleted => PerformanceFormMinorStatus.NotCompleted,
           PerformanceFormInfoStatus.InProgress => PerformanceFormMinorStatus.InProgress,
           PerformanceFormInfoStatus.Completed => PerformanceFormMinorStatus.Completed,
           _ => throw new InvalidOperationException($"Unsupported Statuses value: {status}")
       };

    public static AssessmentSearchQuery ToPerformanceFormSearchQuery(this PerformanceFormSearchQuery searchQuery, string employeeId) =>
       new(
           employeeId: employeeId,
           formIds: searchQuery.FormIds,
           searchEmployees: searchQuery.SearchEmployees,
           selfAssessmentStatuses: searchQuery.SelfAssessmentStatuses,
           performanceFormStatuses: searchQuery.PerformanceFormStatuses,
           pageNumber: searchQuery.PageNumber,
           pageSize: searchQuery.PageSize,
           orderBy: searchQuery.OrderBy is not null ? new OrderBy((SortDirection)searchQuery.OrderBy.Direction, searchQuery.OrderBy.Field) : null
       );

    public static AssessmentForm ToAssessmentForm(this AdminAssessmentInfo source) =>
        new () {
            Id = source.Assessment?.Id,
            TemplateId = null,
            TemplateName = null,
            MajorStatus = source.Assessment?.PerformanceFormStatusId,
            MinorStatus = MapMinorEnum(source.SelfAssessmentStatus),
            EmployeeId = source.Subordinate.EmployeeId,
            EmployeeFullName = null,
            CompanyCode = null,
            CompanyName = null,
            DirectorateId = null,
            DirectorateName = null,
            FunctionId = null,
            FunctionName = null,
            DivisionId = null,
            DivisionName = null,
            AtrGroupName = null,
            AssessmentLineManagerId = source.Assessment?.AssessmentLineManagerId,
            AssessmentLineManagerName = null,
            AssessmentB2BManagerId = source.Assessment?.AssessmentB2BManagerId,
            AssessmentB2BManagerName = null,
            DottedLineManagerId = source.Assessment?.DottedLineManagerId,
            DottedLineManagerName = null,
            LastUpdated = DateTimeOffset.UtcNow,
            UpdatedBy = null
        };
    public static PerformanceFormHistoryRecord ToPerformanceFormHistory(this PerformanceFormHistoryDtoV1 history) =>
        new (
            new ItemId(history.PerformanceFormId),
            history.ActionDateTime,
            history.ActionType,
            history.ActionOwnerId,
            history.ActionOwnerName,
            history.MajorStatusTo,
            history.MinorStatusTo,
            history.MajorStatusFrom,
            history.MinorStatusFrom,
            history.ActionReason,
            history.ActionOwnerJobRole,
            history.GroupCompany,
            history.Directorate,
            history.Function,
            history.Division,
            history.PerformanceCycleName,
            history.AssessmentLineManagerName,
            history.AssessmentB2BManagerName,
            history.DottedLineManagerName,
            history.CurrentLineManager,
            history.ManagerRating,
            history.ManagerRatingName,
            history.ManagerNineBoxPotential,
            history.NineBoxPotentialRating,
            history.NormalizedRating,
            history.NormalizedRatingName,
            history.NormalizedNineBoxPotential,
            history.WorkflowId,
            history.WfAssigneeName);
}
