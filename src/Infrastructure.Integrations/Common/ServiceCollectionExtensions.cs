using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OneTalent.AtrAdminService.Application.PerformanceForms.Repositories;
using OneTalent.AtrAdminService.Infrastructure.Integrations.AtrService;
using OneTalent.AtrAdminService.Application.Employees.Queries.Repositories;
using OneTalent.AtrAdminService.Infrastructure.Integrations.DataSyncService.Repositories;
using OneTalent.AtrService.WebAPI.Client;

namespace OneTalent.AtrAdminService.Infrastructure.Integrations.Common;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection RegisterInfrastructureIntegrationsServices(
        this IServiceCollection services,
        IConfiguration configuration
    ) =>
        services
            .RegisterQueryServiceIntegrationServices(configuration)
            .RegisterAtrServiceIntegrationServices(configuration);

    public static IServiceCollection RegisterQueryServiceIntegrationServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddScoped(provider =>
            GetServiceImplementation<IEmployeeApiRepository, EmployeeApiMockRepository, EmployeeApiRepository>(
                provider,
                configuration));

        return services;
    }

    public static IServiceCollection RegisterAtrServiceIntegrationServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddAtrExternalAuthClient();

        services.AddScoped(provider =>
            GetServiceImplementation<IAtrApiRepository, AtrApiMockRepository, AtrApiRepository>(
                provider,
                configuration));

        return services;
    }

    private static TService GetServiceImplementation<TService, TMockImplementation, TImplementation>(
       IServiceProvider provider,
       IConfiguration configuration)
       where TMockImplementation : TService
       where TImplementation : TService
    {
        var environment = configuration.GetValue<string>(
            Application.Common.Constants.Constants.AspNetCoreEnvironmentVariableName);

        return environment == Environments.Development
            ? ActivatorUtilities.CreateInstance<TMockImplementation>(provider: provider)!
            : ActivatorUtilities.CreateInstance<TImplementation>(provider: provider)!;
    }
}
