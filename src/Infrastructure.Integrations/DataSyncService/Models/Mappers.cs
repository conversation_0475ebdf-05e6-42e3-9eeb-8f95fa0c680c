using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.DataSyncService.WebAPI.Client.DataSyncService;
using Employee = OneTalent.AtrAdminService.Application.Employees.Queries.Models.Employee;

namespace OneTalent.AtrAdminService.Infrastructure.Integrations.DataSyncService.Models;

internal static class Mappers
{
    public static IEmployee ToEmployee(this EmployeeDto source) => new Employee(
        EmployeeId: source.EmployeeId,
        CompanyCode: source.CompanyCode,
        CompanyName: source.CompanyName,
        FullNameEnglish: source.FullNameEnglish!,
        Email: source.Email!,
        JobTitle: source.JobTitle!,
        PositionName: source.PositionNameEnglish,
        LineManagerId: source.ManagerId1,
        B2BManagerId: source.B2BManagerId,
        PositionId: source.PositionId,
        IsManager: source.IsManager);

    public static IEmployee ToEmployee(this EmployeeDetails source) => new Employee(
        source.Employee.EmployeeId,
        "",
        source.Employee.CompanyName,
        source.Employee.FullName,
        source.Employee.Email,
        source.Employee.JobTitle,
        source.Employee.PositionName,
        source.Employee.LineManagerEmployeeId,
        null,
        null,
        source.IsManager);
}
