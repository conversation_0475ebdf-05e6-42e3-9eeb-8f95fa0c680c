using Microsoft.Extensions.Logging;
using OneTalent.AtrAdminService.Application.Employees;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Repositories;
using OneTalent.AtrAdminService.Infrastructure.Integrations.DataSyncService.Models;
using OneTalent.Common.Extensions.Exceptions;
using OneTalent.DataSyncService.WebAPI.Client.DataSyncService;

namespace OneTalent.AtrAdminService.Infrastructure.Integrations.DataSyncService.Repositories;

internal sealed class EmployeeApiRepository(
    IDataSyncServiceClient dataSyncServiceClient,
    ILogger<EmployeeApiRepository> logger
) : IEmployeeApiRepository
{
    public async Task<IEmployee> GetEmployeeByEmailAsync(EmployeeEmailFilter filter, CancellationToken cancellationToken)
    {
        try
        {
            var employeeModel = await dataSyncServiceClient.V1ExposureServiceEmployeesSearchGetAsync(
                id: null,
                email: filter.Email,
                excludeDeleted: true,
                cancellationToken: cancellationToken);
            var employee = employeeModel?.ToEmployee();

            return employee ?? new NotEmployee($"Email = {filter.Email}.");
        }
        catch (ItemNotFoundException)
        {
            logger.EmployeeNotFoundWarning(filter.Email);
            return new NotEmployee($"Email = {filter.Email}.");
        }
    }

    public async Task<IEmployee> GetEmployeeByIdAsync(
        EmployeeIdFilter filter,
        CancellationToken cancellationToken)
    {
        try
        {
            var employeeModel = await dataSyncServiceClient.V2ExposureServiceEmployeesSearchAsync(
                new GetEmployeesRequestV2(
                    [filter.EmployeeId],
                    filter.ExcludeTerminated,
                    true,
                    null,
                    1,
                    1,
                    null),
                cancellationToken);
            var employee = employeeModel.Items.FirstOrDefault()?.ToEmployee();

            return employee ?? new NotEmployee($"Id = {filter.EmployeeId}.");
        }
        catch (ItemNotFoundException)
        {
            logger.EmployeeNotFoundWarning(filter.EmployeeId);
            return new NotEmployee($"Id = {filter.EmployeeId}.");
        }
    }

    public async Task<ICollection<NamedOptions>> GetDirectoratesAllAsync(CancellationToken cancellationToken)
    {
        var directorates = await dataSyncServiceClient.V1ExposureServiceDirectoratesAllAsync(cancellationToken);
        return directorates.Items.Select(x => new NamedOptions(x.Id, x.Name)).ToArray();
    }

    public async Task<ICollection<NamedOptions>> GetFunctionsAllAsync(CancellationToken cancellationToken)
    {
        var functions = await dataSyncServiceClient.V1ExposureServiceFunctionsAllAsync(cancellationToken);
        return functions.Items.Select(x => new NamedOptions(x.Id, x.Name)).ToArray();
    }

    public async Task<ICollection<NamedOptions>> GetDivisionsAllAsync(CancellationToken cancellationToken)
    {
        var divisions = await dataSyncServiceClient.V1ExposureServiceDivisionsAllAsync(cancellationToken);
        return divisions.Items.Select(x => new NamedOptions(x.Id, x.Name)).ToArray();
    }

    public async Task<ICollection<NamedOptions>> GetCompaniesAllAsync(CancellationToken cancellationToken)
    {
        var companies = await dataSyncServiceClient.V1ExposureServiceCompaniesAllAsync(cancellationToken);
        return companies.Items.Select(x => new NamedOptions(x.Code, x.Name)).ToArray();
    }
}
