/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  CompanyPagedListResult,
  DepartmentPagedListResult,
  EmployeeFullInfoModel,
  GetCompaniesRequest,
  GetDepartmentsRequest,
  GetEmployeeDetailsRequest,
  GetPositionsRequestV2,
  PositionPagedListResult,
  ProblemDetails,
  SearchEmployeePagedListResult,
  SearchEmployeeRequest,
} from '../models/index';
import {
    CompanyPagedListResultFromJSON,
    CompanyPagedListResultToJSON,
    DepartmentPagedListResultFromJSON,
    DepartmentPagedListResultToJSON,
    EmployeeFullInfoModelFromJSON,
    EmployeeFullInfoModelToJSON,
    GetCompaniesRequestFromJSON,
    GetCompaniesRequestToJSON,
    GetDepartmentsRequestFromJSON,
    GetDepartmentsRequestToJSON,
    GetEmployeeDetailsRequestFromJSON,
    GetEmployeeDetailsRequestToJSON,
    GetPositionsRequestV2FromJSON,
    GetPositionsRequestV2ToJSON,
    PositionPagedListResultFromJSON,
    PositionPagedListResultToJSON,
    ProblemDetailsFromJSON,
    ProblemDetailsToJSON,
    SearchEmployeePagedListResultFromJSON,
    SearchEmployeePagedListResultToJSON,
    SearchEmployeeRequestFromJSON,
    SearchEmployeeRequestToJSON,
} from '../models/index';

export interface V1CompaniesAllPostRequest {
    getCompaniesRequest: GetCompaniesRequest;
}

export interface V1DepartmentsAllPostRequest {
    getDepartmentsRequest: GetDepartmentsRequest;
}

export interface V1EmployeesDetailsPostRequest {
    getEmployeeDetailsRequest: GetEmployeeDetailsRequest;
}

export interface V1EmployeesEmployeeIdFullDetailsGetRequest {
    employeeId: string;
}

export interface V1EmployeesSearchPostRequest {
    searchEmployeeRequest: SearchEmployeeRequest;
}

export interface V1PositionsAllPostRequest {
    getPositionsRequestV2: GetPositionsRequestV2;
}

/**
 * 
 */
export class EmployeeViewsApi extends runtime.BaseAPI {

    /**
     */
    async v1CompaniesAllPostRaw(requestParameters: V1CompaniesAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CompanyPagedListResult>> {
        if (requestParameters['getCompaniesRequest'] == null) {
            throw new runtime.RequiredError(
                'getCompaniesRequest',
                'Required parameter "getCompaniesRequest" was null or undefined when calling v1CompaniesAllPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/v1/companies/all`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GetCompaniesRequestToJSON(requestParameters['getCompaniesRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CompanyPagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async v1CompaniesAllPost(requestParameters: V1CompaniesAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CompanyPagedListResult> {
        const response = await this.v1CompaniesAllPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async v1DepartmentsAllPostRaw(requestParameters: V1DepartmentsAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DepartmentPagedListResult>> {
        if (requestParameters['getDepartmentsRequest'] == null) {
            throw new runtime.RequiredError(
                'getDepartmentsRequest',
                'Required parameter "getDepartmentsRequest" was null or undefined when calling v1DepartmentsAllPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/v1/departments/all`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GetDepartmentsRequestToJSON(requestParameters['getDepartmentsRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DepartmentPagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async v1DepartmentsAllPost(requestParameters: V1DepartmentsAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DepartmentPagedListResult> {
        const response = await this.v1DepartmentsAllPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async v1EmployeesDetailsPostRaw(requestParameters: V1EmployeesDetailsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmployeeFullInfoModel>> {
        if (requestParameters['getEmployeeDetailsRequest'] == null) {
            throw new runtime.RequiredError(
                'getEmployeeDetailsRequest',
                'Required parameter "getEmployeeDetailsRequest" was null or undefined when calling v1EmployeesDetailsPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/v1/employees/details`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GetEmployeeDetailsRequestToJSON(requestParameters['getEmployeeDetailsRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => EmployeeFullInfoModelFromJSON(jsonValue));
    }

    /**
     */
    async v1EmployeesDetailsPost(requestParameters: V1EmployeesDetailsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmployeeFullInfoModel> {
        const response = await this.v1EmployeesDetailsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async v1EmployeesEmployeeIdFullDetailsGetRaw(requestParameters: V1EmployeesEmployeeIdFullDetailsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmployeeFullInfoModel>> {
        if (requestParameters['employeeId'] == null) {
            throw new runtime.RequiredError(
                'employeeId',
                'Required parameter "employeeId" was null or undefined when calling v1EmployeesEmployeeIdFullDetailsGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/v1/employees/{employeeId}/full-details`;
        urlPath = urlPath.replace(`{${"employeeId"}}`, encodeURIComponent(String(requestParameters['employeeId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => EmployeeFullInfoModelFromJSON(jsonValue));
    }

    /**
     */
    async v1EmployeesEmployeeIdFullDetailsGet(requestParameters: V1EmployeesEmployeeIdFullDetailsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmployeeFullInfoModel> {
        const response = await this.v1EmployeesEmployeeIdFullDetailsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async v1EmployeesSearchPostRaw(requestParameters: V1EmployeesSearchPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<SearchEmployeePagedListResult>> {
        if (requestParameters['searchEmployeeRequest'] == null) {
            throw new runtime.RequiredError(
                'searchEmployeeRequest',
                'Required parameter "searchEmployeeRequest" was null or undefined when calling v1EmployeesSearchPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/v1/employees/search`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: SearchEmployeeRequestToJSON(requestParameters['searchEmployeeRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => SearchEmployeePagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async v1EmployeesSearchPost(requestParameters: V1EmployeesSearchPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<SearchEmployeePagedListResult> {
        const response = await this.v1EmployeesSearchPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async v1PositionsAllPostRaw(requestParameters: V1PositionsAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<PositionPagedListResult>> {
        if (requestParameters['getPositionsRequestV2'] == null) {
            throw new runtime.RequiredError(
                'getPositionsRequestV2',
                'Required parameter "getPositionsRequestV2" was null or undefined when calling v1PositionsAllPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/v1/positions/all`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GetPositionsRequestV2ToJSON(requestParameters['getPositionsRequestV2']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => PositionPagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async v1PositionsAllPost(requestParameters: V1PositionsAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<PositionPagedListResult> {
        const response = await this.v1PositionsAllPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
