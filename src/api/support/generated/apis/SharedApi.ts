/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  FeatureTogglesResponse,
} from '../models/index';
import {
    FeatureTogglesResponseFromJSON,
    FeatureTogglesResponseToJSON,
} from '../models/index';

/**
 * 
 */
export class SharedApi extends runtime.BaseAPI {

    /**
     */
    async v1SharedFeaturesGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<FeatureTogglesResponse>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/v1/shared/features`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => FeatureTogglesResponseFromJSON(jsonValue));
    }

    /**
     */
    async v1SharedFeaturesGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<FeatureTogglesResponse> {
        const response = await this.v1SharedFeaturesGetRaw(initOverrides);
        return await response.value();
    }

}
