/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ProblemDetails,
  UpsertFeatureRequest,
} from '../models/index';
import {
    ProblemDetailsFromJSON,
    ProblemDetailsToJSON,
    UpsertFeatureRequestFromJSON,
    UpsertFeatureRequestToJSON,
} from '../models/index';

export interface V1FeatureManagementPostRequest {
    upsertFeatureRequest: UpsertFeatureRequest;
}

/**
 * 
 */
export class FeatureManagementApi extends runtime.BaseAPI {

    /**
     */
    async v1FeatureManagementGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/v1/feature-management`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async v1FeatureManagementGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.v1FeatureManagementGetRaw(initOverrides);
    }

    /**
     */
    async v1FeatureManagementPostRaw(requestParameters: V1FeatureManagementPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['upsertFeatureRequest'] == null) {
            throw new runtime.RequiredError(
                'upsertFeatureRequest',
                'Required parameter "upsertFeatureRequest" was null or undefined when calling v1FeatureManagementPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/v1/feature-management`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: UpsertFeatureRequestToJSON(requestParameters['upsertFeatureRequest']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async v1FeatureManagementPost(requestParameters: V1FeatureManagementPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.v1FeatureManagementPostRaw(requestParameters, initOverrides);
    }

}
