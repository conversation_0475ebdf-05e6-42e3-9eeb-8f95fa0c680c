/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface Department
 */
export interface Department {
    /**
     * 
     * @type {string}
     * @memberof Department
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof Department
     */
    name: string;
}

/**
 * Check if a given object implements the Department interface.
 */
export function instanceOfDepartment(value: object): value is Department {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function DepartmentFromJSON(json: any): Department {
    return DepartmentFromJSONTyped(json, false);
}

export function DepartmentFromJSONTyped(json: any, ignoreDiscriminator: boolean): Department {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
    };
}

export function DepartmentToJSON(json: any): Department {
    return DepartmentToJSONTyped(json, false);
}

export function DepartmentToJSONTyped(value?: Department | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

