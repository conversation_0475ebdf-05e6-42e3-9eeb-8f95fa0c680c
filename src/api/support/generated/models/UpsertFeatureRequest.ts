/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface UpsertFeatureRequest
 */
export interface UpsertFeatureRequest {
    /**
     * 
     * @type {string}
     * @memberof UpsertFeatureRequest
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof UpsertFeatureRequest
     */
    description?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof UpsertFeatureRequest
     */
    isEnabled: boolean;
}

/**
 * Check if a given object implements the UpsertFeatureRequest interface.
 */
export function instanceOfUpsertFeatureRequest(value: object): value is UpsertFeatureRequest {
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('isEnabled' in value) || value['isEnabled'] === undefined) return false;
    return true;
}

export function UpsertFeatureRequestFromJSON(json: any): UpsertFeatureRequest {
    return UpsertFeatureRequestFromJSONTyped(json, false);
}

export function UpsertFeatureRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): UpsertFeatureRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'],
        'description': json['description'] == null ? undefined : json['description'],
        'isEnabled': json['isEnabled'],
    };
}

export function UpsertFeatureRequestToJSON(json: any): UpsertFeatureRequest {
    return UpsertFeatureRequestToJSONTyped(json, false);
}

export function UpsertFeatureRequestToJSONTyped(value?: UpsertFeatureRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'name': value['name'],
        'description': value['description'],
        'isEnabled': value['isEnabled'],
    };
}

