/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface GetPositionsRequestV2
 */
export interface GetPositionsRequestV2 {
    /**
     * 
     * @type {number}
     * @memberof GetPositionsRequestV2
     */
    pageNumber?: number | null;
    /**
     * 
     * @type {number}
     * @memberof GetPositionsRequestV2
     */
    pageSize?: number | null;
}

/**
 * Check if a given object implements the GetPositionsRequestV2 interface.
 */
export function instanceOfGetPositionsRequestV2(value: object): value is GetPositionsRequestV2 {
    return true;
}

export function GetPositionsRequestV2FromJSON(json: any): GetPositionsRequestV2 {
    return GetPositionsRequestV2FromJSONTyped(json, false);
}

export function GetPositionsRequestV2FromJSONTyped(json: any, ignoreDiscriminator: boolean): GetPositionsRequestV2 {
    if (json == null) {
        return json;
    }
    return {
        
        'pageNumber': json['pageNumber'] == null ? undefined : json['pageNumber'],
        'pageSize': json['pageSize'] == null ? undefined : json['pageSize'],
    };
}

export function GetPositionsRequestV2ToJSON(json: any): GetPositionsRequestV2 {
    return GetPositionsRequestV2ToJSONTyped(json, false);
}

export function GetPositionsRequestV2ToJSONTyped(value?: GetPositionsRequestV2 | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'pageNumber': value['pageNumber'],
        'pageSize': value['pageSize'],
    };
}

