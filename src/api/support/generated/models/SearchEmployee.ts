/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SearchEmployee
 */
export interface SearchEmployee {
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    fullName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    email?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    groupCompany?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    department?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    positionTitle?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    lineManagerEmployeeId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    lineManagerEmployeeName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    lineManagerEmployeeEmail?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    managerB2BId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    managerB2BName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    managerB2BEmail?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployee
     */
    atrUserGroupId?: string | null;
}

/**
 * Check if a given object implements the SearchEmployee interface.
 */
export function instanceOfSearchEmployee(value: object): value is SearchEmployee {
    if (!('id' in value) || value['id'] === undefined) return false;
    return true;
}

export function SearchEmployeeFromJSON(json: any): SearchEmployee {
    return SearchEmployeeFromJSONTyped(json, false);
}

export function SearchEmployeeFromJSONTyped(json: any, ignoreDiscriminator: boolean): SearchEmployee {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'fullName': json['fullName'] == null ? undefined : json['fullName'],
        'email': json['email'] == null ? undefined : json['email'],
        'groupCompany': json['groupCompany'] == null ? undefined : json['groupCompany'],
        'department': json['department'] == null ? undefined : json['department'],
        'positionTitle': json['positionTitle'] == null ? undefined : json['positionTitle'],
        'lineManagerEmployeeId': json['lineManagerEmployeeId'] == null ? undefined : json['lineManagerEmployeeId'],
        'lineManagerEmployeeName': json['lineManagerEmployeeName'] == null ? undefined : json['lineManagerEmployeeName'],
        'lineManagerEmployeeEmail': json['lineManagerEmployeeEmail'] == null ? undefined : json['lineManagerEmployeeEmail'],
        'managerB2BId': json['managerB2BId'] == null ? undefined : json['managerB2BId'],
        'managerB2BName': json['managerB2BName'] == null ? undefined : json['managerB2BName'],
        'managerB2BEmail': json['managerB2BEmail'] == null ? undefined : json['managerB2BEmail'],
        'atrUserGroupId': json['atrUserGroupId'] == null ? undefined : json['atrUserGroupId'],
    };
}

export function SearchEmployeeToJSON(json: any): SearchEmployee {
    return SearchEmployeeToJSONTyped(json, false);
}

export function SearchEmployeeToJSONTyped(value?: SearchEmployee | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'fullName': value['fullName'],
        'email': value['email'],
        'groupCompany': value['groupCompany'],
        'department': value['department'],
        'positionTitle': value['positionTitle'],
        'lineManagerEmployeeId': value['lineManagerEmployeeId'],
        'lineManagerEmployeeName': value['lineManagerEmployeeName'],
        'lineManagerEmployeeEmail': value['lineManagerEmployeeEmail'],
        'managerB2BId': value['managerB2BId'],
        'managerB2BName': value['managerB2BName'],
        'managerB2BEmail': value['managerB2BEmail'],
        'atrUserGroupId': value['atrUserGroupId'],
    };
}

