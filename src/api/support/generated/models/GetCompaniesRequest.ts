/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface GetCompaniesRequest
 */
export interface GetCompaniesRequest {
    /**
     * 
     * @type {number}
     * @memberof GetCompaniesRequest
     */
    pageNumber?: number | null;
    /**
     * 
     * @type {number}
     * @memberof GetCompaniesRequest
     */
    pageSize?: number | null;
}

/**
 * Check if a given object implements the GetCompaniesRequest interface.
 */
export function instanceOfGetCompaniesRequest(value: object): value is GetCompaniesRequest {
    return true;
}

export function GetCompaniesRequestFromJSON(json: any): GetCompaniesRequest {
    return GetCompaniesRequestFromJSONTyped(json, false);
}

export function GetCompaniesRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): GetCompaniesRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'pageNumber': json['pageNumber'] == null ? undefined : json['pageNumber'],
        'pageSize': json['pageSize'] == null ? undefined : json['pageSize'],
    };
}

export function GetCompaniesRequestToJSON(json: any): GetCompaniesRequest {
    return GetCompaniesRequestToJSONTyped(json, false);
}

export function GetCompaniesRequestToJSONTyped(value?: GetCompaniesRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'pageNumber': value['pageNumber'],
        'pageSize': value['pageSize'],
    };
}

