/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SearchEmployeeRequest
 */
export interface SearchEmployeeRequest {
    /**
     * 
     * @type {number}
     * @memberof SearchEmployeeRequest
     */
    pageNumber?: number | null;
    /**
     * 
     * @type {number}
     * @memberof SearchEmployeeRequest
     */
    pageSize?: number | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployeeRequest
     */
    orderBy?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchEmployeeRequest
     */
    search?: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SearchEmployeeRequest
     */
    groupCompanyCodes?: Array<string> | null;
    /**
     * 
     * @type {Array<number>}
     * @memberof SearchEmployeeRequest
     */
    departmentIds?: Array<number> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SearchEmployeeRequest
     */
    positionTitleIds?: Array<string> | null;
}

/**
 * Check if a given object implements the SearchEmployeeRequest interface.
 */
export function instanceOfSearchEmployeeRequest(value: object): value is SearchEmployeeRequest {
    return true;
}

export function SearchEmployeeRequestFromJSON(json: any): SearchEmployeeRequest {
    return SearchEmployeeRequestFromJSONTyped(json, false);
}

export function SearchEmployeeRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): SearchEmployeeRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'pageNumber': json['pageNumber'] == null ? undefined : json['pageNumber'],
        'pageSize': json['pageSize'] == null ? undefined : json['pageSize'],
        'orderBy': json['orderBy'] == null ? undefined : json['orderBy'],
        'search': json['search'] == null ? undefined : json['search'],
        'groupCompanyCodes': json['groupCompanyCodes'] == null ? undefined : json['groupCompanyCodes'],
        'departmentIds': json['departmentIds'] == null ? undefined : json['departmentIds'],
        'positionTitleIds': json['positionTitleIds'] == null ? undefined : json['positionTitleIds'],
    };
}

export function SearchEmployeeRequestToJSON(json: any): SearchEmployeeRequest {
    return SearchEmployeeRequestToJSONTyped(json, false);
}

export function SearchEmployeeRequestToJSONTyped(value?: SearchEmployeeRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'pageNumber': value['pageNumber'],
        'pageSize': value['pageSize'],
        'orderBy': value['orderBy'],
        'search': value['search'],
        'groupCompanyCodes': value['groupCompanyCodes'],
        'departmentIds': value['departmentIds'],
        'positionTitleIds': value['positionTitleIds'],
    };
}

