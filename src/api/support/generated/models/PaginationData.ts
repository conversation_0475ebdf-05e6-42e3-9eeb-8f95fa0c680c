/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.SupportAdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.25
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PaginationData
 */
export interface PaginationData {
    /**
     * 
     * @type {number}
     * @memberof PaginationData
     */
    pageNumber: number;
    /**
     * 
     * @type {number}
     * @memberof PaginationData
     */
    pageSize: number;
    /**
     * 
     * @type {number}
     * @memberof PaginationData
     */
    count: number;
    /**
     * 
     * @type {number}
     * @memberof PaginationData
     */
    totalResults?: number | null;
}

/**
 * Check if a given object implements the PaginationData interface.
 */
export function instanceOfPaginationData(value: object): value is PaginationData {
    if (!('pageNumber' in value) || value['pageNumber'] === undefined) return false;
    if (!('pageSize' in value) || value['pageSize'] === undefined) return false;
    if (!('count' in value) || value['count'] === undefined) return false;
    return true;
}

export function PaginationDataFromJSON(json: any): PaginationData {
    return PaginationDataFromJSONTyped(json, false);
}

export function PaginationDataFromJSONTyped(json: any, ignoreDiscriminator: boolean): PaginationData {
    if (json == null) {
        return json;
    }
    return {
        
        'pageNumber': json['pageNumber'],
        'pageSize': json['pageSize'],
        'count': json['count'],
        'totalResults': json['totalResults'] == null ? undefined : json['totalResults'],
    };
}

export function PaginationDataToJSON(json: any): PaginationData {
    return PaginationDataToJSONTyped(json, false);
}

export function PaginationDataToJSONTyped(value?: PaginationData | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'pageNumber': value['pageNumber'],
        'pageSize': value['pageSize'],
        'count': value['count'],
        'totalResults': value['totalResults'],
    };
}

