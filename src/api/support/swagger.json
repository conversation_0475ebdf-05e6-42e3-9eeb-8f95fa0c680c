{"openapi": "3.0.1", "info": {"title": "OneTalent.SupportAdminService - PublicAPI", "description": "PublicAPI documentation", "version": "1.0.25"}, "servers": [{"url": "https://api.dev.onetalent.adnoc.ae/support-admin", "description": "Server"}], "paths": {"/v1/companies/all": {"post": {"tags": ["EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCompaniesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyPagedListResult"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/departments/all": {"post": {"tags": ["EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetDepartmentsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartmentPagedListResult"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/employees/details": {"post": {"tags": ["EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEmployeeDetailsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeFullInfoModel"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/employees/{employeeId}/full-details": {"get": {"tags": ["EmployeeViews"], "parameters": [{"name": "employeeId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeFullInfoModel"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/employees/search": {"post": {"tags": ["EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchEmployeeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchEmployeePagedListResult"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/positions/all": {"post": {"tags": ["EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPositionsRequestV2"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PositionPagedListResult"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/feature-management": {"get": {"tags": ["FeatureManagement"], "responses": {"404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "post": {"tags": ["FeatureManagement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertFeatureRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/shared/features": {"get": {"tags": ["Shared"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureTogglesResponse"}}}}}}}}, "components": {"schemas": {"Company": {"required": ["code", "name"], "type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}}, "additionalProperties": false}, "CompanyPagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "Department": {"required": ["id", "name"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "additionalProperties": false}, "DepartmentPagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Department"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "EmployeeCard": {"required": ["companyCode", "companyName", "email", "employeeId", "fullName", "isDeleted", "jobTitle", "positionName"], "type": "object", "properties": {"employeeId": {"type": "string"}, "fullName": {"type": "string"}, "email": {"type": "string"}, "jobTitle": {"type": "string"}, "companyCode": {"type": "string"}, "companyName": {"type": "string"}, "positionName": {"type": "string"}, "positionId": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "lineManager": {"$ref": "#/components/schemas/ManagerDetails"}, "b2BManager": {"$ref": "#/components/schemas/ManagerDetails"}}, "additionalProperties": false}, "EmployeeFullInfoModel": {"required": ["otherEmployeeProfiles", "primaryEmployeeInfo"], "type": "object", "properties": {"primaryEmployeeInfo": {"$ref": "#/components/schemas/EmployeeCard"}, "otherEmployeeProfiles": {"type": "array", "items": {"$ref": "#/components/schemas/EmployeeCard"}}}, "additionalProperties": false}, "FeatureToggleModel": {"required": ["enabled"], "type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}, "FeatureTogglesResponse": {"required": ["currentUserEmail", "features"], "type": "object", "properties": {"currentUserEmail": {"type": "string"}, "features": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FeatureToggleModel"}}}, "additionalProperties": false}, "GetCompaniesRequest": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetDepartmentsRequest": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetEmployeeDetailsRequest": {"required": ["email"], "type": "object", "properties": {"email": {"type": "string"}}, "additionalProperties": false}, "GetPositionsRequestV2": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ManagerDetails": {"type": "object", "properties": {"employeeId": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaginationData": {"required": ["count", "pageNumber", "pageSize"], "type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "count": {"type": "integer", "format": "int32"}, "totalResults": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "Position": {"required": ["id", "name"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "additionalProperties": false}, "PositionPagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Position"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "SearchEmployee": {"required": ["id"], "type": "object", "properties": {"id": {"type": "string"}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "groupCompany": {"type": "string", "nullable": true}, "department": {"type": "string", "nullable": true}, "positionTitle": {"type": "string", "nullable": true}, "lineManagerEmployeeId": {"type": "string", "nullable": true}, "lineManagerEmployeeName": {"type": "string", "nullable": true}, "lineManagerEmployeeEmail": {"type": "string", "nullable": true}, "managerB2BId": {"type": "string", "nullable": true}, "managerB2BName": {"type": "string", "nullable": true}, "managerB2BEmail": {"type": "string", "nullable": true}, "atrUserGroupId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchEmployeePagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SearchEmployee"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "SearchEmployeeRequest": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}, "orderBy": {"type": "string", "nullable": true}, "search": {"type": "string", "nullable": true}, "groupCompanyCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "departmentIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "positionTitleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpsertFeatureRequest": {"required": ["isEnabled", "name"], "type": "object", "properties": {"name": {"minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "isEnabled": {"type": "boolean"}}, "additionalProperties": false}}, "securitySchemes": {"oauth2": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT token in format 'Bearer *****'", "name": "Authorization", "in": "header"}}}, "security": [{"oauth2": []}]}