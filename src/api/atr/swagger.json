{"openapi": "3.0.1", "info": {"title": "OneTalent.AdminService - PublicAPI", "description": "PublicAPI documentation", "version": "1.0.123-500-on-history"}, "servers": [{"url": "https://api.dev.onetalent.adnoc.ae/admin", "description": "Server"}], "paths": {"/atr-admin/v1/admins/me/actions": {"post": {"tags": ["Atr - Actions - Commands"], "parameters": [{"name": "actionType", "in": "query", "description": "\n\nLaunch\n\nTransition\n\nReAssign\n\nExport", "required": true, "schema": {"$ref": "#/components/schemas/ActionType"}, "x-enumNames": ["Launch", "Transition", "ReAssign", "Export"]}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleActionRequest"}}}, "required": true}, "responses": {"202": {"description": "Accepted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionDto"}}}}, "409": {"description": "Conflict", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/atr-admin/v1/admins/me/actions/{actionId}": {"patch": {"tags": ["Atr - Actions - Commands"], "parameters": [{"name": "actionId", "in": "path", "required": true, "schema": {"type": "string", "format": "int64", "x-internalType": "ItemId"}}], "responses": {"202": {"description": "Accepted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionStatus"}}}}, "409": {"description": "Conflict"}, "404": {"description": "Not Found"}}}, "get": {"tags": ["Atr - Actions - Queries"], "parameters": [{"name": "actionId", "in": "path", "required": true, "schema": {"type": "string", "format": "int64", "x-internalType": "ItemId"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionDto"}}}}, "404": {"description": "Not Found"}}}}, "/atr-admin/v1/admins/me/actions/current/input-file": {"post": {"tags": ["Atr - Actions - Commands"], "parameters": [{"name": "actionType", "in": "query", "description": "\n\nLaunch\n\nTransition\n\nReAssign\n\nExport", "required": true, "schema": {"$ref": "#/components/schemas/ActionType"}, "x-enumNames": ["Launch", "Transition", "ReAssign", "Export"]}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/UploadFileRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/UploadFileRequest"}}}}, "responses": {"204": {"description": "No Content"}}}, "delete": {"tags": ["Atr - Actions - Commands"], "parameters": [{"name": "actionType", "in": "query", "description": "\n\nLaunch\n\nTransition\n\nReAssign\n\nExport", "required": true, "schema": {"$ref": "#/components/schemas/ActionType"}, "x-enumNames": ["Launch", "Transition", "ReAssign", "Export"]}], "responses": {"204": {"description": "No Content"}, "404": {"description": "Not Found"}}}, "get": {"tags": ["Atr - Actions - Queries"], "parameters": [{"name": "actionType", "in": "query", "description": "\n\nLaunch\n\nTransition\n\nReAssign\n\nExport", "required": true, "schema": {"$ref": "#/components/schemas/ActionType"}, "x-enumNames": ["Launch", "Transition", "ReAssign", "Export"]}], "responses": {"200": {"description": "OK", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Not Found"}}}}, "/atr-admin/v1/admins/me/actions/search": {"post": {"tags": ["Atr - Actions - Queries"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchMyActionsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionDtoPagedListResult"}}}}}}}, "/atr-admin/v1/admins/me/actions/{actionId}/input-file": {"get": {"tags": ["Atr - Actions - Queries"], "parameters": [{"name": "actionId", "in": "path", "required": true, "schema": {"type": "string", "format": "int64", "x-internalType": "ItemId"}}], "responses": {"200": {"description": "OK", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Not Found"}}}}, "/atr-admin/v1/admins/me/actions/{actionId}/output-file": {"get": {"tags": ["Atr - Actions - Queries"], "parameters": [{"name": "actionId", "in": "path", "required": true, "schema": {"type": "string", "format": "int64", "x-internalType": "ItemId"}}], "responses": {"200": {"description": "OK", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Not Found"}}}}, "/atr-admin/v1/admins/me/actions/current": {"get": {"tags": ["Atr - Actions - Queries"], "parameters": [{"name": "actionType", "in": "query", "description": "\n\nLaunch\n\nTransition\n\nReAssign\n\nExport", "required": true, "schema": {"$ref": "#/components/schemas/ActionType"}, "x-enumNames": ["Launch", "Transition", "ReAssign", "Export"]}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionDto"}}}}}}}, "/atr-admin/v1/performance-forms/search": {"post": {"tags": ["Atr - Performance Forms - Queries"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PerformanceFormSearchQuery"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PerformanceFormDtoPagedListResult"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/atr-admin/v1/performance-forms/statuses": {"get": {"tags": ["Atr - Performance Forms - Queries"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PerformanceFormStatusDto"}}}}}}}}, "/atr-admin/v1/performance-forms/atr-cycles": {"get": {"tags": ["Atr - Performance Forms - Queries"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AtrCycleDto"}}}}}}}}, "/atr-admin/v1/performance-forms/companies": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/divisions": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/directorates": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/functions": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/templates": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/atr-groups": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/employees": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/line-managers": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/b2b-managers": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/dotted-line-managers": {"get": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NamedOptionsDto"}}}}}}}}, "/atr-admin/v1/performance-forms/{performanceFormId}/history": {"post": {"tags": ["Atr - Performance Forms - Queries"], "parameters": [{"name": "performanceFormId", "in": "path", "required": true, "schema": {"type": "string", "format": "int64", "x-internalType": "ItemId"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPerformanceFormHistoryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PerformanceFormHistoryDtoPagedListResult"}}}}, "401": {"description": "Unauthorized", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/atr-admin/v1/performance-forms/insights": {"get": {"tags": ["Atr - Performance Forms - Queries"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsightsDto"}}}}}}}, "/atr-admin/v1/templates": {"get": {"tags": ["Atr - Templates - Queries"], "parameters": [{"name": "actionType", "in": "query", "description": "\n\nLaunch\n\nTransition\n\nReAssign\n\nExport", "required": true, "schema": {"$ref": "#/components/schemas/ActionType"}, "x-enumNames": ["Launch", "Transition", "ReAssign", "Export"]}], "responses": {"200": {"description": "OK", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Not Found"}}}}, "/atr-admin/v1/employees/{employeeId}/employee-info": {"get": {"tags": ["Employees - Queries"], "parameters": [{"name": "employeeId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeInfoDto"}}}}}}}, "/atr-admin/v1/jobs": {"post": {"tags": ["Jobs"], "parameters": [{"name": "action", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJobResponse"}}}}, "409": {"description": "Conflict"}}}}, "/atr-admin/v1/jobs/{jobId}": {"delete": {"tags": ["Jobs"], "parameters": [{"name": "jobId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "404": {"description": "Not Found"}}}}, "/v1/shared/features": {"get": {"tags": ["Shared"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureTogglesResponse"}}}}}}}, "/support-admin/v1/companies/all": {"post": {"tags": ["SupportAdmin - EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCompaniesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyPagedListResult"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/support-admin/v1/departments/all": {"post": {"tags": ["SupportAdmin - EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetDepartmentsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartmentPagedListResult"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/support-admin/v1/employees/details": {"post": {"tags": ["SupportAdmin - EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEmployeeDetailsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeFullInfoModel"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/support-admin/v1/employees/{employeeId}/full-details": {"get": {"tags": ["SupportAdmin - EmployeeViews"], "parameters": [{"name": "employeeId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeFullInfoModel"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/support-admin/v1/employees/search": {"post": {"tags": ["SupportAdmin - EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchEmployeeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchEmployeePagedListResult"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/support-admin/v1/positions/all": {"post": {"tags": ["SupportAdmin - EmployeeViews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPositionsRequestV2"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PositionPagedListResult"}}}}, "404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/support-admin/v1/feature-management": {"get": {"tags": ["SupportAdmin - FeatureManagement"], "responses": {"404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "post": {"tags": ["SupportAdmin - FeatureManagement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertFeatureRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/support-admin/v1/permissions/employee/{employeeId}": {"get": {"tags": ["SupportAdmin - Permissions"], "parameters": [{"name": "employeeId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEmployeePermissionsResponse"}}}}}}}}, "components": {"schemas": {"ActionDto": {"required": ["rowsFailed", "rowsSucceeded", "status", "totalRowsProcessed", "type"], "type": "object", "properties": {"id": {"type": "string", "format": "int64", "nullable": true, "x-internalType": "ItemId"}, "isValidation": {"type": "boolean", "nullable": true}, "status": {"$ref": "#/components/schemas/ActionStatus"}, "type": {"$ref": "#/components/schemas/ActionType"}, "initiatorId": {"type": "string", "nullable": true, "deprecated": true}, "initiator": {"$ref": "#/components/schemas/EmployeeShortDto"}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "inputFileId": {"type": "string", "format": "int64", "nullable": true, "x-internalType": "ItemId"}, "inputFileName": {"type": "string", "nullable": true}, "inputFileSize": {"type": "integer", "format": "int64", "nullable": true}, "outputFileName": {"type": "string", "nullable": true}, "totalRowsProcessed": {"type": "integer", "format": "int32"}, "rowsSucceeded": {"type": "integer", "format": "int32"}, "rowsFailed": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ActionDtoPagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ActionDto"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "ActionStatus": {"enum": ["Draft", "InProgress", "Pending", "Completed", "Canceled", "Cancelling", "Failed"], "type": "string", "description": "\n\nDraft\n\nInProgress\n\nPending\n\nCompleted\n\nCanceled\n\nCancelling\n\nFailed", "x-enumNames": ["Draft", "InProgress", "Pending", "Completed", "Canceled", "Cancelling", "Failed"], "x-enum-varnames": ["Draft", "InProgress", "Pending", "Completed", "Canceled", "Cancelling", "Failed"]}, "ActionType": {"enum": ["Launch", "Transition", "ReAssign", "Export"], "type": "string", "description": "\n\nLaunch\n\nTransition\n\nReAssign\n\nExport", "x-enumNames": ["Launch", "Transition", "ReAssign", "Export"], "x-enum-varnames": ["Launch", "Transition", "ReAssign", "Export"]}, "AtrCycleDto": {"required": ["id", "name", "status"], "type": "object", "properties": {"id": {"type": "string", "format": "int64", "x-internalType": "ItemId"}, "name": {"type": "string"}, "status": {"$ref": "#/components/schemas/AtrCycleStatuses"}}, "additionalProperties": false}, "AtrCycleStatuses": {"enum": ["Inactive", "Current", "Next"], "type": "string", "description": "\n\nInactive\n\nCurrent\n\nNext", "x-enumNames": ["Inactive", "Current", "Next"], "x-enum-varnames": ["Inactive", "Current", "Next"]}, "Company": {"required": ["code", "name"], "type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}}, "additionalProperties": false}, "CompanyPagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "CreateJobResponse": {"required": ["jobId"], "type": "object", "properties": {"jobId": {"type": "string"}}, "additionalProperties": false}, "Department": {"required": ["id", "name"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "additionalProperties": false}, "DepartmentPagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Department"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "EmployeeCard": {"required": ["companyCode", "companyName", "email", "employeeId", "fullName", "isDeleted", "jobTitle", "positionName"], "type": "object", "properties": {"employeeId": {"type": "string"}, "fullName": {"type": "string"}, "email": {"type": "string"}, "jobTitle": {"type": "string"}, "companyCode": {"type": "string"}, "companyName": {"type": "string"}, "positionName": {"type": "string"}, "positionId": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "lineManager": {"$ref": "#/components/schemas/ManagerDetails"}, "b2BManager": {"$ref": "#/components/schemas/ManagerDetails"}}, "additionalProperties": false}, "EmployeeFullInfoModel": {"required": ["otherEmployeeProfiles", "primaryEmployeeInfo"], "type": "object", "properties": {"primaryEmployeeInfo": {"$ref": "#/components/schemas/EmployeeCard"}, "otherEmployeeProfiles": {"type": "array", "items": {"$ref": "#/components/schemas/EmployeeCard"}}}, "additionalProperties": false}, "EmployeeInfoDto": {"required": ["companyCode", "companyName", "email", "employeeId", "fullNameEnglish", "is<PERSON>anager", "jobTitle", "permissions", "positionName"], "type": "object", "properties": {"employeeId": {"type": "string"}, "companyCode": {"type": "string"}, "companyName": {"type": "string"}, "fullNameEnglish": {"type": "string"}, "email": {"type": "string"}, "jobTitle": {"type": "string"}, "positionName": {"type": "string"}, "isManager": {"type": "boolean"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/Rights"}}}, "additionalProperties": false}, "EmployeeShortDto": {"required": ["companyName", "email", "employeeId", "fullNameEnglish", "positionName"], "type": "object", "properties": {"employeeId": {"type": "string"}, "companyName": {"type": "string"}, "fullNameEnglish": {"type": "string"}, "email": {"type": "string"}, "positionName": {"type": "string"}}, "additionalProperties": false}, "FeatureToggleModel": {"required": ["enabled"], "type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}, "FeatureTogglesResponse": {"required": ["currentUserEmail", "features"], "type": "object", "properties": {"currentUserEmail": {"type": "string"}, "features": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FeatureToggleModel"}}}, "additionalProperties": false}, "GetCompaniesRequest": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetDepartmentsRequest": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetEmployeeDetailsRequest": {"required": ["email"], "type": "object", "properties": {"email": {"type": "string"}}, "additionalProperties": false}, "GetEmployeePermissionsResponse": {"required": ["fullName", "permissions"], "type": "object", "properties": {"fullName": {"type": "string"}, "email": {"type": "string", "nullable": true}, "permissions": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}, "additionalProperties": false}, "GetPerformanceFormHistoryRequest": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetPositionsRequestV2": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "InsightsDto": {"required": ["completedManagerAssessments", "completedSelfAssessments", "totalAssessments"], "type": "object", "properties": {"completedSelfAssessments": {"type": "integer", "format": "int32"}, "completedManagerAssessments": {"type": "integer", "format": "int32"}, "totalAssessments": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ManagerDetails": {"type": "object", "properties": {"employeeId": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NamedOptionsDto": {"required": ["id", "name"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "additionalProperties": false}, "OrderBy": {"required": ["direction", "field"], "type": "object", "properties": {"field": {"type": "string"}, "direction": {"$ref": "#/components/schemas/SortDirection"}}, "additionalProperties": false}, "PaginationData": {"required": ["count", "pageNumber", "pageSize"], "type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "count": {"type": "integer", "format": "int32"}, "totalResults": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PerformanceFormDto": {"required": ["assessmentLineManager", "atrGroupName", "employee", "employeeCompanyName", "employeeDivision", "employeeFunction", "id", "majorStatus", "minorStatus", "templateId", "templateName"], "type": "object", "properties": {"id": {"type": "string"}, "templateId": {"type": "string"}, "templateName": {"type": "string"}, "majorStatus": {"type": "string"}, "minorStatus": {"type": "string"}, "employee": {"$ref": "#/components/schemas/EmployeeShortDto"}, "employeeCompanyName": {"type": "string"}, "employeeDirectorate": {"type": "string", "nullable": true}, "employeeFunction": {"type": "string"}, "employeeDivision": {"type": "string"}, "atrGroupName": {"type": "string"}, "assessmentLineManager": {"$ref": "#/components/schemas/EmployeeShortDto"}, "assessmentB2BManager": {"$ref": "#/components/schemas/EmployeeShortDto"}, "dottedLineManager": {"$ref": "#/components/schemas/EmployeeShortDto"}, "lastUpdated": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"$ref": "#/components/schemas/EmployeeShortDto"}}, "additionalProperties": false}, "PerformanceFormDtoPagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PerformanceFormDto"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "PerformanceFormHistoryDto": {"required": ["actionDateTime", "actionOwnerName", "actionType", "currentLineManager", "groupCompany", "majorStatusTo", "managerNineBoxPotential", "managerRating", "managerRatingName", "minorStatusTo", "nineBoxPotentialRating", "performanceCycleName", "performanceFormId"], "type": "object", "properties": {"performanceFormId": {"type": "string", "format": "int64", "x-internalType": "ItemId"}, "actionDateTime": {"type": "string", "format": "date-time"}, "actionType": {"type": "string"}, "actionOwnerId": {"type": "string", "nullable": true}, "actionOwnerName": {"type": "string"}, "majorStatusTo": {"type": "string"}, "minorStatusTo": {"type": "string"}, "majorStatusFrom": {"type": "string", "nullable": true}, "minorStatusFrom": {"type": "string", "nullable": true}, "actionReason": {"type": "string", "nullable": true}, "actionOwnerJobRole": {"type": "string", "nullable": true}, "groupCompany": {"type": "string"}, "directorate": {"type": "string", "nullable": true}, "function": {"type": "string", "nullable": true}, "division": {"type": "string", "nullable": true}, "performanceCycleName": {"type": "string"}, "assessmentLineManagerName": {"type": "string", "nullable": true}, "assessmentB2BManagerName": {"type": "string", "nullable": true}, "dottedLineManagerName": {"type": "string", "nullable": true}, "currentLineManager": {"type": "string"}, "managerRating": {"type": "number", "format": "double"}, "managerRatingName": {"type": "string"}, "managerNineBoxPotential": {"type": "string"}, "nineBoxPotentialRating": {"type": "integer", "format": "int32"}, "normalizedRating": {"type": "string", "nullable": true}, "normalizedRatingName": {"type": "string", "nullable": true}, "normalizedNineBoxPotential": {"type": "string", "nullable": true}, "workflowId": {"type": "string", "nullable": true}, "wfAssigneeName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PerformanceFormHistoryDtoPagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PerformanceFormHistoryDto"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "PerformanceFormSearchQuery": {"type": "object", "properties": {"formIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "divisionIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "companyCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "directorateIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "functionIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "templateIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "atrGroupIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "employeeIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "lineManagerIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "b2BManagerIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dottetLineManagerIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "performanceFormStatusIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "atrCycleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "orderBy": {"$ref": "#/components/schemas/OrderBy"}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PerformanceFormStatusDto": {"required": ["id", "name"], "type": "object", "properties": {"id": {"type": "string", "format": "int64", "x-internalType": "ItemId"}, "name": {"type": "string"}}, "additionalProperties": false}, "Position": {"required": ["id", "name"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "additionalProperties": false}, "PositionPagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Position"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "Rights": {"enum": ["mock"], "type": "string", "description": "\n\nmock", "x-enumNames": ["<PERSON><PERSON>"], "x-enum-varnames": ["<PERSON><PERSON>"]}, "ScheduleActionRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "nullable": true, "x-internalType": "ItemId"}, "inputFileId": {"type": "string", "format": "int64", "nullable": true, "x-internalType": "ItemId"}}, "additionalProperties": false}, "SearchEmployee": {"required": ["id"], "type": "object", "properties": {"id": {"type": "string"}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "groupCompany": {"type": "string", "nullable": true}, "department": {"type": "string", "nullable": true}, "positionTitle": {"type": "string", "nullable": true}, "lineManagerEmployeeId": {"type": "string", "nullable": true}, "lineManagerEmployeeName": {"type": "string", "nullable": true}, "lineManagerEmployeeEmail": {"type": "string", "nullable": true}, "managerB2BId": {"type": "string", "nullable": true}, "managerB2BName": {"type": "string", "nullable": true}, "managerB2BEmail": {"type": "string", "nullable": true}, "atrUserGroupId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchEmployeePagedListResult": {"required": ["items", "paging"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SearchEmployee"}}, "paging": {"$ref": "#/components/schemas/PaginationData"}}, "additionalProperties": false}, "SearchEmployeeRequest": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}, "orderBy": {"type": "string", "nullable": true}, "search": {"type": "string", "nullable": true}, "groupCompanyCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "departmentIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "positionTitleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "SearchMyActionsRequest": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "SortDirection": {"enum": ["Ascending", "Descending"], "type": "string", "description": "\n\nAscending\n\nDescending", "x-enumNames": ["Ascending", "Descending"], "x-enum-varnames": ["Ascending", "Descending"]}, "UploadFileRequest": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "additionalProperties": false}, "UpsertFeatureRequest": {"required": ["isEnabled", "name"], "type": "object", "properties": {"name": {"minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "isEnabled": {"type": "boolean"}}, "additionalProperties": false}}, "securitySchemes": {"oauth2": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT token in format 'Bearer *****'", "name": "Authorization", "in": "header"}}}, "security": [{"oauth2": []}]}