/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  CreateJobResponse,
} from '../models/index';
import {
    CreateJobResponseFromJSON,
    CreateJobResponseToJSON,
} from '../models/index';

export interface AtrAdminV1JobsJobIdDeleteRequest {
    jobId: string;
}

export interface AtrAdminV1JobsPostRequest {
    action: string;
}

/**
 * 
 */
export class JobsApi extends runtime.BaseAPI {

    /**
     */
    async atrAdminV1JobsJobIdDeleteRaw(requestParameters: AtrAdminV1JobsJobIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['jobId'] == null) {
            throw new runtime.RequiredError(
                'jobId',
                'Required parameter "jobId" was null or undefined when calling atrAdminV1JobsJobIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/atr-admin/v1/jobs/{jobId}`;
        urlPath = urlPath.replace(`{${"jobId"}}`, encodeURIComponent(String(requestParameters['jobId'])));

        const response = await this.request({
            path: urlPath,
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async atrAdminV1JobsJobIdDelete(requestParameters: AtrAdminV1JobsJobIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.atrAdminV1JobsJobIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     */
    async atrAdminV1JobsPostRaw(requestParameters: AtrAdminV1JobsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CreateJobResponse>> {
        if (requestParameters['action'] == null) {
            throw new runtime.RequiredError(
                'action',
                'Required parameter "action" was null or undefined when calling atrAdminV1JobsPost().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['action'] != null) {
            queryParameters['action'] = requestParameters['action'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/atr-admin/v1/jobs`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CreateJobResponseFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1JobsPost(requestParameters: AtrAdminV1JobsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CreateJobResponse> {
        const response = await this.atrAdminV1JobsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
