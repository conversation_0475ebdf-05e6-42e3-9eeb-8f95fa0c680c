/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  GetEmployeePermissionsResponse,
} from '../models/index';
import {
    GetEmployeePermissionsResponseFromJSON,
    GetEmployeePermissionsResponseToJSON,
} from '../models/index';

export interface SupportAdminV1PermissionsEmployeeEmployeeIdGetRequest {
    employeeId: string;
}

/**
 * 
 */
export class SupportAdminPermissionsApi extends runtime.BaseAPI {

    /**
     */
    async supportAdminV1PermissionsEmployeeEmployeeIdGetRaw(requestParameters: SupportAdminV1PermissionsEmployeeEmployeeIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<GetEmployeePermissionsResponse>> {
        if (requestParameters['employeeId'] == null) {
            throw new runtime.RequiredError(
                'employeeId',
                'Required parameter "employeeId" was null or undefined when calling supportAdminV1PermissionsEmployeeEmployeeIdGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/support-admin/v1/permissions/employee/{employeeId}`;
        urlPath = urlPath.replace(`{${"employeeId"}}`, encodeURIComponent(String(requestParameters['employeeId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => GetEmployeePermissionsResponseFromJSON(jsonValue));
    }

    /**
     */
    async supportAdminV1PermissionsEmployeeEmployeeIdGet(requestParameters: SupportAdminV1PermissionsEmployeeEmployeeIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<GetEmployeePermissionsResponse> {
        const response = await this.supportAdminV1PermissionsEmployeeEmployeeIdGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
