/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ActionDto,
  ActionDtoPagedListResult,
  ActionType,
  SearchMyActionsRequest,
} from '../models/index';
import {
    ActionDtoFromJSON,
    ActionDtoToJSON,
    ActionDtoPagedListResultFromJSON,
    ActionDtoPagedListResultToJSON,
    ActionTypeFromJSON,
    ActionTypeToJSON,
    SearchMyActionsRequestFromJSON,
    SearchMyActionsRequestToJSON,
} from '../models/index';

export interface AtrAdminV1AdminsMeActionsActionIdGetRequest {
    actionId: string;
}

export interface AtrAdminV1AdminsMeActionsActionIdInputFileGetRequest {
    actionId: string;
}

export interface AtrAdminV1AdminsMeActionsActionIdOutputFileGetRequest {
    actionId: string;
}

export interface AtrAdminV1AdminsMeActionsCurrentGetRequest {
    actionType: ActionType;
}

export interface AtrAdminV1AdminsMeActionsCurrentInputFileGetRequest {
    actionType: ActionType;
}

export interface AtrAdminV1AdminsMeActionsSearchPostRequest {
    searchMyActionsRequest?: SearchMyActionsRequest;
}

/**
 * 
 */
export class AtrActionsQueriesApi extends runtime.BaseAPI {

    /**
     */
    async atrAdminV1AdminsMeActionsActionIdGetRaw(requestParameters: AtrAdminV1AdminsMeActionsActionIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ActionDto>> {
        if (requestParameters['actionId'] == null) {
            throw new runtime.RequiredError(
                'actionId',
                'Required parameter "actionId" was null or undefined when calling atrAdminV1AdminsMeActionsActionIdGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/atr-admin/v1/admins/me/actions/{actionId}`;
        urlPath = urlPath.replace(`{${"actionId"}}`, encodeURIComponent(String(requestParameters['actionId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ActionDtoFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1AdminsMeActionsActionIdGet(requestParameters: AtrAdminV1AdminsMeActionsActionIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ActionDto> {
        const response = await this.atrAdminV1AdminsMeActionsActionIdGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1AdminsMeActionsActionIdInputFileGetRaw(requestParameters: AtrAdminV1AdminsMeActionsActionIdInputFileGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Blob>> {
        if (requestParameters['actionId'] == null) {
            throw new runtime.RequiredError(
                'actionId',
                'Required parameter "actionId" was null or undefined when calling atrAdminV1AdminsMeActionsActionIdInputFileGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/atr-admin/v1/admins/me/actions/{actionId}/input-file`;
        urlPath = urlPath.replace(`{${"actionId"}}`, encodeURIComponent(String(requestParameters['actionId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.BlobApiResponse(response);
    }

    /**
     */
    async atrAdminV1AdminsMeActionsActionIdInputFileGet(requestParameters: AtrAdminV1AdminsMeActionsActionIdInputFileGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Blob> {
        const response = await this.atrAdminV1AdminsMeActionsActionIdInputFileGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1AdminsMeActionsActionIdOutputFileGetRaw(requestParameters: AtrAdminV1AdminsMeActionsActionIdOutputFileGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Blob>> {
        if (requestParameters['actionId'] == null) {
            throw new runtime.RequiredError(
                'actionId',
                'Required parameter "actionId" was null or undefined when calling atrAdminV1AdminsMeActionsActionIdOutputFileGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/atr-admin/v1/admins/me/actions/{actionId}/output-file`;
        urlPath = urlPath.replace(`{${"actionId"}}`, encodeURIComponent(String(requestParameters['actionId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.BlobApiResponse(response);
    }

    /**
     */
    async atrAdminV1AdminsMeActionsActionIdOutputFileGet(requestParameters: AtrAdminV1AdminsMeActionsActionIdOutputFileGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Blob> {
        const response = await this.atrAdminV1AdminsMeActionsActionIdOutputFileGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1AdminsMeActionsCurrentGetRaw(requestParameters: AtrAdminV1AdminsMeActionsCurrentGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ActionDto>> {
        if (requestParameters['actionType'] == null) {
            throw new runtime.RequiredError(
                'actionType',
                'Required parameter "actionType" was null or undefined when calling atrAdminV1AdminsMeActionsCurrentGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['actionType'] != null) {
            queryParameters['actionType'] = requestParameters['actionType'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/atr-admin/v1/admins/me/actions/current`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ActionDtoFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1AdminsMeActionsCurrentGet(requestParameters: AtrAdminV1AdminsMeActionsCurrentGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ActionDto> {
        const response = await this.atrAdminV1AdminsMeActionsCurrentGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1AdminsMeActionsCurrentInputFileGetRaw(requestParameters: AtrAdminV1AdminsMeActionsCurrentInputFileGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Blob>> {
        if (requestParameters['actionType'] == null) {
            throw new runtime.RequiredError(
                'actionType',
                'Required parameter "actionType" was null or undefined when calling atrAdminV1AdminsMeActionsCurrentInputFileGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['actionType'] != null) {
            queryParameters['actionType'] = requestParameters['actionType'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/atr-admin/v1/admins/me/actions/current/input-file`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.BlobApiResponse(response);
    }

    /**
     */
    async atrAdminV1AdminsMeActionsCurrentInputFileGet(requestParameters: AtrAdminV1AdminsMeActionsCurrentInputFileGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Blob> {
        const response = await this.atrAdminV1AdminsMeActionsCurrentInputFileGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1AdminsMeActionsSearchPostRaw(requestParameters: AtrAdminV1AdminsMeActionsSearchPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ActionDtoPagedListResult>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/atr-admin/v1/admins/me/actions/search`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: SearchMyActionsRequestToJSON(requestParameters['searchMyActionsRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ActionDtoPagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1AdminsMeActionsSearchPost(requestParameters: AtrAdminV1AdminsMeActionsSearchPostRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ActionDtoPagedListResult> {
        const response = await this.atrAdminV1AdminsMeActionsSearchPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
