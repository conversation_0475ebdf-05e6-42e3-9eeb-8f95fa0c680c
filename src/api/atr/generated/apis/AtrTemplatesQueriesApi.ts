/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ActionType,
} from '../models/index';
import {
    ActionTypeFromJSON,
    ActionTypeToJSON,
} from '../models/index';

export interface AtrAdminV1TemplatesGetRequest {
    actionType: ActionType;
}

/**
 * 
 */
export class AtrTemplatesQueriesApi extends runtime.BaseAPI {

    /**
     */
    async atrAdminV1TemplatesGetRaw(requestParameters: AtrAdminV1TemplatesGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Blob>> {
        if (requestParameters['actionType'] == null) {
            throw new runtime.RequiredError(
                'actionType',
                'Required parameter "actionType" was null or undefined when calling atrAdminV1TemplatesGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['actionType'] != null) {
            queryParameters['actionType'] = requestParameters['actionType'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }


        let urlPath = `/atr-admin/v1/templates`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.BlobApiResponse(response);
    }

    /**
     */
    async atrAdminV1TemplatesGet(requestParameters: AtrAdminV1TemplatesGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Blob> {
        const response = await this.atrAdminV1TemplatesGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
