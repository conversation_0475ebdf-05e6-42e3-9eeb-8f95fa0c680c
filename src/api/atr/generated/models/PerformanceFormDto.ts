/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { EmployeeShortDto } from './EmployeeShortDto';
import {
    EmployeeShortDtoFromJSON,
    EmployeeShortDtoFromJSONTyped,
    EmployeeShortDtoToJSON,
    EmployeeShortDtoToJSONTyped,
} from './EmployeeShortDto';

/**
 * 
 * @export
 * @interface PerformanceFormDto
 */
export interface PerformanceFormDto {
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    templateId: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    templateName: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    majorStatus: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    minorStatus: string;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormDto
     */
    employee: EmployeeShortDto;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    employeeCompanyName: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    employeeDirectorate?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    employeeFunction: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    employeeDivision: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormDto
     */
    atrGroupName: string;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormDto
     */
    assessmentLineManager: EmployeeShortDto;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormDto
     */
    assessmentB2BManager?: EmployeeShortDto;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormDto
     */
    dottedLineManager?: EmployeeShortDto;
    /**
     * 
     * @type {Date}
     * @memberof PerformanceFormDto
     */
    lastUpdated?: Date | null;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormDto
     */
    updatedBy?: EmployeeShortDto;
}

/**
 * Check if a given object implements the PerformanceFormDto interface.
 */
export function instanceOfPerformanceFormDto(value: object): value is PerformanceFormDto {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('templateId' in value) || value['templateId'] === undefined) return false;
    if (!('templateName' in value) || value['templateName'] === undefined) return false;
    if (!('majorStatus' in value) || value['majorStatus'] === undefined) return false;
    if (!('minorStatus' in value) || value['minorStatus'] === undefined) return false;
    if (!('employee' in value) || value['employee'] === undefined) return false;
    if (!('employeeCompanyName' in value) || value['employeeCompanyName'] === undefined) return false;
    if (!('employeeFunction' in value) || value['employeeFunction'] === undefined) return false;
    if (!('employeeDivision' in value) || value['employeeDivision'] === undefined) return false;
    if (!('atrGroupName' in value) || value['atrGroupName'] === undefined) return false;
    if (!('assessmentLineManager' in value) || value['assessmentLineManager'] === undefined) return false;
    return true;
}

export function PerformanceFormDtoFromJSON(json: any): PerformanceFormDto {
    return PerformanceFormDtoFromJSONTyped(json, false);
}

export function PerformanceFormDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): PerformanceFormDto {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'templateId': json['templateId'],
        'templateName': json['templateName'],
        'majorStatus': json['majorStatus'],
        'minorStatus': json['minorStatus'],
        'employee': EmployeeShortDtoFromJSON(json['employee']),
        'employeeCompanyName': json['employeeCompanyName'],
        'employeeDirectorate': json['employeeDirectorate'] == null ? undefined : json['employeeDirectorate'],
        'employeeFunction': json['employeeFunction'],
        'employeeDivision': json['employeeDivision'],
        'atrGroupName': json['atrGroupName'],
        'assessmentLineManager': EmployeeShortDtoFromJSON(json['assessmentLineManager']),
        'assessmentB2BManager': json['assessmentB2BManager'] == null ? undefined : EmployeeShortDtoFromJSON(json['assessmentB2BManager']),
        'dottedLineManager': json['dottedLineManager'] == null ? undefined : EmployeeShortDtoFromJSON(json['dottedLineManager']),
        'lastUpdated': json['lastUpdated'] == null ? undefined : (new Date(json['lastUpdated'])),
        'updatedBy': json['updatedBy'] == null ? undefined : EmployeeShortDtoFromJSON(json['updatedBy']),
    };
}

export function PerformanceFormDtoToJSON(json: any): PerformanceFormDto {
    return PerformanceFormDtoToJSONTyped(json, false);
}

export function PerformanceFormDtoToJSONTyped(value?: PerformanceFormDto | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'templateId': value['templateId'],
        'templateName': value['templateName'],
        'majorStatus': value['majorStatus'],
        'minorStatus': value['minorStatus'],
        'employee': EmployeeShortDtoToJSON(value['employee']),
        'employeeCompanyName': value['employeeCompanyName'],
        'employeeDirectorate': value['employeeDirectorate'],
        'employeeFunction': value['employeeFunction'],
        'employeeDivision': value['employeeDivision'],
        'atrGroupName': value['atrGroupName'],
        'assessmentLineManager': EmployeeShortDtoToJSON(value['assessmentLineManager']),
        'assessmentB2BManager': EmployeeShortDtoToJSON(value['assessmentB2BManager']),
        'dottedLineManager': EmployeeShortDtoToJSON(value['dottedLineManager']),
        'lastUpdated': value['lastUpdated'] === null ? null : ((value['lastUpdated'] as any)?.toISOString()),
        'updatedBy': EmployeeShortDtoToJSON(value['updatedBy']),
    };
}

