/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface GetPerformanceFormHistoryRequest
 */
export interface GetPerformanceFormHistoryRequest {
    /**
     * 
     * @type {number}
     * @memberof GetPerformanceFormHistoryRequest
     */
    pageNumber?: number | null;
    /**
     * 
     * @type {number}
     * @memberof GetPerformanceFormHistoryRequest
     */
    pageSize?: number | null;
}

/**
 * Check if a given object implements the GetPerformanceFormHistoryRequest interface.
 */
export function instanceOfGetPerformanceFormHistoryRequest(value: object): value is GetPerformanceFormHistoryRequest {
    return true;
}

export function GetPerformanceFormHistoryRequestFromJSON(json: any): GetPerformanceFormHistoryRequest {
    return GetPerformanceFormHistoryRequestFromJSONTyped(json, false);
}

export function GetPerformanceFormHistoryRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): GetPerformanceFormHistoryRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'pageNumber': json['pageNumber'] == null ? undefined : json['pageNumber'],
        'pageSize': json['pageSize'] == null ? undefined : json['pageSize'],
    };
}

export function GetPerformanceFormHistoryRequestToJSON(json: any): GetPerformanceFormHistoryRequest {
    return GetPerformanceFormHistoryRequestToJSONTyped(json, false);
}

export function GetPerformanceFormHistoryRequestToJSONTyped(value?: GetPerformanceFormHistoryRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'pageNumber': value['pageNumber'],
        'pageSize': value['pageSize'],
    };
}

