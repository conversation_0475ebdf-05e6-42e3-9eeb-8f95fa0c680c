/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PerformanceFormHistoryDto
 */
export interface PerformanceFormHistoryDto {
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    performanceFormId: string;
    /**
     * 
     * @type {Date}
     * @memberof PerformanceFormHistoryDto
     */
    actionDateTime: Date;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    actionType: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    actionOwnerId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    actionOwnerName: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    majorStatusTo: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    minorStatusTo: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    majorStatusFrom?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    minorStatusFrom?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    actionReason?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    actionOwnerJobRole?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    groupCompany: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    directorate?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    _function?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    division?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    performanceCycleName: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    assessmentLineManagerName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    assessmentB2BManagerName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    dottedLineManagerName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    currentLineManager: string;
    /**
     * 
     * @type {number}
     * @memberof PerformanceFormHistoryDto
     */
    managerRating: number;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    managerRatingName: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    managerNineBoxPotential: string;
    /**
     * 
     * @type {number}
     * @memberof PerformanceFormHistoryDto
     */
    nineBoxPotentialRating: number;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    normalizedRating?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    normalizedRatingName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    normalizedNineBoxPotential?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    workflowId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    wfAssigneeName?: string | null;
}

/**
 * Check if a given object implements the PerformanceFormHistoryDto interface.
 */
export function instanceOfPerformanceFormHistoryDto(value: object): value is PerformanceFormHistoryDto {
    if (!('performanceFormId' in value) || value['performanceFormId'] === undefined) return false;
    if (!('actionDateTime' in value) || value['actionDateTime'] === undefined) return false;
    if (!('actionType' in value) || value['actionType'] === undefined) return false;
    if (!('actionOwnerName' in value) || value['actionOwnerName'] === undefined) return false;
    if (!('majorStatusTo' in value) || value['majorStatusTo'] === undefined) return false;
    if (!('minorStatusTo' in value) || value['minorStatusTo'] === undefined) return false;
    if (!('groupCompany' in value) || value['groupCompany'] === undefined) return false;
    if (!('performanceCycleName' in value) || value['performanceCycleName'] === undefined) return false;
    if (!('currentLineManager' in value) || value['currentLineManager'] === undefined) return false;
    if (!('managerRating' in value) || value['managerRating'] === undefined) return false;
    if (!('managerRatingName' in value) || value['managerRatingName'] === undefined) return false;
    if (!('managerNineBoxPotential' in value) || value['managerNineBoxPotential'] === undefined) return false;
    if (!('nineBoxPotentialRating' in value) || value['nineBoxPotentialRating'] === undefined) return false;
    return true;
}

export function PerformanceFormHistoryDtoFromJSON(json: any): PerformanceFormHistoryDto {
    return PerformanceFormHistoryDtoFromJSONTyped(json, false);
}

export function PerformanceFormHistoryDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): PerformanceFormHistoryDto {
    if (json == null) {
        return json;
    }
    return {
        
        'performanceFormId': json['performanceFormId'],
        'actionDateTime': (new Date(json['actionDateTime'])),
        'actionType': json['actionType'],
        'actionOwnerId': json['actionOwnerId'] == null ? undefined : json['actionOwnerId'],
        'actionOwnerName': json['actionOwnerName'],
        'majorStatusTo': json['majorStatusTo'],
        'minorStatusTo': json['minorStatusTo'],
        'majorStatusFrom': json['majorStatusFrom'] == null ? undefined : json['majorStatusFrom'],
        'minorStatusFrom': json['minorStatusFrom'] == null ? undefined : json['minorStatusFrom'],
        'actionReason': json['actionReason'] == null ? undefined : json['actionReason'],
        'actionOwnerJobRole': json['actionOwnerJobRole'] == null ? undefined : json['actionOwnerJobRole'],
        'groupCompany': json['groupCompany'],
        'directorate': json['directorate'] == null ? undefined : json['directorate'],
        '_function': json['function'] == null ? undefined : json['function'],
        'division': json['division'] == null ? undefined : json['division'],
        'performanceCycleName': json['performanceCycleName'],
        'assessmentLineManagerName': json['assessmentLineManagerName'] == null ? undefined : json['assessmentLineManagerName'],
        'assessmentB2BManagerName': json['assessmentB2BManagerName'] == null ? undefined : json['assessmentB2BManagerName'],
        'dottedLineManagerName': json['dottedLineManagerName'] == null ? undefined : json['dottedLineManagerName'],
        'currentLineManager': json['currentLineManager'],
        'managerRating': json['managerRating'],
        'managerRatingName': json['managerRatingName'],
        'managerNineBoxPotential': json['managerNineBoxPotential'],
        'nineBoxPotentialRating': json['nineBoxPotentialRating'],
        'normalizedRating': json['normalizedRating'] == null ? undefined : json['normalizedRating'],
        'normalizedRatingName': json['normalizedRatingName'] == null ? undefined : json['normalizedRatingName'],
        'normalizedNineBoxPotential': json['normalizedNineBoxPotential'] == null ? undefined : json['normalizedNineBoxPotential'],
        'workflowId': json['workflowId'] == null ? undefined : json['workflowId'],
        'wfAssigneeName': json['wfAssigneeName'] == null ? undefined : json['wfAssigneeName'],
    };
}

export function PerformanceFormHistoryDtoToJSON(json: any): PerformanceFormHistoryDto {
    return PerformanceFormHistoryDtoToJSONTyped(json, false);
}

export function PerformanceFormHistoryDtoToJSONTyped(value?: PerformanceFormHistoryDto | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'performanceFormId': value['performanceFormId'],
        'actionDateTime': ((value['actionDateTime']).toISOString()),
        'actionType': value['actionType'],
        'actionOwnerId': value['actionOwnerId'],
        'actionOwnerName': value['actionOwnerName'],
        'majorStatusTo': value['majorStatusTo'],
        'minorStatusTo': value['minorStatusTo'],
        'majorStatusFrom': value['majorStatusFrom'],
        'minorStatusFrom': value['minorStatusFrom'],
        'actionReason': value['actionReason'],
        'actionOwnerJobRole': value['actionOwnerJobRole'],
        'groupCompany': value['groupCompany'],
        'directorate': value['directorate'],
        'function': value['_function'],
        'division': value['division'],
        'performanceCycleName': value['performanceCycleName'],
        'assessmentLineManagerName': value['assessmentLineManagerName'],
        'assessmentB2BManagerName': value['assessmentB2BManagerName'],
        'dottedLineManagerName': value['dottedLineManagerName'],
        'currentLineManager': value['currentLineManager'],
        'managerRating': value['managerRating'],
        'managerRatingName': value['managerRatingName'],
        'managerNineBoxPotential': value['managerNineBoxPotential'],
        'nineBoxPotentialRating': value['nineBoxPotentialRating'],
        'normalizedRating': value['normalizedRating'],
        'normalizedRatingName': value['normalizedRatingName'],
        'normalizedNineBoxPotential': value['normalizedNineBoxPotential'],
        'workflowId': value['workflowId'],
        'wfAssigneeName': value['wfAssigneeName'],
    };
}

