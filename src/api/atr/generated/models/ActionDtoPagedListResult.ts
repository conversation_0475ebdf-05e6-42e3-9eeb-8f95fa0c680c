/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { PaginationData } from './PaginationData';
import {
    PaginationDataFromJSON,
    PaginationDataFromJSONTyped,
    PaginationDataToJSON,
    PaginationDataToJSONTyped,
} from './PaginationData';
import type { ActionDto } from './ActionDto';
import {
    ActionDtoFromJSON,
    ActionDtoFromJSONTyped,
    ActionDtoToJSON,
    ActionDtoToJSONTyped,
} from './ActionDto';

/**
 * 
 * @export
 * @interface ActionDtoPagedListResult
 */
export interface ActionDtoPagedListResult {
    /**
     * 
     * @type {Array<ActionDto>}
     * @memberof ActionDtoPagedListResult
     */
    items: Array<ActionDto>;
    /**
     * 
     * @type {PaginationData}
     * @memberof ActionDtoPagedListResult
     */
    paging: PaginationData;
}

/**
 * Check if a given object implements the ActionDtoPagedListResult interface.
 */
export function instanceOfActionDtoPagedListResult(value: object): value is ActionDtoPagedListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    if (!('paging' in value) || value['paging'] === undefined) return false;
    return true;
}

export function ActionDtoPagedListResultFromJSON(json: any): ActionDtoPagedListResult {
    return ActionDtoPagedListResultFromJSONTyped(json, false);
}

export function ActionDtoPagedListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionDtoPagedListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(ActionDtoFromJSON)),
        'paging': PaginationDataFromJSON(json['paging']),
    };
}

export function ActionDtoPagedListResultToJSON(json: any): ActionDtoPagedListResult {
    return ActionDtoPagedListResultToJSONTyped(json, false);
}

export function ActionDtoPagedListResultToJSONTyped(value?: ActionDtoPagedListResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'items': ((value['items'] as Array<any>).map(ActionDtoToJSON)),
        'paging': PaginationDataToJSON(value['paging']),
    };
}

