/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ManagerDetails
 */
export interface ManagerDetails {
    /**
     * 
     * @type {string}
     * @memberof ManagerDetails
     */
    employeeId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ManagerDetails
     */
    fullName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ManagerDetails
     */
    email?: string | null;
}

/**
 * Check if a given object implements the ManagerDetails interface.
 */
export function instanceOfManagerDetails(value: object): value is ManagerDetails {
    return true;
}

export function ManagerDetailsFromJSON(json: any): ManagerDetails {
    return ManagerDetailsFromJSONTyped(json, false);
}

export function ManagerDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): ManagerDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'employeeId': json['employeeId'] == null ? undefined : json['employeeId'],
        'fullName': json['fullName'] == null ? undefined : json['fullName'],
        'email': json['email'] == null ? undefined : json['email'],
    };
}

export function ManagerDetailsToJSON(json: any): ManagerDetails {
    return ManagerDetailsToJSONTyped(json, false);
}

export function ManagerDetailsToJSONTyped(value?: ManagerDetails | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'employeeId': value['employeeId'],
        'fullName': value['fullName'],
        'email': value['email'],
    };
}

