/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AtrCycleStatuses } from './AtrCycleStatuses';
import {
    AtrCycleStatusesFromJSON,
    AtrCycleStatusesFromJSONTyped,
    AtrCycleStatusesToJSON,
    AtrCycleStatusesToJSONTyped,
} from './AtrCycleStatuses';

/**
 * 
 * @export
 * @interface AtrCycleDto
 */
export interface AtrCycleDto {
    /**
     * 
     * @type {string}
     * @memberof AtrCycleDto
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof AtrCycleDto
     */
    name: string;
    /**
     * 
     * @type {AtrCycleStatuses}
     * @memberof AtrCycleDto
     */
    status: AtrCycleStatuses;
}



/**
 * Check if a given object implements the AtrCycleDto interface.
 */
export function instanceOfAtrCycleDto(value: object): value is AtrCycleDto {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    return true;
}

export function AtrCycleDtoFromJSON(json: any): AtrCycleDto {
    return AtrCycleDtoFromJSONTyped(json, false);
}

export function AtrCycleDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): AtrCycleDto {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
        'status': AtrCycleStatusesFromJSON(json['status']),
    };
}

export function AtrCycleDtoToJSON(json: any): AtrCycleDto {
    return AtrCycleDtoToJSONTyped(json, false);
}

export function AtrCycleDtoToJSONTyped(value?: AtrCycleDto | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'status': AtrCycleStatusesToJSON(value['status']),
    };
}

