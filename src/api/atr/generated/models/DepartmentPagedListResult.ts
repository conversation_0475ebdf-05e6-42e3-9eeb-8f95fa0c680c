/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { Department } from './Department';
import {
    DepartmentFromJSON,
    DepartmentFromJSONTyped,
    DepartmentToJSON,
    DepartmentToJSONTyped,
} from './Department';
import type { PaginationData } from './PaginationData';
import {
    PaginationDataFromJSON,
    PaginationDataFromJSONTyped,
    PaginationDataToJSON,
    PaginationDataToJSONTyped,
} from './PaginationData';

/**
 * 
 * @export
 * @interface DepartmentPagedListResult
 */
export interface DepartmentPagedListResult {
    /**
     * 
     * @type {Array<Department>}
     * @memberof DepartmentPagedListResult
     */
    items: Array<Department>;
    /**
     * 
     * @type {PaginationData}
     * @memberof DepartmentPagedListResult
     */
    paging: PaginationData;
}

/**
 * Check if a given object implements the DepartmentPagedListResult interface.
 */
export function instanceOfDepartmentPagedListResult(value: object): value is DepartmentPagedListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    if (!('paging' in value) || value['paging'] === undefined) return false;
    return true;
}

export function DepartmentPagedListResultFromJSON(json: any): DepartmentPagedListResult {
    return DepartmentPagedListResultFromJSONTyped(json, false);
}

export function DepartmentPagedListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): DepartmentPagedListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(DepartmentFromJSON)),
        'paging': PaginationDataFromJSON(json['paging']),
    };
}

export function DepartmentPagedListResultToJSON(json: any): DepartmentPagedListResult {
    return DepartmentPagedListResultToJSONTyped(json, false);
}

export function DepartmentPagedListResultToJSONTyped(value?: DepartmentPagedListResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'items': ((value['items'] as Array<any>).map(DepartmentToJSON)),
        'paging': PaginationDataToJSON(value['paging']),
    };
}

