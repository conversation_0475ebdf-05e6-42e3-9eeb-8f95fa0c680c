/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * 
 * Launch
 * 
 * Transition
 * 
 * ReAssign
 * 
 * Export
 * @export
 */
export const ActionType = {
    Launch: 'Launch',
    Transition: 'Transition',
    ReAssign: 'ReAssign',
    Export: 'Export'
} as const;
export type ActionType = typeof ActionType[keyof typeof ActionType];


export function instanceOfActionType(value: any): boolean {
    for (const key in ActionType) {
        if (Object.prototype.hasOwnProperty.call(ActionType, key)) {
            if (ActionType[key as keyof typeof ActionType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function ActionTypeFromJSON(json: any): ActionType {
    return ActionTypeFromJSONTyped(json, false);
}

export function ActionTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionType {
    return json as ActionType;
}

export function ActionTypeToJSON(value?: ActionType | null): any {
    return value as any;
}

export function ActionTypeToJSONTyped(value: any, ignoreDiscriminator: boolean): ActionType {
    return value as ActionType;
}

