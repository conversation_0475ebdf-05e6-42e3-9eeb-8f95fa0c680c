/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { PaginationData } from './PaginationData';
import {
    PaginationDataFromJSON,
    PaginationDataFromJSONTyped,
    PaginationDataToJSON,
    PaginationDataToJSONTyped,
} from './PaginationData';
import type { SearchEmployee } from './SearchEmployee';
import {
    SearchEmployeeFromJSON,
    SearchEmployeeFromJSONTyped,
    SearchEmployeeToJSON,
    SearchEmployeeToJSONTyped,
} from './SearchEmployee';

/**
 * 
 * @export
 * @interface SearchEmployeePagedListResult
 */
export interface SearchEmployeePagedListResult {
    /**
     * 
     * @type {Array<SearchEmployee>}
     * @memberof SearchEmployeePagedListResult
     */
    items: Array<SearchEmployee>;
    /**
     * 
     * @type {PaginationData}
     * @memberof SearchEmployeePagedListResult
     */
    paging: PaginationData;
}

/**
 * Check if a given object implements the SearchEmployeePagedListResult interface.
 */
export function instanceOfSearchEmployeePagedListResult(value: object): value is SearchEmployeePagedListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    if (!('paging' in value) || value['paging'] === undefined) return false;
    return true;
}

export function SearchEmployeePagedListResultFromJSON(json: any): SearchEmployeePagedListResult {
    return SearchEmployeePagedListResultFromJSONTyped(json, false);
}

export function SearchEmployeePagedListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): SearchEmployeePagedListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(SearchEmployeeFromJSON)),
        'paging': PaginationDataFromJSON(json['paging']),
    };
}

export function SearchEmployeePagedListResultToJSON(json: any): SearchEmployeePagedListResult {
    return SearchEmployeePagedListResultToJSONTyped(json, false);
}

export function SearchEmployeePagedListResultToJSONTyped(value?: SearchEmployeePagedListResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'items': ((value['items'] as Array<any>).map(SearchEmployeeToJSON)),
        'paging': PaginationDataToJSON(value['paging']),
    };
}

