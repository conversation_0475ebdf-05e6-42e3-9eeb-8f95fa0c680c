/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { PaginationData } from './PaginationData';
import {
    PaginationDataFromJSON,
    PaginationDataFromJSONTyped,
    PaginationDataToJSON,
    PaginationDataToJSONTyped,
} from './PaginationData';
import type { PerformanceFormDto } from './PerformanceFormDto';
import {
    PerformanceFormDtoFromJSON,
    PerformanceFormDtoFromJSONTyped,
    PerformanceFormDtoToJSON,
    PerformanceFormDtoToJSONTyped,
} from './PerformanceFormDto';

/**
 * 
 * @export
 * @interface PerformanceFormDtoPagedListResult
 */
export interface PerformanceFormDtoPagedListResult {
    /**
     * 
     * @type {Array<PerformanceFormDto>}
     * @memberof PerformanceFormDtoPagedListResult
     */
    items: Array<PerformanceFormDto>;
    /**
     * 
     * @type {PaginationData}
     * @memberof PerformanceFormDtoPagedListResult
     */
    paging: PaginationData;
}

/**
 * Check if a given object implements the PerformanceFormDtoPagedListResult interface.
 */
export function instanceOfPerformanceFormDtoPagedListResult(value: object): value is PerformanceFormDtoPagedListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    if (!('paging' in value) || value['paging'] === undefined) return false;
    return true;
}

export function PerformanceFormDtoPagedListResultFromJSON(json: any): PerformanceFormDtoPagedListResult {
    return PerformanceFormDtoPagedListResultFromJSONTyped(json, false);
}

export function PerformanceFormDtoPagedListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): PerformanceFormDtoPagedListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(PerformanceFormDtoFromJSON)),
        'paging': PaginationDataFromJSON(json['paging']),
    };
}

export function PerformanceFormDtoPagedListResultToJSON(json: any): PerformanceFormDtoPagedListResult {
    return PerformanceFormDtoPagedListResultToJSONTyped(json, false);
}

export function PerformanceFormDtoPagedListResultToJSONTyped(value?: PerformanceFormDtoPagedListResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'items': ((value['items'] as Array<any>).map(PerformanceFormDtoToJSON)),
        'paging': PaginationDataToJSON(value['paging']),
    };
}

