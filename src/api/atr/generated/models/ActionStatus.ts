/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * 
 * Draft
 * 
 * InProgress
 * 
 * Pending
 * 
 * Completed
 * 
 * Canceled
 * 
 * Cancelling
 * 
 * Failed
 * @export
 */
export const ActionStatus = {
    Draft: 'Draft',
    InProgress: 'InProgress',
    Pending: 'Pending',
    Completed: 'Completed',
    Canceled: 'Canceled',
    Cancelling: 'Cancelling',
    Failed: 'Failed'
} as const;
export type ActionStatus = typeof ActionStatus[keyof typeof ActionStatus];


export function instanceOfActionStatus(value: any): boolean {
    for (const key in ActionStatus) {
        if (Object.prototype.hasOwnProperty.call(ActionStatus, key)) {
            if (ActionStatus[key as keyof typeof ActionStatus] === value) {
                return true;
            }
        }
    }
    return false;
}

export function ActionStatusFromJSON(json: any): ActionStatus {
    return ActionStatusFromJSONTyped(json, false);
}

export function ActionStatusFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionStatus {
    return json as ActionStatus;
}

export function ActionStatusToJSON(value?: ActionStatus | null): any {
    return value as any;
}

export function ActionStatusToJSONTyped(value: any, ignoreDiscriminator: boolean): ActionStatus {
    return value as ActionStatus;
}

