/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface CreateJobResponse
 */
export interface CreateJobResponse {
    /**
     * 
     * @type {string}
     * @memberof CreateJobResponse
     */
    jobId: string;
}

/**
 * Check if a given object implements the CreateJobResponse interface.
 */
export function instanceOfCreateJobResponse(value: object): value is CreateJobResponse {
    if (!('jobId' in value) || value['jobId'] === undefined) return false;
    return true;
}

export function CreateJobResponseFromJSON(json: any): CreateJobResponse {
    return CreateJobResponseFromJSONTyped(json, false);
}

export function CreateJobResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateJobResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'jobId': json['jobId'],
    };
}

export function CreateJobResponseToJSON(json: any): CreateJobResponse {
    return CreateJobResponseToJSONTyped(json, false);
}

export function CreateJobResponseToJSONTyped(value?: CreateJobResponse | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'jobId': value['jobId'],
    };
}

