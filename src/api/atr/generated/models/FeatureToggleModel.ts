/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface FeatureToggleModel
 */
export interface FeatureToggleModel {
    /**
     * 
     * @type {boolean}
     * @memberof FeatureToggleModel
     */
    enabled: boolean;
}

/**
 * Check if a given object implements the FeatureToggleModel interface.
 */
export function instanceOfFeatureToggleModel(value: object): value is FeatureToggleModel {
    if (!('enabled' in value) || value['enabled'] === undefined) return false;
    return true;
}

export function FeatureToggleModelFromJSON(json: any): FeatureToggleModel {
    return FeatureToggleModelFromJSONTyped(json, false);
}

export function FeatureToggleModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): FeatureToggleModel {
    if (json == null) {
        return json;
    }
    return {
        
        'enabled': json['enabled'],
    };
}

export function FeatureToggleModelToJSON(json: any): FeatureToggleModel {
    return FeatureToggleModelToJSONTyped(json, false);
}

export function FeatureToggleModelToJSONTyped(value?: FeatureToggleModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'enabled': value['enabled'],
    };
}

