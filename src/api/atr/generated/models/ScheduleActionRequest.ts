/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ScheduleActionRequest
 */
export interface ScheduleActionRequest {
    /**
     * 
     * @type {string}
     * @memberof ScheduleActionRequest
     */
    id?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ScheduleActionRequest
     */
    inputFileId?: string | null;
}

/**
 * Check if a given object implements the ScheduleActionRequest interface.
 */
export function instanceOfScheduleActionRequest(value: object): value is ScheduleActionRequest {
    return true;
}

export function ScheduleActionRequestFromJSON(json: any): ScheduleActionRequest {
    return ScheduleActionRequestFromJSONTyped(json, false);
}

export function ScheduleActionRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): ScheduleActionRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'inputFileId': json['inputFileId'] == null ? undefined : json['inputFileId'],
    };
}

export function ScheduleActionRequestToJSON(json: any): ScheduleActionRequest {
    return ScheduleActionRequestToJSONTyped(json, false);
}

export function ScheduleActionRequestToJSONTyped(value?: ScheduleActionRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'inputFileId': value['inputFileId'],
    };
}

