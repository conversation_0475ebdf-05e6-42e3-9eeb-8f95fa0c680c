/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface GetEmployeeDetailsRequest
 */
export interface GetEmployeeDetailsRequest {
    /**
     * 
     * @type {string}
     * @memberof GetEmployeeDetailsRequest
     */
    email: string;
}

/**
 * Check if a given object implements the GetEmployeeDetailsRequest interface.
 */
export function instanceOfGetEmployeeDetailsRequest(value: object): value is GetEmployeeDetailsRequest {
    if (!('email' in value) || value['email'] === undefined) return false;
    return true;
}

export function GetEmployeeDetailsRequestFromJSON(json: any): GetEmployeeDetailsRequest {
    return GetEmployeeDetailsRequestFromJSONTyped(json, false);
}

export function GetEmployeeDetailsRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): GetEmployeeDetailsRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'email': json['email'],
    };
}

export function GetEmployeeDetailsRequestToJSON(json: any): GetEmployeeDetailsRequest {
    return GetEmployeeDetailsRequestToJSONTyped(json, false);
}

export function GetEmployeeDetailsRequestToJSONTyped(value?: GetEmployeeDetailsRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'email': value['email'],
    };
}

