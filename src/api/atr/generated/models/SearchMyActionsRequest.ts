/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SearchMyActionsRequest
 */
export interface SearchMyActionsRequest {
    /**
     * 
     * @type {number}
     * @memberof SearchMyActionsRequest
     */
    pageNumber?: number | null;
    /**
     * 
     * @type {number}
     * @memberof SearchMyActionsRequest
     */
    pageSize?: number | null;
}

/**
 * Check if a given object implements the SearchMyActionsRequest interface.
 */
export function instanceOfSearchMyActionsRequest(value: object): value is SearchMyActionsRequest {
    return true;
}

export function SearchMyActionsRequestFromJSON(json: any): SearchMyActionsRequest {
    return SearchMyActionsRequestFromJSONTyped(json, false);
}

export function SearchMyActionsRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): SearchMyActionsRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'pageNumber': json['pageNumber'] == null ? undefined : json['pageNumber'],
        'pageSize': json['pageSize'] == null ? undefined : json['pageSize'],
    };
}

export function SearchMyActionsRequestToJSON(json: any): SearchMyActionsRequest {
    return SearchMyActionsRequestToJSONTyped(json, false);
}

export function SearchMyActionsRequestToJSONTyped(value?: SearchMyActionsRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'pageNumber': value['pageNumber'],
        'pageSize': value['pageSize'],
    };
}

