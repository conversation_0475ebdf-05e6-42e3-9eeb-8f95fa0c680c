/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { PaginationData } from './PaginationData';
import {
    PaginationDataFromJSON,
    PaginationDataFromJSONTyped,
    PaginationDataToJSON,
    PaginationDataToJSONTyped,
} from './PaginationData';
import type { Position } from './Position';
import {
    PositionFromJSON,
    PositionFromJSONTyped,
    PositionToJSON,
    PositionToJSONTyped,
} from './Position';

/**
 * 
 * @export
 * @interface PositionPagedListResult
 */
export interface PositionPagedListResult {
    /**
     * 
     * @type {Array<Position>}
     * @memberof PositionPagedListResult
     */
    items: Array<Position>;
    /**
     * 
     * @type {PaginationData}
     * @memberof PositionPagedListResult
     */
    paging: PaginationData;
}

/**
 * Check if a given object implements the PositionPagedListResult interface.
 */
export function instanceOfPositionPagedListResult(value: object): value is PositionPagedListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    if (!('paging' in value) || value['paging'] === undefined) return false;
    return true;
}

export function PositionPagedListResultFromJSON(json: any): PositionPagedListResult {
    return PositionPagedListResultFromJSONTyped(json, false);
}

export function PositionPagedListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): PositionPagedListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(PositionFromJSON)),
        'paging': PaginationDataFromJSON(json['paging']),
    };
}

export function PositionPagedListResultToJSON(json: any): PositionPagedListResult {
    return PositionPagedListResultToJSONTyped(json, false);
}

export function PositionPagedListResultToJSONTyped(value?: PositionPagedListResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'items': ((value['items'] as Array<any>).map(PositionToJSON)),
        'paging': PaginationDataToJSON(value['paging']),
    };
}

