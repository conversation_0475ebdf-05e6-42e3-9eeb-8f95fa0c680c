/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SortDirection } from './SortDirection';
import {
    SortDirectionFromJSON,
    SortDirectionFromJSONTyped,
    SortDirectionToJSON,
    SortDirectionToJSONTyped,
} from './SortDirection';

/**
 * 
 * @export
 * @interface OrderBy
 */
export interface OrderBy {
    /**
     * 
     * @type {string}
     * @memberof OrderBy
     */
    field: string;
    /**
     * 
     * @type {SortDirection}
     * @memberof OrderBy
     */
    direction: SortDirection;
}



/**
 * Check if a given object implements the OrderBy interface.
 */
export function instanceOfOrderBy(value: object): value is OrderBy {
    if (!('field' in value) || value['field'] === undefined) return false;
    if (!('direction' in value) || value['direction'] === undefined) return false;
    return true;
}

export function OrderByFromJSON(json: any): OrderBy {
    return OrderByFromJSONTyped(json, false);
}

export function OrderByFromJSONTyped(json: any, ignoreDiscriminator: boolean): OrderBy {
    if (json == null) {
        return json;
    }
    return {
        
        'field': json['field'],
        'direction': SortDirectionFromJSON(json['direction']),
    };
}

export function OrderByToJSON(json: any): OrderBy {
    return OrderByToJSONTyped(json, false);
}

export function OrderByToJSONTyped(value?: OrderBy | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'field': value['field'],
        'direction': SortDirectionToJSON(value['direction']),
    };
}

