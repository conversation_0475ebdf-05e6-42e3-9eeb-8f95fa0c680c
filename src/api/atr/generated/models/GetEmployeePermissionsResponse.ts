/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.123-500-on-history
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface GetEmployeePermissionsResponse
 */
export interface GetEmployeePermissionsResponse {
    /**
     * 
     * @type {string}
     * @memberof GetEmployeePermissionsResponse
     */
    fullName: string;
    /**
     * 
     * @type {string}
     * @memberof GetEmployeePermissionsResponse
     */
    email?: string | null;
    /**
     * 
     * @type {Array<{ [key: string]: Array<string>; }>}
     * @memberof GetEmployeePermissionsResponse
     */
    permissions: Array<{ [key: string]: Array<string>; }>;
}

/**
 * Check if a given object implements the GetEmployeePermissionsResponse interface.
 */
export function instanceOfGetEmployeePermissionsResponse(value: object): value is GetEmployeePermissionsResponse {
    if (!('fullName' in value) || value['fullName'] === undefined) return false;
    if (!('permissions' in value) || value['permissions'] === undefined) return false;
    return true;
}

export function GetEmployeePermissionsResponseFromJSON(json: any): GetEmployeePermissionsResponse {
    return GetEmployeePermissionsResponseFromJSONTyped(json, false);
}

export function GetEmployeePermissionsResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): GetEmployeePermissionsResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'fullName': json['fullName'],
        'email': json['email'] == null ? undefined : json['email'],
        'permissions': json['permissions'],
    };
}

export function GetEmployeePermissionsResponseToJSON(json: any): GetEmployeePermissionsResponse {
    return GetEmployeePermissionsResponseToJSONTyped(json, false);
}

export function GetEmployeePermissionsResponseToJSONTyped(value?: GetEmployeePermissionsResponse | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'fullName': value['fullName'],
        'email': value['email'],
        'permissions': value['permissions'],
    };
}

