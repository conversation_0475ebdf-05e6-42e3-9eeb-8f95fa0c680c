import { useState } from 'react';

import { Modal, Button } from '@ot/onetalent-ui-kit';

import { EmployeeDetailModalContent } from './EmployeeDetailModalContent';

import { useEmployeeByIdOrEmail } from '../hooks';

interface EmployeeDetailModalProps {
  selectedEmployeeId: string;
  selectedEmployeeEmail: string;
  onClose: () => void;
  isOpen?: boolean;
}

export const EmployeeDetailModal = ({
  selectedEmployeeId,
  selectedEmployeeEmail,
  onClose,
  isOpen = true
}: EmployeeDetailModalProps) => {
  const [isExpandedAll, setIsExpandedAll] = useState(true);

  const { data, isFetching } = useEmployeeByIdOrEmail(
    selectedEmployeeEmail,
    selectedEmployeeId
  );

  const handleCollapseAll = () => {
    setIsExpandedAll((prev) => !prev);
  };

  return (
    <Modal
      isOpen={isOpen}
      header="Employee Details"
      variant="Secondary"
      onClose={onClose}
      showClose
      footer={
        <>
          <Button
            variant="Secondary"
            className="w-140 px-8 py-12"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            className="w-140 px-8 py-12"
            disabled={isFetching}
            onClick={handleCollapseAll}
          >
            Collapse All
          </Button>
        </>
      }
    >
      <div
        data-attributes="EmployeeDetailContent"
        className="item-center flex h-[60dvh] min-h-[490px] justify-center"
      >
        <EmployeeDetailModalContent
          data={data}
          isFetching={isFetching}
          isExpandedAll={isExpandedAll}
        />
      </div>
    </Modal>
  );
};
