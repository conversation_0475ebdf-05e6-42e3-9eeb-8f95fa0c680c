import { CollapsedRow } from '@oh/components';

import { EmployeeCard } from 'api/support/generated';

import { EmployeeInformationRow } from './EmployeeInformationRow';
import { ManagerInfo } from './ManagerInfo';

interface EmployeeDetailsInformationProps {
  employeeData: EmployeeCard;
  isExpandedAll: boolean;
  isPrimary?: boolean;
}

export const EmployeeDetailsInformation = ({
  employeeData,
  isExpandedAll,
  isPrimary = false
}: EmployeeDetailsInformationProps) => {
  const {
    companyName,
    companyCode,
    positionName,
    positionId,
    lineManager,
    b2BManager
  } = employeeData;

  return (
    <>
      <div className="rounded-xl bg-white">
        <CollapsedRow
          headerItemsCount={1}
          isInitialExpanded={isExpandedAll}
          headerClassName="typography--primary text-[14px] font-medium leading-[18px] pb-10 !border-none cursor-pointer"
          bodyClassName="!px-20"
          rowData={[
            {
              variableData: 'header',
              variableName: 'Row header',
              variableValue: isPrimary
                ? 'Employee Information'
                : 'Secondary Employee Information'
            },
            {
              variableData: 'body',
              variableName: '',
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              variableValue: (
                <>
                  <EmployeeInformationRow
                    fieldName="Primary Company"
                    fieldValue={companyName ?? '-'}
                  />
                  <EmployeeInformationRow
                    fieldName="Company Code"
                    fieldValue={companyCode ?? '-'}
                  />
                  <EmployeeInformationRow
                    fieldName="Position Title"
                    fieldValue={positionName ?? '-'}
                  />
                  <EmployeeInformationRow
                    fieldName="Position ID"
                    fieldValue={positionId ?? '-'}
                  />
                  <ManagerInfo
                    managerData={lineManager}
                    fieldName="Line Manager"
                  />
                  <ManagerInfo
                    managerData={b2BManager}
                    fieldName="Back to Back manager"
                  />
                </>
              )
            }
          ]}
        />
      </div>
    </>
  );
};
