import { useMemo } from 'react';

import { DataColumnProps } from '@epam/uui-core';

import { SearchEmployeeUniqueId } from 'support/general/pages/EmployeePage/hooks';

import { ColumnContext } from './columnDefinitions';
import { ColumnConfig, getColumnsByConfig } from './getColumnsByConfig';

export function useColumns(ctx?: ColumnContext, columnConfig?: ColumnConfig) {
  const columns: DataColumnProps<SearchEmployeeUniqueId>[] = useMemo(
    () => getColumnsByConfig(ctx || {}, columnConfig),
    [ctx, columnConfig]
  );

  return columns;
}
