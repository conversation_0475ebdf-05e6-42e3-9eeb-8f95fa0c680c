import { FC } from 'react';

import isArray from 'lodash/isArray';

import { useSearchEmployees } from '@/support/general/pages/EmployeePage/hooks/useSearchEmployees';
import { TableFiltersType } from '@/support/general/pages/EmployeePage/hooks/utils';
import {
  PerformanceDataTable,
  useDataSourceState
} from '@/support/shared/components/GenericDataTable';
import { IllustrationMessageError } from '@/support/shared/components/IllustrationMessageError';
import { IllustrationMessageNoResults } from '@/support/shared/components/IllustrationMessageNoResults';

import { ColumnConfig, useColumns } from './columns';
import { INITIAL_ORDER_BY, PAGE_SIZE } from './consts';
import { transformSorting } from './utils';

import { EmployeesDataFilterType } from '../EmployeesDataFilter';

export interface EmployeesDataTableProps {
  searchQuery?: string;
  filters: TableFiltersType;
  columnConfig?: ColumnConfig;
}

export const EmployeesDataTable: FC<EmployeesDataTableProps> = ({
  searchQuery,
  filters,
  columnConfig
}) => {
  const { dataSource, parameters } = useDataSourceState<
    string,
    EmployeesDataFilterType
  >({
    pageSize: PAGE_SIZE,
    search: searchQuery,
    filters,
    initialPageNumber: 1,
    initialOrderBy: INITIAL_ORDER_BY
  });

  const queryResult = useSearchEmployees({
    search: searchQuery,
    pageNumber: parameters.pageNumber ?? 1,
    pageSize: parameters.pageSize ?? PAGE_SIZE,
    orderBy: transformSorting(parameters.orderBy),
    departmentIds: isArray(filters?.departmentIds)
      ? filters?.departmentIds.filter(Boolean).map(Number)
      : [],
    positionTitleIds: isArray(filters?.positionTitleIds)
      ? filters?.positionTitleIds.filter(Boolean)
      : [],
    groupCompanyCodes: isArray(filters?.groupCompanyCodes)
      ? filters?.groupCompanyCodes.filter(Boolean)
      : []
  });

  const columns = useColumns({ searchQuery }, columnConfig);

  return (
    <div
      data-attributes="EmployeesDataTable"
      className="flex flex-col gap-20 rounded-xl bg-surface-grey_0 p-12"
    >
      <PerformanceDataTable
        {...dataSource}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        queryResult={queryResult}
        columns={columns}
        renderNoResults={IllustrationMessageNoResults}
        renderError={IllustrationMessageError}
        maxVisibleRowsWithoutScroll={8}
        paginationContainerClassName="!mb-20"
        paginationSize="24"
      />
    </div>
  );
};
