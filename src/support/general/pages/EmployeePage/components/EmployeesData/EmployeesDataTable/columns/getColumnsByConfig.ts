import { DataColumnProps } from '@epam/uui-core';

import { SearchEmployeeUniqueId } from 'support/general/pages/EmployeePage/hooks';

import { ColumnContext, columnDefinitions } from './columnDefinitions';

type ColumnKey = keyof typeof columnDefinitions;

export type ColumnConfig = Partial<
  Record<ColumnKey, Partial<DataColumnProps<SearchEmployeeUniqueId>>>
>;

export const getColumnsByConfig = (
  ctx: ColumnContext,
  columnConfig?: ColumnConfig
): DataColumnProps<SearchEmployeeUniqueId>[] => {
  if (!columnConfig) {
    return Object.values(columnDefinitions).map((builder) => builder(ctx));
  }

  return Object.entries(columnConfig).map(([key, override]) => {
    const builder = columnDefinitions[key as Column<PERSON>ey];

    return {
      ...builder(ctx),
      ...override
    };
  });
};
