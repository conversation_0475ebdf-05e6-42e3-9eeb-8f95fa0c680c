import { FC, useState } from 'react';

import { Button, ButtonSize, ButtonVariant } from '@ot/onetalent-ui-kit';

import { AdminGeneralService } from '@/api/support/index';
import { EmployeeDetailModal } from '@/support/general/pages/EmployeePage/components/EmployeeDetailModal';
import { Dropdown } from '@/support/shared/components/Dropdown';
import { Icon } from '@/support/shared/components/Icons';

import { ActionsDropdownContent } from './ActionsDropdownContent';

const getKebabButton =
  (isEnabled: boolean) =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ({ onClick }: { onClick?: ((e?: any) => void) | undefined }) => (
    <Button
      onClick={onClick}
      size={ButtonSize.Medium}
      variant={ButtonVariant.Tertiary}
      className="w-40 min-w-40 p-[10px]"
      disabled={isEnabled}
    >
      <Icon name="kebab" />
    </Button>
  );

export type ActionsDropdownProps = {
  item: AdminGeneralService.SearchEmployee;
};

export const ActionsDropdown: FC<ActionsDropdownProps> = ({ item }) => {
  const [isOpenEmployeeDetailsModal, setIsOpenEmployeeDetailsModal] =
    useState(false);

  if (!item.id) return null;

  return (
    <>
      <Dropdown
        renderContent={(onClose) => (
          <div className="flex w-[240px] flex-col rounded-lg bg-surface-grey_0 p-8 shadow-action_bar">
            <ActionsDropdownContent
              onClose={onClose}
              onOpenEmployeeDetailsModal={() =>
                setIsOpenEmployeeDetailsModal(true)
              }
            />
          </div>
        )}
        renderChildren={getKebabButton(!item.id && !item.email)}
      />

      {isOpenEmployeeDetailsModal && (
        <EmployeeDetailModal
          selectedEmployeeId={item.id || ''}
          selectedEmployeeEmail={item.email || ''}
          onClose={() => setIsOpenEmployeeDetailsModal(false)}
        />
      )}
    </>
  );
};
