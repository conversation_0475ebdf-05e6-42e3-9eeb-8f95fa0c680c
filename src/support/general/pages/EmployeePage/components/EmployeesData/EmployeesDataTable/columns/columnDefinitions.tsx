import { DataColumnProps } from '@epam/uui-core';

import { SearchEmployeeUniqueId } from 'support/general/pages/EmployeePage/hooks';

import { CellContent } from '@/support/shared/components/CellContent';
import { Icon } from '@/support/shared/components/Icons';
import { Text } from '@/support/shared/components/Text';
import { UserProfileAvatar } from '@/support/shared/components/UserProfileAvatar';
import { openEmployeeAtNewTab } from '@/support/shared/utils/urls/urls';

import { ActionsDropdown } from './ActionsDropdown';

import { COLUMNS_KEYS } from '../consts';

export type ColumnContext = {
  searchQuery?: string;
};

type ColumnBuilder = (
  ctx: ColumnContext
) => DataColumnProps<SearchEmployeeUniqueId>;

type ColumnDefinitions = Record<
  (typeof COLUMNS_KEYS)[keyof typeof COLUMNS_KEYS],
  ColumnBuilder
>;

export const columnDefinitions: ColumnDefinitions = {
  [COLUMNS_KEYS.USER_ID]: () => ({
    key: COLUMNS_KEYS.USER_ID,
    caption: 'User ID',
    renderTooltip: () => null,
    render: (item) => (
      <CellContent classNames="">{item?.employeeId ?? '-'}</CellContent>
    ),
    width: 100,
    minWidth: 100,
    isSortable: true
  }),

  [COLUMNS_KEYS.USER_NAME]: () => ({
    key: COLUMNS_KEYS.USER_NAME,
    caption: 'User Name',
    renderTooltip: () => null,
    render: (item) => (
      <CellContent classNames="line-clamp-none flex">
        <UserProfileAvatar
          size={30}
          email={item?.email}
          name={item.fullName}
          className="mr-8"
        />
        <Text className="mt-8 break-all text-body-2-regular">
          {item?.fullName ?? '-'}
        </Text>
      </CellContent>
    ),
    width: 100,
    grow: 1,
    isSortable: true
  }),

  [COLUMNS_KEYS.GROUP_COMPANY]: () => ({
    key: COLUMNS_KEYS.GROUP_COMPANY,
    caption: 'Group Company',
    render: (item) => (
      <CellContent classNames="line-clamp-none">
        <Text className="break-all text-body-2-regular">
          {item?.groupCompany ?? '-'}
        </Text>
      </CellContent>
    ),
    width: 100,
    grow: 1,
    isSortable: true
  }),

  [COLUMNS_KEYS.DEPARTEMENT]: () => ({
    key: COLUMNS_KEYS.DEPARTEMENT,
    caption: 'Departement',
    render: (item) => (
      <CellContent classNames="line-clamp-none">
        <Text className="break-all text-body-2-regular">
          {item?.department ?? '-'}
        </Text>
      </CellContent>
    ),
    width: 100,
    grow: 1,
    isSortable: true
  }),

  [COLUMNS_KEYS.POSITION_TITLE]: () => ({
    key: COLUMNS_KEYS.POSITION_TITLE,
    caption: 'Position Title',
    render: (item) => (
      <CellContent classNames="line-clamp-none">
        <Text className="break-all text-body-2-regular">
          {item?.positionTitle ?? '-'}
        </Text>
      </CellContent>
    ),
    width: 100,
    grow: 1,
    isSortable: true
  }),

  [COLUMNS_KEYS.ATR_USER_GROUP_ID]: () => ({
    key: COLUMNS_KEYS.ATR_USER_GROUP_ID,
    caption: 'ATR User Group ID',
    render: (item) => (
      <CellContent classNames="">{item?.atrUserGroupId ?? '-'}</CellContent>
    ),
    width: 100,
    grow: 1,
    isSortable: true
  }),

  [COLUMNS_KEYS.LINE_MANAGER]: () => ({
    key: COLUMNS_KEYS.LINE_MANAGER,
    caption: 'Manager (LM)',
    render: (item) => (
      <CellContent
        onClick={() =>
          item?.lineManagerEmployeeId && item?.lineManagerEmployeeEmail
            ? openEmployeeAtNewTab(
                item.lineManagerEmployeeId,
                item.lineManagerEmployeeEmail
              )
            : undefined
        }
        classNames="line-clamp-none flex"
      >
        {item?.lineManagerEmployeeName && (
          <UserProfileAvatar
            size={30}
            email={item?.lineManagerEmployeeEmail}
            name={item.lineManagerEmployeeName}
            className="mr-8"
          />
        )}
        <Text className="mt-8 break-all text-body-2-regular">
          {item?.lineManagerEmployeeName ?? '-'}
        </Text>
      </CellContent>
    ),
    width: 100,
    grow: 1,
    isSortable: true
  }),

  [COLUMNS_KEYS.B2B_MANAGER]: () => ({
    key: COLUMNS_KEYS.B2B_MANAGER,
    caption: 'Manager (B2B)',
    render: (item) => (
      <CellContent
        onClick={
          item?.managerB2BId && item?.managerB2BEmail
            ? () =>
                openEmployeeAtNewTab(
                  item.managerB2BId || '',
                  item.managerB2BEmail || ''
                )
            : undefined
        }
        classNames="line-clamp-none flex"
      >
        {item?.managerB2BName && (
          <UserProfileAvatar
            size={30}
            email={item?.managerB2BEmail}
            name={item.managerB2BName}
            className="mr-8"
          />
        )}
        <Text className="mt-8 break-all text-body-2-regular">
          {item?.managerB2BName ?? '-'}
        </Text>
      </CellContent>
    ),
    width: 100,
    grow: 1,
    isSortable: true
  }),

  [COLUMNS_KEYS.EMAIL]: () => ({
    key: COLUMNS_KEYS.EMAIL,
    caption: 'Email',
    render: (item) => (
      <CellContent classNames="line-clamp-none">
        <Text className="break-words text-body-2-regular">
          {item?.email ?? '-'}
        </Text>
      </CellContent>
    ),
    width: 100,
    grow: 1,
    isSortable: true
  }),

  [COLUMNS_KEYS.ACTIONS]: () => ({
    key: COLUMNS_KEYS.ACTIONS,
    caption: <Icon name="settingLineLight" />,
    textAlign: 'right',
    renderTooltip: () => null,
    render: (item) => {
      return (
        <CellContent>
          <ActionsDropdown item={item} />
        </CellContent>
      );
    },
    width: 80
  })
};
