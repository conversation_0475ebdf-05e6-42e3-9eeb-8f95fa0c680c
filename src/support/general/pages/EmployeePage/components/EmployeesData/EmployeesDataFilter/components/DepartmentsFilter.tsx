import { DropdownMultiselect, DropdownOption } from '@ot/onetalent-ui-kit';

import { Department } from '@/api/support/generated';
import { useGetDepartmentsFilters } from '@/support/general/pages/EmployeePage/hooks/useGetDepartmentsFilters';
import { FilterValue } from '@/support/general/pages/EmployeePage/hooks/utils';

import { useFilters } from './useFilters';

import { EmployeesDataFilterType } from '../EmployeesDataFilter';

const prepareOptions = (
  departmentsOptions: Department[] | undefined,
  isFilterVisible: (key: keyof EmployeesDataFilterType) => boolean
) => {
  const isDataAvailable =
    isFilterVisible('groupCompanyCodes') && !!departmentsOptions?.length;

  if (!isDataAvailable) {
    return null;
  }

  return departmentsOptions?.map((item) => ({ ...item, title: item.name }));
};

interface DepartmentsFilterProps {
  filters: FilterValue;
  onFilterChange: (
    category: keyof EmployeesDataFilterType,
    selectedValues: DropdownOption[]
  ) => void;
  isFilterVisible: (key: keyof EmployeesDataFilterType) => boolean;
}

export const DepartmentsFilter = ({
  filters,
  onFilterChange,
  isFilterVisible
}: DepartmentsFilterProps) => {
  const { preparedOptions, idsValue, loadMore, isError, hasMore } =
    useFilters<Department>(
      useGetDepartmentsFilters,
      prepareOptions,
      isFilterVisible,
      filters
    );

  return (
    <>
      <DropdownMultiselect
        label=""
        fallbackPlacements={['bottom', 'top']}
        name="departmentIds"
        placeholder="Select one or more department"
        placement="bottom"
        showClear
        size="Large"
        variant="Primary"
        className="w-[400px]"
        disabled={isError}
        options={preparedOptions}
        value={idsValue}
        onChange={(selectedValues: DropdownOption[]) =>
          onFilterChange('departmentIds', selectedValues)
        }
        onLoadMore={loadMore}
        hasMore={hasMore}
      />
    </>
  );
};
