import { DropdownMultiselect, DropdownOption } from '@ot/onetalent-ui-kit';

import { Company } from '@/api/support/generated';
import { useGetGroupCompaniesFilters } from '@/support/general/pages/EmployeePage/hooks/useGetGroupCompaniesFilters';
import { FilterValue } from '@/support/general/pages/EmployeePage/hooks/utils';

import { useFilters } from './useFilters';

import { EmployeesDataFilterType } from '../EmployeesDataFilter';

const prepareOptions = (
  groupCompaniesOptions: Company[] | undefined,
  isFilterVisible: (key: keyof EmployeesDataFilterType) => boolean
) => {
  const isDataAvailable =
    isFilterVisible('groupCompanyCodes') && !!groupCompaniesOptions?.length;

  if (!isDataAvailable) {
    return null;
  }

  return groupCompaniesOptions?.map((item) => ({
    id: item.code,
    title: item.name
  }));
};

interface GroupCompaniesFilterProps {
  filters: FilterValue;
  onFilterChange: (
    category: keyof EmployeesDataFilterType,
    selectedValues: DropdownOption[]
  ) => void;
  isFilterVisible: (key: keyof EmployeesDataFilterType) => boolean;
}

export const GroupCompaniesFilter = ({
  filters,
  onFilterChange,
  isFilterVisible
}: GroupCompaniesFilterProps) => {
  const { preparedOptions, idsValue, loadMore, isError, hasMore } =
    useFilters<Company>(
      useGetGroupCompaniesFilters,
      prepareOptions,
      isFilterVisible,
      filters
    );

  return (
    <>
      <DropdownMultiselect
        label=""
        fallbackPlacements={['bottom', 'top']}
        name="groupCompanyCodes"
        placeholder="Select one or more group companies"
        placement="bottom"
        showClear
        size="Large"
        variant="Primary"
        className="w-[400px]"
        disabled={isError}
        options={preparedOptions}
        value={idsValue}
        onChange={(selectedValues: DropdownOption[]) =>
          onFilterChange('groupCompanyCodes', selectedValues)
        }
        onLoadMore={loadMore}
        hasMore={hasMore}
      />
    </>
  );
};
