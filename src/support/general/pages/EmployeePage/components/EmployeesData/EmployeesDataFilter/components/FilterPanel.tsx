import { useState } from 'react';

import { DropdownOption } from '@ot/onetalent-ui-kit';
import isEqual from 'lodash/isEqual';

import { TableFiltersType } from '@/support/general/pages/EmployeePage/hooks/utils';
import { Stack, StackDirection } from '@/support/shared/components/Stack';

import { DepartmentsFilter } from './DepartmentsFilter';
import { FilterButtons } from './FilterButtons';
import { GroupCompaniesFilter } from './GroupCompaniesFilter';
import { PositionTitlesFilter } from './PositionTitlesFilter';

import { EmployeesDataFilterType, FilterProps } from '../EmployeesDataFilter';

export const FilterPanel = ({
  initialFilters,
  visibleFilters,
  onApply
}: FilterProps) => {
  const [filters, setFilters] = useState(initialFilters);

  const handleFilterChange = (
    category: keyof EmployeesDataFilterType,
    selectedValues: DropdownOption[]
  ) => {
    const mappedSelectedValues = selectedValues.map((item) => item.id);
    const updatedFilters = { ...filters, [category]: mappedSelectedValues };

    setFilters(updatedFilters as TableFiltersType);
  };

  const handleClearFilters = () => {
    setFilters({
      search: initialFilters.search,
      groupCompanyCodes: [],
      departmentIds: [],
      positionTitleIds: []
    });
  };

  const handleApplyFilters = () => {
    onApply(filters);
  };

  const isFilterChanged = !isEqual(initialFilters, filters);

  const isFilterVisible = (key: keyof EmployeesDataFilterType): boolean =>
    !visibleFilters || visibleFilters.includes(key);

  return (
    <Stack gap={16} justify="start" direction={StackDirection.ROW} wrap="wrap">
      <GroupCompaniesFilter
        filters={filters?.groupCompanyCodes}
        onFilterChange={handleFilterChange}
        isFilterVisible={isFilterVisible}
      />

      <DepartmentsFilter
        filters={filters?.departmentIds}
        onFilterChange={handleFilterChange}
        isFilterVisible={isFilterVisible}
      />

      <PositionTitlesFilter
        filters={filters?.positionTitleIds}
        onFilterChange={handleFilterChange}
        isFilterVisible={isFilterVisible}
      />

      {isFilterChanged && (
        <FilterButtons
          onClearFilters={handleClearFilters}
          onApplyFilters={handleApplyFilters}
        />
      )}
    </Stack>
  );
};
