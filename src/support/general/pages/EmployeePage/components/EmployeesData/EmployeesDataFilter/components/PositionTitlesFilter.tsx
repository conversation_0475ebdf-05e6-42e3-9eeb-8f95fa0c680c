import { DropdownMultiselect, DropdownOption } from '@ot/onetalent-ui-kit';

import { Position } from '@/api/support/generated';
import { useGetPositionTitlesFilters } from '@/support/general/pages/EmployeePage/hooks/usePositionTitlesFilters';
import { FilterValue } from '@/support/general/pages/EmployeePage/hooks/utils';

import { useFilters } from './useFilters';

import { EmployeesDataFilterType } from '../EmployeesDataFilter';

const prepareOptions = (
  positionTitlesOptions: Position[] | undefined,
  isFilterVisible: (key: keyof EmployeesDataFilterType) => boolean
) => {
  const isDataAvailable =
    isFilterVisible('positionTitleIds') && !!positionTitlesOptions?.length;

  if (!isDataAvailable) {
    return null;
  }

  return positionTitlesOptions?.map((item) => ({ ...item, title: item.name }));
};

interface PositionTitlesFilterProps {
  filters: FilterValue;
  onFilterChange: (
    category: keyof EmployeesDataFilterType,
    selectedValues: DropdownOption[]
  ) => void;
  isFilterVisible: (key: keyof EmployeesDataFilterType) => boolean;
}

export const PositionTitlesFilter = ({
  filters,
  onFilterChange,
  isFilterVisible
}: PositionTitlesFilterProps) => {
  const { preparedOptions, idsValue, loadMore, isError, hasMore } =
    useFilters<Position>(
      useGetPositionTitlesFilters,
      prepareOptions,
      isFilterVisible,
      filters
    );

  return (
    <>
      <DropdownMultiselect
        label=""
        fallbackPlacements={['bottom', 'top']}
        name="positionTitleIds"
        placeholder="Select one or more position title"
        placement="bottom"
        showClear
        size="Large"
        variant="Primary"
        className="w-[400px]"
        disabled={isError}
        options={preparedOptions}
        value={idsValue}
        onChange={(selectedValues: DropdownOption[]) =>
          onFilterChange('positionTitleIds', selectedValues)
        }
        onLoadMore={loadMore}
        hasMore={hasMore}
      />
    </>
  );
};
