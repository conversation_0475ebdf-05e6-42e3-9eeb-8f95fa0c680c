import { useEffect, useMemo, useState } from 'react';

import { UseQueryResult } from '@tanstack/react-query';
import isArray from 'lodash/isArray';

import { PaginationData } from 'shared/services/queryService/generated';

import { FILTERS_PAGE_SIZE } from './constants';

import { EmployeesDataFilterType } from '../EmployeesDataFilter';

import { FilterValue } from '../../../../hooks/utils';

type IsFilterVisible = (key: keyof EmployeesDataFilterType) => boolean;

export const useFilters = <T>(
  fetchFilters: (
    pageSize: number,
    page: number
  ) => UseQueryResult<{ items: T[]; paging: PaginationData }, Error>,
  prepareOptions: (
    options: T[] | undefined,
    isFilterVisible: IsFilterVisible
  ) => { title: string; id: string; name?: string }[] | null,
  isFilterVisible: IsFilterVisible,
  filters: FilterValue
) => {
  const [page, setPage] = useState(1);
  const [options, setOptions] = useState<T[]>([]);

  const { data, isLoading, isError } = fetchFilters(FILTERS_PAGE_SIZE, page);

  useEffect(() => {
    setOptions((currentOptions) => [...currentOptions, ...(data?.items || [])]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const preparedOptions = prepareOptions(
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    options,
    isFilterVisible
  );

  const idsValue = useMemo(() => {
    if (!preparedOptions || !filters || !isArray(filters)) return [];

    return preparedOptions.filter((item) =>
      filters.some((id: string) => id === item.id)
    );
  }, [filters, preparedOptions]);

  return {
    preparedOptions: preparedOptions ?? [],
    idsValue,
    loadMore: () => {
      if (!isLoading) {
        setPage((currentPage) => currentPage + 1);
      }
    },
    isLoading,
    isError,
    hasMore:
      !!data?.paging?.totalResults &&
      data?.paging?.totalResults > FILTERS_PAGE_SIZE * page
  };
};
