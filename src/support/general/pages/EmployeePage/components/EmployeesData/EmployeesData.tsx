import { Stack } from '@/support/shared/components/Stack';

import { EmployeesDataFilter } from './EmployeesDataFilter';
import { EmployeesDataTable } from './EmployeesDataTable';
import { ColumnConfig } from './EmployeesDataTable/columns';
import { getVisibleFiltersFromColumnConfig } from './EmployeesDataTable/utils';
import { useEmployeesDataTableState } from './useEmployeesDataTableState';

import { TableFiltersType } from '../../hooks/utils';

interface EmployeesDataProps {
  columnConfig?: ColumnConfig;
  searchQuery: string;
  filters?: TableFiltersType;
  updateFilters: (newFilters: TableFiltersType) => void;
  rerenderTable: () => void;
}

export const EmployeesData = ({
  columnConfig,
  searchQuery,
  filters = {},
  updateFilters,
  rerenderTable
}: EmployeesDataProps) => {
  const [tableState, setTableState] = useEmployeesDataTableState(filters);

  const visibleFilters = getVisibleFiltersFromColumnConfig(columnConfig);

  const onApplyFilters = (filters: TableFiltersType) => {
    rerenderTable();
    setTableState(
      (prevState: TableFiltersType) =>
        ({ ...prevState, ...filters }) as unknown as TableFiltersType
    );
    updateFilters({ ...filters, pageNumber: '1' });
  };

  return (
    <Stack gap={16}>
      <EmployeesDataFilter
        initialFilters={tableState}
        visibleFilters={visibleFilters}
        onApply={onApplyFilters}
      />

      <EmployeesDataTable
        filters={tableState}
        searchQuery={searchQuery}
        columnConfig={columnConfig}
      />
    </Stack>
  );
};
