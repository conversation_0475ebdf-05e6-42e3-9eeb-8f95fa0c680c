import { useEffect, useState } from 'react';

export const useAutoOpenEmployeeModal = () => {
  const [selectedEmployeeIdFromUrl, setSelectedEmployeeIdFromUrl] = useState<
    string | null
  >(null);
  const [selectedEmployeeEmailFromUrl, setSelectedEmployeeEmailFromUrl] =
    useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const employeeId = urlParams.get('employeeId');
    const employeeEmail = urlParams.get('employeeEmail');
    const newUrl = new URL(window.location.href);

    if (employeeId) {
      setSelectedEmployeeIdFromUrl(employeeId);
      setIsModalOpen(true);
      newUrl.searchParams.delete('employeeId');
    }

    if (employeeEmail) {
      setSelectedEmployeeEmailFromUrl(employeeEmail);
      newUrl.searchParams.delete('employeeEmail');
    }

    if (employeeId || employeeEmail) {
      setIsModalOpen(true);
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, []);

  const openModal = (employeeId: string, employeeEmail: string) => {
    setSelectedEmployeeIdFromUrl(employeeId);
    setSelectedEmployeeEmailFromUrl(employeeEmail);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedEmployeeIdFromUrl(null);
    setSelectedEmployeeEmailFromUrl(null);
  };

  return {
    selectedEmployeeIdFromUrl,
    selectedEmployeeEmailFromUrl,
    isModalOpen,
    openModal,
    closeModal
  };
};
