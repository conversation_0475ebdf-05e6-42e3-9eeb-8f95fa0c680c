import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { useAdminGeneralServiceApi } from '@/api/support/index';
import { logger } from '@/support/shared/utils/logger';

function useGetDepartmentsFiltersRaw(pageSize: number, pageNumber: number) {
  const { employeeViewsApi } = useAdminGeneralServiceApi();

  return useCallback(async () => {
    try {
      const response = await employeeViewsApi.v1DepartmentsAllPost({
        getDepartmentsRequest: {
          pageNumber,
          pageSize
        }
      });

      return response;
    } catch (error) {
      logger.error('Failed to load v1DepartmentsAllPost', error);

      throw error;
    }
  }, [employeeViewsApi, pageSize, pageNumber]);
}

export function useGetDepartmentsFilters(pageSize: number, pageNumber: number) {
  const getDepartmentsFiltersRaw = useGetDepartmentsFiltersRaw(
    pageSize,
    pageNumber
  );

  return useQuery({
    queryKey: ['employeeView', 'departmentsFilters', pageSize, pageNumber],
    queryFn: getDepartmentsFiltersRaw
  });
}
