import { useQuery } from '@tanstack/react-query';
import { v4 as uuidv4 } from 'uuid';

import {
  PaginationData,
  SearchEmployee,
  SearchEmployeePagedListResult
} from 'api/support/generated';

import {
  AdminGeneralService,
  useAdminGeneralServiceApi
} from '@/api/support/index';
import { logger } from '@/support/shared/utils/logger';

export interface SearchEmployeeUniqueId extends SearchEmployee {
  employeeId: string;
}

type DataWithUniqueItems = {
  items: SearchEmployeeUniqueId[];
  paging: PaginationData;
};

const addUUIDField = (data: SearchEmployeePagedListResult) => {
  const itemsWithUUID = data.items.map((item) => ({
    ...item,
    id: uuidv4(),
    employeeId: item.id
  }));

  const output = {
    ...data,
    items: itemsWithUUID
  };
  return output;
};

export function useSearchEmployees(
  params: AdminGeneralService.SearchEmployeeRequest
) {
  const { employeeViewsApi } = useAdminGeneralServiceApi();

  return useQuery({
    queryKey: ['search', 'employeeView', params],
    queryFn: async () => {
      try {
        const data = await employeeViewsApi.v1EmployeesSearchPost({
          searchEmployeeRequest: params
        });

        // api may return items with the same id and email but different name, so need to add id which will be unique
        const dataWithUniqueItems: DataWithUniqueItems = addUUIDField(data);
        return dataWithUniqueItems;
      } catch (error) {
        logger.error('Failed to load v1EmployeesSearchPost', error);

        throw error;
      }
    },
    enabled: params.search ? params.search.length >= 3 : false
  });
}
