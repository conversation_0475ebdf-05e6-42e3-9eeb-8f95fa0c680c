import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { useAdminGeneralServiceApi } from '@/api/support/index';
import { logger } from '@/support/shared/utils/logger';

function useGetPositionTitlesFiltersRaw(pageSize: number, pageNumber: number) {
  const { employeeViewsApi } = useAdminGeneralServiceApi();

  return useCallback(async () => {
    try {
      const response = await employeeViewsApi.v1PositionsAllPost({
        getPositionsRequestV2: {
          pageNumber,
          pageSize
        }
      });

      return response;
    } catch (error) {
      logger.error('Failed to load v1PositionsAllPost', error);

      throw error;
    }
  }, [employeeViewsApi, pageNumber, pageSize]);
}

export function useGetPositionTitlesFilters(
  pageSize: number,
  pageNumber: number
) {
  const getPositionTitlesFiltersRaw = useGetPositionTitlesFiltersRaw(
    pageSize,
    pageNumber
  );

  return useQuery({
    queryKey: ['employeeView', 'positionTitlesFilters', pageSize, pageNumber],
    queryFn: getPositionTitlesFiltersRaw
  });
}
