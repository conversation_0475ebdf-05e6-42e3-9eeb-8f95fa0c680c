import { useEffect, useMemo } from 'react';

import { useIntersectionObserver } from './useIntersectionObserver';

export interface UseSelectInfiniteScrollParams {
  fetchNextPage: () => void;
}

export function useInfiniteScrollSelect({
  fetchNextPage
}: UseSelectInfiniteScrollParams) {
  const { isIntersecting, refCallback } = useIntersectionObserver({
    threshold: 0
  });

  useEffect(() => {
    if (isIntersecting) {
      fetchNextPage();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isIntersecting]);

  return useMemo(
    () => ({
      refCallback: refCallback
    }),
    [refCallback]
  );
}
