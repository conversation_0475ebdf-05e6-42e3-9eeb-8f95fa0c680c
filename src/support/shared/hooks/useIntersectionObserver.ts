import { useCallback, useEffect, useMemo, useState } from 'react';

import { useDeepCompareMemoize } from '@oh/hooks';

export const useIntersectionObserver = (options: IntersectionObserverInit) => {
  const [isIntersecting, setIntersecting] = useState(false);
  const [refElement, setRefElement] = useState<HTMLElement | null>(null);
  const refCallback = useCallback((element: HTMLElement | null) => {
    setRefElement(element);
  }, []);

  const memoizedOptions = useDeepCompareMemoize(options);

  const observer = useMemo(
    () =>
      new IntersectionObserver(([entry]) => {
        setIntersecting(entry.isIntersecting);
      }, memoizedOptions),
    [memoizedOptions]
  );

  useEffect(() => {
    if (refElement) {
      observer.observe(refElement);
    }

    return () => {
      observer.disconnect();
    };
  }, [refElement, observer]);

  return {
    isIntersecting,
    refCallback
  };
};
