export const apiUrl = (baseApiUrl: string, ...segments: string[]) =>
  [baseApiUrl, ...segments].filter(Boolean).join('/');

export const appUrl = (baseAppUrl: string, ...segments: string[]) =>
  [baseAppUrl, ...segments].filter(Boolean).join('/');

export const openEmployeeAtNewTab = (
  employeeId: string,
  employeeEmail: string
) => {
  const currentUrl = new URL(window.location.href);

  if (employeeId) {
    currentUrl.searchParams.set('employeeId', employeeId);
  } else if (employeeEmail) {
    currentUrl.searchParams.set('employeeEmail', employeeEmail);
  }

  if (employeeId || employeeEmail) window.open(currentUrl.toString(), '_blank');
};
