.performance-table {
  &.with-scroll {
    > div:first-of-type {
      height: 521px;
    }

    [role='table'] {
      margin-right: 4px !important;
    }
  }

  [role='table'] {
    margin-right: 0px !important;
    overflow: auto !important;
  }

  .uui-table-header-row {
    border-radius: 8px;
    overflow: hidden;

    .uui-table-scrolling-section {
      border-bottom: none;
    }
  }

  .uui-table-row .uui-table-cell {
    min-height: 67px;
    height: auto;
    padding-top: 11px;
    padding-bottom: 11px;
  }

  .uui-table-header-row {
    --icon-size: 20px;
    --uui-dt-row-bg: var(--uui-dt-header-row-bg);
    --uui-dt-row-bg-hover: var(--uui-dt-header-row-bg-hover);

    .uui-table-fixed-section-right {
      border-width: 0;
    }
  }
  .uui-paginator {
    justify-content: center;

    button.uui-button {
      padding: 0;
      border-radius: var(--paging-size);
    }
  }

  .uui-blocker-container {
    margin-top: 8px;
  }

  .uui-scroll-bars {
    padding-right: 0 !important;
  }

  .uui-track-horizontal {
    z-index: 10;
    bottom: 0px;
  }

  .uui-table-cell {
    --icon-size: 24px;
    .uui-dr_addons-indent {
      width: var(--icon-size) !important;
    }
    .uui-icon {
      svg {
        display: none;
      }
      &.uui-folding-arrow {
        margin-top: 0;
      }

      &::before {
        display: inline-block;
        background-color: var(--text-body);
        content: '';
        mask-image: url('imgs/paging-left.svg');
        mask-size: cover;
        transform: rotate(270deg);
        mask-type: alpha;
        width: 24px;
        height: 24px;
      }

      &[aria-label='Fold']::before {
        mask-image: url('imgs/paging-right.svg');
      }
    }
  }

  .uui-track-horizontal .uui-thumb-horizontal {
    height: 4px;
    margin-top: 2px;

    &.table-with-custom-row {
      .uui-table-header-row {
        .uui-table-scrolling-section {
          .uui-table-header-cell:first-child {
            padding-left: 50px !important;
          }
        }
      }
      .uui-table-row {
        .uui-table-cell:first-child {
          padding-left: 8px !important;
        }
      }
    }
  }
}
