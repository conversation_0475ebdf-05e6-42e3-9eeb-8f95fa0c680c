import { SortDirection, SortingOption } from '@epam/uui-core';

interface OrderBy {
  field: string;
  isDescending?: boolean;
}

export const SORT_ASCENDING = 'asc';
export const SORT_DESCENDING = 'desc';

export function isSortingChanged<TItem>(
  prevArray?: SortingOption<TItem>[],
  nextArray?: SortingOption<TItem>[]
): boolean {
  const prevArrayNormalized = prevArray || [];
  const nextArrayNormalized = nextArray || [];

  if (prevArrayNormalized.length === 0 && nextArrayNormalized.length === 0) {
    return false;
  }

  if (prevArrayNormalized.length === 0 || nextArrayNormalized.length === 0) {
    return true;
  }

  const [prev] = prevArrayNormalized;
  const [next] = nextArrayNormalized;

  if (prev === next) {
    return false;
  }

  if (!prev || !next) {
    return true;
  }

  const prevDirection = prev.direction || SORT_ASCENDING;
  const nextDirection = next.direction || SORT_ASCENDING;

  if (prev.field === next.field && prevDirection === nextDirection) {
    return false;
  }

  return true;
}

export function isSearchChanged(
  prevSearch: string | undefined,
  nextSearch: string | undefined
): boolean {
  return prevSearch !== nextSearch;
}

export function isFiltersChanged<TFilters>(
  prevFilters: TFilters | undefined,
  nextFilters: TFilters | undefined
): boolean {
  return prevFilters !== nextFilters;
}

export function transformSorting(
  sortingArray: SortingOption[] | undefined
): OrderBy | undefined {
  const [sorting] = sortingArray || [];

  if (!sorting) {
    return undefined;
  }

  return {
    field: String(sorting.field),
    isDescending: sorting.direction === SORT_DESCENDING
  };
}

export function orderByToQueryParamValue(orderBy: OrderBy) {
  return `${orderBy.field}-${orderBy.isDescending ? 'desc' : 'asc'}`;
}

export function parseOrderByQueryParam(
  orderByQueryParam: string | null
): SortingOption[] | undefined {
  if (!orderByQueryParam) {
    return undefined;
  }

  const [fieldName, direction] = orderByQueryParam.split('-');

  return [
    {
      field: fieldName,
      direction: direction as SortDirection
    }
  ];
}

export function transformSortingOption(
  orderBy: OrderBy | undefined
): SortingOption[] | undefined {
  if (!orderBy) {
    return undefined;
  }

  return [
    {
      field: orderBy.field,
      direction: orderBy.isDescending ? SORT_DESCENDING : SORT_ASCENDING
    }
  ];
}

const indexProp = Symbol('index');
const reverseIndexProp = Symbol('reverseIndex');

export type NonSortable<TItem> = TItem & {
  [indexProp]: number;
  [reverseIndexProp]: number;
};

export interface NonSortableSource<TItem> {
  items: NonSortable<TItem>[];
  sortBy: (item: NonSortable<TItem>, sorting: SortingOption) => unknown;
}

export function createNonSortableSource<TItem>(
  items: TItem[]
): NonSortableSource<TItem> {
  const length = items.length;

  const nonSortableItems = items.map((item, index) => ({
    ...item,
    [indexProp]: index,
    [reverseIndexProp]: length - index
  }));

  return {
    items: nonSortableItems,
    sortBy: (item: NonSortable<TItem>, sorting: SortingOption) => {
      if (sorting.direction === SORT_DESCENDING) {
        return item[reverseIndexProp];
      }
      return item[indexProp];
    }
  };
}
