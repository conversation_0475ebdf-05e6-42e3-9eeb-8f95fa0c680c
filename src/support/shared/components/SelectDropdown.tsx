import {
  ForwardedRef,
  forwardRef,
  MutableRefObject,
  ReactNode,
  Ref,
  RefObject,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react';

import { flip, offset, useFloating } from '@floating-ui/react';
import clsx from 'clsx';

import { useClickOutside } from '@oh/hooks';

export interface RenderTriggerProps<T extends Element> {
  onClick: () => void;
  isOpen: boolean;
  ref: Ref<T>;
}

export interface RenderContentProps {
  onClick: () => void;
}

export interface DropdownProps<T extends Element> {
  containerClassName?: string;
  triggerClassName?: string;
  menuClassName?: string;
  readonly?: boolean;
  onCloseMenu?: () => void;
  renderTrigger: (props: RenderTriggerProps<T>) => ReactNode;
  renderContent: (props: RenderContentProps) => JSX.Element;
  id?: string;
  isRenderTriggerInput?: boolean;
}

export interface DropdownRef {
  close: () => void;
}

const DropdownRender = <T extends Element>(
  {
    containerClassName,
    menuClassName,
    onCloseMenu,
    renderTrigger,
    renderContent,
    readonly,
    id,
    isRenderTriggerInput = false
  }: DropdownProps<T>,
  ref: ForwardedRef<DropdownRef>
) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      close: () => setIsOpen(false)
    }),
    []
  );

  useEffect(() => {
    if (!isOpen && onCloseMenu) {
      onCloseMenu();
    }
  }, [isOpen, onCloseMenu]);

  const {
    context: {
      refs: { setReference, setFloating }
    },
    strategy,
    update,
    refs: { reference: referenceRef }
  } = useFloating({
    placement: 'bottom',
    middleware: [flip(), offset(4)]
  });

  const triggerRef = referenceRef as MutableRefObject<HTMLElement>;

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  useClickOutside(containerRef, handleClose);
  const handleTriggerClick = useCallback(() => {
    if (!readonly) {
      update();
      // if render trigger element is a text field, no need to close it when clicking inside to type another text
      setIsOpen((previous) => (isRenderTriggerInput ? true : !previous));
    }
  }, [update, readonly, isRenderTriggerInput]);

  return (
    <div
      data-attributes={id && `${id}Container`}
      ref={containerRef}
      className={clsx('relative', containerClassName)}
    >
      {renderTrigger({
        ref: setReference,
        onClick: handleTriggerClick,
        isOpen
      })}
      {isOpen && (
        <div
          ref={setFloating}
          style={{
            position: strategy,
            zIndex: 999,
            width: triggerRef.current?.offsetWidth,
            paddingTop: 8
          }}
          className={menuClassName}
          data-attributes={id && `${id}Dropdown`}
        >
          {renderContent({ onClick: handleTriggerClick })}
        </div>
      )}
    </div>
  );
};

export const Dropdown = forwardRef(DropdownRender) as <T extends Element>(
  props: DropdownProps<T> & {
    ref?: RefObject<DropdownRef>;
  }
) => JSX.Element;
