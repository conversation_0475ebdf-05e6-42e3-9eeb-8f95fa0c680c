import { FC } from 'react';

import { Button } from '@ot/onetalent-ui-kit';

interface Props {
  totalResults: number | undefined;
  onViewAllClick: () => void;
  hasEmployees: boolean;
}

export const TotalResults: FC<Props> = ({
  totalResults,
  onViewAllClick,
  hasEmployees
}) => {
  if (!totalResults || !hasEmployees) {
    return null;
  }

  return (
    <div
      data-attributes="TotalResults"
      className="flex rounded-xl border-t border-gray-200 bg-gray-50 px-12 py-8 text-sm text-gray-600"
    >
      <div className="mx-20 mt-8">Total Results: {totalResults}</div>
      <Button
        variant="Primary"
        className="h-15 w-40 !py-8"
        onClick={onViewAllClick}
      >
        View all
      </Button>
    </div>
  );
};
