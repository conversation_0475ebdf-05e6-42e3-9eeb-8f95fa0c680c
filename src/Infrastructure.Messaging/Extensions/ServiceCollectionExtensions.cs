using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OneTalent.AtrAdminService.Application.Common.Constants;
using OneTalent.Common.Extensions.Configuration;
using OneTalent.ServiceBus.Configuration;
using OneTalent.ServiceBus.Configurators.Queues;
using OneTalent.ServiceBus.Configurators.Topics;
using OneTalent.ServiceBus.Extensions;
using System.Text.Json.Serialization;

namespace OneTalent.AtrAdminService.Infrastructure.Messaging.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection RegisterInfrastructureMessagingServices(
        this IServiceCollection services,
        IConfiguration configuration,
        IHostEnvironment environment)
    {
        services.RegisterConfiguration<MassTransitConfiguration>();

        ICollection<ServiceBusTopicEntityConfigurator> serviceBusTopicEntityConfigurators = [];

        if (!environment.IsEnvironment(EnvironmentNames.IntegrationWriteScenariosEnvironmentName))
        {
            services.ConfigureMassTransit(
                configuration,
                topicEntityConfigurators: serviceBusTopicEntityConfigurators,
                jsonConverter: new JsonStringEnumConverter());
        }
        else
        {
            services.ConfigureMassTransitHarness(
                configuration,
                topicEntityConfigurators: serviceBusTopicEntityConfigurators);
        }

        return services;
    }

    private static IServiceCollection ConfigureMassTransitHarness(
        this IServiceCollection services,
        IConfiguration configuration,
        ICollection<ServiceBusQueueEntityConfigurator>? queueEntityConfigurators = null,
        ICollection<ServiceBusTopicEntityConfigurator>? topicEntityConfigurators = null)
    {
        var massTransitConfiguration =
            configuration.GetSection(MassTransitConfiguration.SectionName).Get<MassTransitConfiguration>()
            ?? throw new ArgumentException($"{nameof(MassTransitConfiguration)} was not found.");

        var serviceBusConfiguration = massTransitConfiguration.ServiceBusConfiguration
            ?? throw new ArgumentException($"{nameof(ServiceBusConfiguration)} was not found.");

        queueEntityConfigurators ??= [];
        topicEntityConfigurators ??= [];

        foreach (var item in queueEntityConfigurators)
        {
            item.SetServiceBusConfiguration(serviceBusConfiguration);
        }

        foreach (var item in topicEntityConfigurators)
        {
            item.SetServiceBusConfiguration(serviceBusConfiguration);
        }

        return services.AddMassTransitTestHarness(busRegistractionConfigurator =>
        {
            foreach (var item in queueEntityConfigurators)
            {
                item.SetServiceBusConfiguration(serviceBusConfiguration);
            }

            foreach (var topicEntityConfigurator in topicEntityConfigurators)
            {
                topicEntityConfigurator.ConfigureBus(busRegistractionConfigurator);
            }
        });
    }
}
