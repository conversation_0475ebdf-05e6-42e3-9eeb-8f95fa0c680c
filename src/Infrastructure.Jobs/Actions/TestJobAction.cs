using Microsoft.Extensions.Logging;
using OneTalent.Hangfire.Extensions.Actions;
using OneTalent.Hangfire.Extensions.Extensions;

namespace OneTalent.AtrAdminService.Infrastructure.Jobs.Actions;

public sealed class TestJobAction(ILogger<TestJobAction> logger) : IHangfireJobAction
{
    public Task ExecuteAsync(ActionContext context, CancellationToken cancellationToken)
    {
        const int totalCount = 0;

        LogProgress(totalCount);

        return Task.CompletedTask;

        void LogProgress(int completed)
        {
            logger.LogProgress(JobActions.TestJobAction, context.CorrelationId, completed);
        }
    }
}
