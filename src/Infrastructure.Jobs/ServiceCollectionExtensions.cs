using Microsoft.Extensions.DependencyInjection;
using OneTalent.AtrAdminService.Infrastructure.Jobs.Actions;
using OneTalent.Hangfire.Extensions;

namespace OneTalent.AtrAdminService.Infrastructure.Jobs;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection RegisterInfrastructureJobsServices(this IServiceCollection services) =>
        services
            .AddHangfire()
            .AddAction<TestJobAction>(JobActions.TestJobAction);
}
