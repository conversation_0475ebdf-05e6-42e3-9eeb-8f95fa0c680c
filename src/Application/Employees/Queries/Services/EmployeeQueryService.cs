using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Logging;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Repositories;
using OneTalent.Common.Extensions.Caching.Services;
using OneTalent.Common.Extensions.CurrentUser;

namespace OneTalent.AtrAdminService.Application.Employees.Queries.Services;

internal sealed class EmployeeQueryService(
    IEmployeeApiRepository employeeApiRepository,
    ICacheService cacheService,
    ICurrentUserProvider currentUserProvider,
    ILogger<EmployeeQueryService> logger) : IEmployeeQueryService
{
    private static readonly HybridCacheEntryOptions _cacheEntryOptions = new()
    {
        Expiration = TimeSpan.FromHours(8),
        LocalCacheExpiration = TimeSpan.FromMinutes(15)
    };

    public async Task<Employee> GetCurrentUserEmployeeAsync(CancellationToken cancellationToken) =>
        (await GetEmployeeByEmailAsync(currentUserProvider.CurrentUser!.Email, false, cancellationToken))
        .AccessForbiddenIfNotEmployee();

    public async Task<IEmployee> GetActiveOrTerminatedEmployeeByIdAsync(
        string employeeId,
        CancellationToken cancellationToken) =>
        await GetEmployeeByIdAsync(employeeId, false, cancellationToken);

    public async Task<IEnumerable<NamedOptions>?> GetFilteredCompaniesAsync(string search, CancellationToken cancellationToken)
    {
        var companies = await GetCompaniesAllAsync(cancellationToken);
        var filteredList = !string.IsNullOrWhiteSpace(search)
            ? companies.Where(x => x.Name.StartsWith(search, StringComparison.OrdinalIgnoreCase))
            : companies;

        return filteredList?.Take(10);
    }

    public async Task<IEnumerable<NamedOptions>?> GetFilteredDirectoratesAsync(string search, CancellationToken cancellationToken)
    {
        var directorates = await GetDirectoratesAllAsync(cancellationToken);
        var filteredList = !string.IsNullOrWhiteSpace(search)
            ? directorates.Where(x => x.Name.StartsWith(search, StringComparison.OrdinalIgnoreCase))
            : directorates;

        return filteredList?.Take(10);
    }

    public async Task<IEnumerable<NamedOptions>?> GetFilteredFunctionsAsync(string search, CancellationToken cancellationToken)
    {
        var functions = await GetFunctionsAllAsync(cancellationToken);
        var filteredList = !string.IsNullOrWhiteSpace(search)
            ? functions.Where(x => x.Name.StartsWith(search, StringComparison.OrdinalIgnoreCase))
            : functions;

        return filteredList?.Take(10);
    }

    public async Task<IEnumerable<NamedOptions>?> GetFilteredDivisionsAsync(string search, CancellationToken cancellationToken)
    {
        var functions = await GetDivisionsAllAsync(cancellationToken);
        var filteredList = !string.IsNullOrWhiteSpace(search)
            ? functions.Where(x => x.Name.StartsWith(search, StringComparison.OrdinalIgnoreCase))
            : functions;

        return filteredList?.Take(10);
    }

    private async Task<ICollection<NamedOptions>> GetDirectoratesAllAsync(CancellationToken cancellationToken)
    {
        var key = $"{nameof(GetDirectoratesAllAsync)}";
        return await cacheService.GetOrCreateAsync(key,
            async ct => await employeeApiRepository.GetDirectoratesAllAsync(ct),
            _cacheEntryOptions,
            cancellationToken: cancellationToken);
    }

    private async Task<ICollection<NamedOptions>> GetFunctionsAllAsync(CancellationToken cancellationToken)
    {
        var key = $"{nameof(GetFunctionsAllAsync)}";
        return await cacheService.GetOrCreateAsync(key,
            async ct => await employeeApiRepository.GetFunctionsAllAsync(ct),
            _cacheEntryOptions,
            cancellationToken: cancellationToken);
    }

    private async Task<ICollection<NamedOptions>> GetCompaniesAllAsync(CancellationToken cancellationToken)
    {
        var key = $"{nameof(GetCompaniesAllAsync)}";
        return await cacheService.GetOrCreateAsync(key,
            async ct => await employeeApiRepository.GetCompaniesAllAsync(ct),
            _cacheEntryOptions,
            cancellationToken: cancellationToken);
    }

    private async Task<ICollection<NamedOptions>> GetDivisionsAllAsync(CancellationToken cancellationToken)
    {
        var key = $"{nameof(GetDivisionsAllAsync)}";
        return await cacheService.GetOrCreateAsync(key,
            async ct => await employeeApiRepository.GetDivisionsAllAsync(ct),
            _cacheEntryOptions,
            cancellationToken: cancellationToken);
    }

    private async Task<IEmployee> GetEmployeeByIdAsync(
        string employeeId,
        bool excludeDeleted,
        CancellationToken cancellationToken)
    {
        var filter = new EmployeeIdFilter(employeeId, excludeDeleted);

        var key = $"{nameof(GetEmployeeByIdAsync)}.{employeeId}";
        IEmployee? employee = await cacheService.GetOrCreateAsync(key,
            async ct => await employeeApiRepository.GetEmployeeByIdAsync(filter, ct) as Employee,
            _cacheEntryOptions,
            cancellationToken: cancellationToken);

        if (employee is not Employee)
        {
            employee = await employeeApiRepository.GetEmployeeByIdAsync(filter, cancellationToken);
            await cacheService.SetAsync(key,
                ct => ValueTask.FromResult(employee as Employee),
                _cacheEntryOptions,
                cancellationToken: cancellationToken);
        }

        if (employee is not Employee)
        {
            logger.EmployeeNotFoundWarning(employeeId);
        }

        return employee;
    }

    private async Task<IEmployee> GetEmployeeByEmailAsync(
        string email,
        bool excludeDeleted,
        CancellationToken cancellationToken)
    {
        var filter = new EmployeeEmailFilter(email, excludeDeleted);

        var key = $"{nameof(GetEmployeeByEmailAsync)}.{email}";
        IEmployee? employee = await cacheService.GetOrCreateAsync(key,
            async ct => await employeeApiRepository.GetEmployeeByEmailAsync(filter, ct) as Employee,
            _cacheEntryOptions,
            cancellationToken: cancellationToken);

        if (employee is not Employee)
        {
            employee = await employeeApiRepository.GetEmployeeByEmailAsync(filter, cancellationToken);
            await cacheService.SetAsync(key,
                ct => ValueTask.FromResult(employee as Employee),
                _cacheEntryOptions,
                cancellationToken: cancellationToken);
        }

        if (employee is not Employee)
        {
            logger.EmployeeNotFoundWarning(email);
        }

        return employee;
    }
}
