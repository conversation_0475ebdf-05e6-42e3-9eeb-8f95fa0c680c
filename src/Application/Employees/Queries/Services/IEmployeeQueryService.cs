using OneTalent.AtrAdminService.Application.Employees.Queries.Models;

namespace OneTalent.AtrAdminService.Application.Employees.Queries.Services;

public interface IEmployeeQueryService
{
    Task<IEmployee> GetActiveOrTerminatedEmployeeByIdAsync(string employeeId, CancellationToken cancellationToken);
    Task<Employee> GetCurrentUserEmployeeAsync(CancellationToken cancellationToken);
    Task<IEnumerable<NamedOptions>?> GetFilteredCompaniesAsync(string search, CancellationToken cancellationToken);
    Task<IEnumerable<NamedOptions>?> GetFilteredDirectoratesAsync(string search, CancellationToken cancellationToken);
    Task<IEnumerable<NamedOptions>?> GetFilteredFunctionsAsync(string search, CancellationToken cancellationToken);
    Task<IEnumerable<NamedOptions>?> GetFilteredDivisionsAsync(string search, CancellationToken cancellationToken);
}
