using OneTalent.AtrAdminService.Application.Employees.Queries.Models;

namespace OneTalent.AtrAdminService.Application.Employees.Queries.Repositories;

public interface IEmployeeApiRepository
{
    Task<IEmployee> GetEmployeeByEmailAsync(EmployeeEmailFilter filter, CancellationToken cancellationToken);

    Task<IEmployee> GetEmployeeByIdAsync(EmployeeIdFilter filter, CancellationToken cancellationToken);
    
    Task<ICollection<NamedOptions>> GetDirectoratesAllAsync(CancellationToken cancellationToken);
    
    Task<ICollection<NamedOptions>> GetFunctionsAllAsync(CancellationToken cancellationToken);

    Task<ICollection<NamedOptions>> GetDivisionsAllAsync(CancellationToken cancellationToken);

    Task<ICollection<NamedOptions>> GetCompaniesAllAsync(CancellationToken cancellationToken);
}
