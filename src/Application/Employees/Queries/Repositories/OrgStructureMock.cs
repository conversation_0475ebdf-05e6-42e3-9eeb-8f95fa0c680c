using OneTalent.AtrAdminService.Application.Employees.Queries.Models;

namespace OneTalent.AtrAdminService.Application.Employees.Queries.Repositories;

public static class OrgStructureMock
{
    public static readonly List<NamedOptions> DirectorateList =
    [
        new(Id: "100001", Name: "Directorate Test 1"),
        new(Id: "100002", Name: "Directorate Test 2"),
        new(Id: "100003", Name: "Directorate Test 3"),
        new(Id: "100004", Name: "Directorate Test 4"),
        new(Id: "100005", Name: "Directorate Test 5"),
        new(Id: "100006", Name: "Directorate Test 6")
    ];

    public static readonly List<NamedOptions> DivisionList =
    [
        new(Id: "110001", Name: "Division Test 1"),
        new(Id: "110002", Name: "Division Test 2"),
        new(Id: "110003", Name: "Division Test 3"),
        new(Id: "110004", Name: "Division Test 4"),
        new(Id: "110005", Name: "Division Test 5"),
        new(Id: "110006", Name: "Division Test 6")
    ];

    public static readonly List<NamedOptions> FunctionList =
    [
        new(Id: "111001", Name: "Function Test 1"),
        new(Id: "111002", Name: "Function Test 2"),
        new(Id: "111003", Name: "Function Test 3"),
        new(Id: "111004", Name: "Function Test 4"),
        new(Id: "111005", Name: "Function Test 5"),
        new(Id: "111006", Name: "Function Test 6")
    ];
}

