using OneTalent.AtrAdminService.Application.Employees.Queries.Models;

namespace OneTalent.AtrAdminService.Application.Employees.Queries.Repositories;

public static class EmployeeMock
{
    public static readonly List<Employee> EmployeeList =
    [
        new(EmployeeId: "1001", CompanyCode: "0001", CompanyName: "ADNOC Test", FullNameEnglish: "Test1 Test1", Email: "<EMAIL>", JobTitle: "QA Specialist", PositionName: "Specialist, Quality Assurance", LineManagerId: "1005", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "1002", CompanyCode: "0001", CompanyName: "ADNOC Test", FullNameEnglish: "Test2 Test2", Email: "<EMAIL>", JobTitle: "QA Specialist", PositionName: "Specialist, Quality Assurance", LineManagerId: "1005", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "1003", CompanyCode: "0001", CompanyName: "ADNOC Test", FullNameEnglish: "Test3 Test3", Email: "<EMAIL>", JobTitle: "QA Specialist", PositionName: "Specialist, Quality Assurance", LineManagerId: "1005", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "1004", CompanyCode: "0001", CompanyName: "ADNOC Test", FullNameEnglish: "Test4 Test4", Email: "<EMAIL>", JobTitle: "QA Specialist", PositionName: "Specialist, Quality Assurance", LineManagerId: "1005", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "1005", CompanyCode: "0001", CompanyName: "ADNOC Test", FullNameEnglish: "Test5 Test5", Email: "<EMAIL>", JobTitle: "QA Specialist", PositionName: "Specialist, Quality Assurance", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),

        new(EmployeeId: "99905151", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "John Doe", Email: "<EMAIL>", JobTitle: "Marine Standards Specialist", PositionName: "Specialist, Marine Standards & Regulations", LineManagerId: "99905156", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905152", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Jane Smith", Email: "<EMAIL>", JobTitle: "Safety Compliance Manager", PositionName: "Manager, Safety & Compliance", LineManagerId: "99905156", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905153", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Michael Johnson", Email: "<EMAIL>", JobTitle: "Senior Engineer", PositionName: "Senior Engineer, Technical Operations", LineManagerId: "99905156", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905154", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Frank Miller", Email: "<EMAIL>", JobTitle: "Market Research Analyst", PositionName: "Analyst, Market Research & Strategy", LineManagerId: "99905153", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905155", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "David Brown", Email: "<EMAIL>", JobTitle: "Business Solutions Consultant", PositionName: "Consultant, Business Solutions", LineManagerId: "99905181", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905156", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Roman Sharabura", Email: "<EMAIL>", JobTitle: "Business Solutions Consultant", PositionName: "Manager, Business Solutions", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),

        new(EmployeeId: "99905157", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Sophia Lee", Email: "<EMAIL>", JobTitle: "Project Management Assistant", PositionName: "Assistant, Project Management", LineManagerId: "99905162", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905158", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Daniel Harris", Email: "<EMAIL>", JobTitle: "IT Services Director", PositionName: "Director, IT Services", LineManagerId: "99905162", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905159", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Isabella Clark", Email: "<EMAIL>", JobTitle: "HR Coordinator", PositionName: "Coordinator, Human Resources", LineManagerId: "99905162", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905160", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "James Wilson", Email: "<EMAIL>", JobTitle: "HR Coordinator", PositionName: "Lead, Supply Chain Management", LineManagerId: "99905162", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905161", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Olivia Martinez", Email: "<EMAIL>", JobTitle: "Digital Marketing Specialist", PositionName: "Specialist, Digital Marketing", LineManagerId: "99905162", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905162", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Andrii Serputko", Email: "<EMAIL>", JobTitle: "Digital Marketing Specialist", PositionName: "Manager, Digital Marketing", LineManagerId: "99905155", B2BManagerId: null, PositionId: null, IsManager : true),

        new(EmployeeId: "99905163", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Liam Scott", Email: "<EMAIL>", JobTitle: "Supply Chain Analyst", PositionName: "Analyst, Supply Chain", LineManagerId: "99905167", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905164", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Emma Turner", Email: "<EMAIL>", JobTitle: "Finance Manager", PositionName: "Manager, Finance Operations", LineManagerId: "99905167", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905165", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Noah Walker", Email: "<EMAIL>", JobTitle: "Operations Specialist", PositionName: "Specialist, Operations", LineManagerId: "99905167", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905166", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ava Young", Email: "<EMAIL>", JobTitle: "Business Analyst", PositionName: "Analyst, Business Strategy", LineManagerId: "99905153", B2BManagerId: "99905181", PositionId: null, IsManager : false),
        new(EmployeeId: "99905167", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ivanna Seneiko", Email: "<EMAIL>", JobTitle: "Quality Assurance Specialist", PositionName: "Specialist, Quality Assurance", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),

        new(EmployeeId: "99905168", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Mia King", Email: "<EMAIL>", JobTitle: "Legal Advisor", PositionName: "Advisor, Legal Affairs", LineManagerId: "99905172", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905169", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "William Lopez", Email: "<EMAIL>", JobTitle: "Marketing Coordinator", PositionName: "Coordinator, Marketing", LineManagerId: "99905172", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905170", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Sophia Green", Email: "<EMAIL>", JobTitle: "Sales Associate", PositionName: "Associate, Sales Operations", LineManagerId: "99905172", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905171", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Benjamin Perez", Email: "<EMAIL>", JobTitle: "Customer Support Manager", PositionName: "Manager, Customer Support", LineManagerId: "99905172", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905172", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Mykola Basiuk", Email: "<EMAIL>", JobTitle: "Technical Writer", PositionName: "Writer, Technical Documentation", LineManagerId: null, B2BManagerId: null, PositionId: "11000020000098", IsManager : true),

        new(EmployeeId: "99905173", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Elijah Harris", Email: "<EMAIL>", JobTitle: "Software Developer", PositionName: "Developer, Software Engineering", LineManagerId: "99905177", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905174", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Charlotte Clark", Email: "<EMAIL>", JobTitle: "Business Development Associate", PositionName: "Associate, Business Development", LineManagerId: "99905177", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905175", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Oliver Martin", Email: "<EMAIL>", JobTitle: "Network Administrator", PositionName: "Administrator, IT Network", LineManagerId: "99905177", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905176", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Henry Lewis", Email: "<EMAIL>", JobTitle: "HR Specialist", PositionName: "Specialist, Human Resources", LineManagerId: "99905177", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905177", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Aleh Tarazevich", Email: "<EMAIL>", JobTitle: "Research Scientist", PositionName: "Scientist, Research & Development", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),

        new(EmployeeId: "99905178", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Hugh Wilson", Email: "<EMAIL>", JobTitle: "Marketing Manager", PositionName: "Manager, Marketing", LineManagerId: "99905182", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905179", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ivy Martinez", Email: "<EMAIL>", JobTitle: "Data Analyst", PositionName: "Analyst, Data Science", LineManagerId: "99905182", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905180", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Amelia Johnson", Email: "<EMAIL>", JobTitle: "Financial Analyst", PositionName: "Analyst, Financial Planning", LineManagerId: "99905182", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905181", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ethan Brown", Email: "<EMAIL>", JobTitle: "IT Support Specialist", PositionName: "Specialist, IT Support", LineManagerId: "99905182", B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99905182", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Alston Johnson", Email: "<EMAIL>", JobTitle: "Product Manager", PositionName: "Manager, Product Development", LineManagerId: "99905201", B2BManagerId: null, PositionId: null, IsManager : true),

        new(EmployeeId: "99905201", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Samantha Ray", Email: "<EMAIL>", JobTitle: "Director of Product Development", PositionName: "Director, Product Development", null, B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906000", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Vitalii Kozmin", Email: "<EMAIL>", JobTitle: "Business Partner", PositionName: "Business Partner, Software Engineering", LineManagerId: "99905162", B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906001", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Alston Johnson", Email: "<EMAIL>", JobTitle: "Product Manager", PositionName: "Manager, Product Development", LineManagerId: "99906000", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906002", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Lisa White", Email: "<EMAIL>", JobTitle: "Project Manager", PositionName: "Manager, Project Execution", LineManagerId: "99906000", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906003", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Robert Garcia", Email: "<EMAIL>", JobTitle: "Operations Coordinator", PositionName: "Coordinator, Operations & Logistics", LineManagerId: "99906000", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906004", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Lucy Green", Email: "<EMAIL>", JobTitle: "Quality Assurance Specialist", PositionName: "Specialist, Quality Assurance", LineManagerId: "99906000", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906005", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ethan Martinez", Email: "<EMAIL>", JobTitle: "Technical Support Engineer", PositionName: "Engineer, Technical Support", LineManagerId: "99906000", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906006", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Sarah Lewis", Email: "<EMAIL>", JobTitle: "Data Analyst", PositionName: "Analyst, Data Integration", LineManagerId: "99906000", B2BManagerId: null, PositionId: null, IsManager : false),

        // QA Team
        new(EmployeeId: "99906007", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Aliaksandr Kandratsenia", Email: "<EMAIL>", JobTitle: "Software Engineer", PositionName: "Engineer, Software Development", LineManagerId: "99906012", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906008", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Hleb Shulhin", Email: "<EMAIL>", JobTitle: "Marketing Specialist", PositionName: "Specialist, Marketing Communications", LineManagerId: "99906012", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906009", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Oleksii Filatov", Email: "<EMAIL>", JobTitle: "Logistics Manager", PositionName: "Manager, Supply Chain Logistics", LineManagerId: "99906012", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906010", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Yuliya Drabysheuskaya", Email: "<EMAIL>", JobTitle: "Human Resources Specialist", PositionName: "Specialist, HR Operations", LineManagerId: "99906012", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906011", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Denys Nahai", Email: "<EMAIL>", JobTitle: "Financial Analyst", PositionName: "Analyst, Financial Planning", LineManagerId: "99906012", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906012", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Krystsina Panamarenka", Email: "<EMAIL>", JobTitle: "Compliance Officer", PositionName: "Officer, Regulatory Compliance", LineManagerId: "99906012", B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906013", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Iryna Pazniak1", Email: "<EMAIL>", JobTitle: "Legal Advisorr", PositionName: "Advisor, Corporate Legal Affairs", LineManagerId: "99906012", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906014", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Kristina Kotava", Email: "<EMAIL>", JobTitle: "Senior Business Analyst", PositionName: "Senior Analyst, Business Operations", LineManagerId: "99906012", B2BManagerId: null, PositionId: null, IsManager : true),

        new(EmployeeId: "99906015", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Konstantin Rebrov", Email: "<EMAIL>", JobTitle: "Digital Marketing Specialist", PositionName: "Manager, Digital Marketing", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906016", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Roman Hrechuk", Email: "<EMAIL>", JobTitle: "HR Specialist", PositionName: "HR Assistant", LineManagerId: "99905162", B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906017", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Mohammed Rashid", Email: "<EMAIL>", JobTitle: "Financial Analyst", PositionName: "Finance Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906018", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Layla Hussein", Email: "<EMAIL>", JobTitle: "Project Coordinator", PositionName: "Project Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906019", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Omar Khaled", Email: "<EMAIL>", JobTitle: "IT Support Engineer", PositionName: "IT Specialist", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906020", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Fatima Abdullah", Email: "<EMAIL>", JobTitle: "Data Analyst", PositionName: "Data Scientist", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906021", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ahmed Salim", Email: "<EMAIL>", JobTitle: "Logistics Coordinator", PositionName: "Supply Chain Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906022", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Noura Saeed", Email: "<EMAIL>", JobTitle: "Marketing Specialist", PositionName: "Marketing Manager", LineManagerId: "99906014", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906023", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Hassan Mahmoud", Email: "<EMAIL>", JobTitle: "Legal Advisor", PositionName: "Legal Counsel", LineManagerId: "99906014", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906024", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Amina Ahmed", Email: "<EMAIL>", JobTitle: "Procurement Officer", PositionName: "Procurement Manager", LineManagerId: "99906014", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906025", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Tariq Al-Tamimi", Email: "<EMAIL>", JobTitle: "Risk Analyst", PositionName: "Risk Manager", LineManagerId: "99906014", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906026", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Sara Fawaz", Email: "<EMAIL>", JobTitle: "HR Coordinator", PositionName: "HR Manager", LineManagerId: "99906014", B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906027", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Uladzimir Dzmitryieu1", Email: "<EMAIL>", JobTitle: "IT Consultant", PositionName: "IT Manager", LineManagerId: "99906014", B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906028", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Khalid Mustafa", Email: "<EMAIL>", JobTitle: "Operations Supervisor", PositionName: "Operations Manager", LineManagerId: "99906027", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906029", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Rania Jamal", Email: "<EMAIL>", JobTitle: "Quality Control Specialist", PositionName: "Quality Assurance Manager", LineManagerId: "99906027", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906030", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Zaid Ali", Email: "<EMAIL>", JobTitle: "Compliance Officer", PositionName: "Compliance Manager", LineManagerId: "99906027", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906031", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Salma Khalifa", Email: "<EMAIL>", JobTitle: "Financial Auditor", PositionName: "Audit Manager", LineManagerId: "99906027", B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906032", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ahmed Nasser", Email: "<EMAIL>", JobTitle: "Data Analyst", PositionName: "Data Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906033", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Layla Saeed", Email: "<EMAIL>", JobTitle: "Project Coordinator", PositionName: "Project Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906034", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Omar Tariq", Email: "<EMAIL>", JobTitle: "HR Specialist", PositionName: "HR Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906035", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Sara Malik", Email: "<EMAIL>", JobTitle: "Marketing Executive", PositionName: "Marketing Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906036", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Hassan Youssef", Email: "<EMAIL>", JobTitle: "Procurement Officer", PositionName: "Procurement Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906037", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Mona Abdullah", Email: "<EMAIL>", JobTitle: "IT Support Specialist", PositionName: "IT Support Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906038", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Faisal Hasan", Email: "<EMAIL>", JobTitle: "Logistics Coordinator", PositionName: "Logistics Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906039", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Nour Ahmed", Email: "<EMAIL>", JobTitle: "Research Assistant", PositionName: "Research Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906040", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Yasmin Khalid", Email: "<EMAIL>", JobTitle: "Training Coordinator", PositionName: "Training Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906041", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Mohammed Salim", Email: "<EMAIL>", JobTitle: "Legal Advisor", PositionName: "Legal Manager", LineManagerId: "99906016", B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906072", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Artur Minin", Email: "<EMAIL>", JobTitle: "Data Analyst", PositionName: "Data Manager", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906073", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ruby Cross", Email: "<EMAIL>", JobTitle: "Trainee Engineer", PositionName: "Engineering Trainee", LineManagerId: "99906072", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906074", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Daniel Fry", Email: "<EMAIL>", JobTitle: "Operations Specialist", PositionName: "Operations Specialist", LineManagerId: "99906072", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906075", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Georgia Ward", Email: "<EMAIL>", JobTitle: "Marketing Intern", PositionName: "Marketing Intern", LineManagerId: "99906072", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906076", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Noah King", Email: "<EMAIL>", JobTitle: "Financial Assistant", PositionName: "Financial Assistant", LineManagerId: "99906072", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906077", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Oliver Bates", Email: "<EMAIL>", JobTitle: "Supply Chain Coordinator", PositionName: "Supply Chain Coordinator", LineManagerId: "99906072", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906078", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Piper Wells", Email: "<EMAIL>", JobTitle: "IT Support Officer", PositionName: "IT Support Officer", LineManagerId: "99906072", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906079", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Quentin Fields", Email: "<EMAIL>", JobTitle: "HR Representative", PositionName: "HR Representative", LineManagerId: "99906072", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906080", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Rosie Moore", Email: "<EMAIL>", JobTitle: "Production Analyst", PositionName: "Production Analyst", LineManagerId: "99906072", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906081", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Sam Novak", Email: "<EMAIL>", JobTitle: "Creative Designer", PositionName: "Creative Designer", LineManagerId: "99906072", B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906092", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Aliaksandr Limasau", Email: "<EMAIL>", JobTitle: "Senior Director", PositionName: "Department Head", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906093", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Lucas White", Email: "<EMAIL>", JobTitle: "Junior Engineer", PositionName: "Engineering Intern", LineManagerId: "99906092", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906094", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Bella Martin", Email: "<EMAIL>", JobTitle: "Operations Assistant", PositionName: "Operations Assistant", LineManagerId: "99906092", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906095", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Caleb Fisher", Email: "<EMAIL>", JobTitle: "Project Assistant", PositionName: "Project Assistant", LineManagerId: "99906092", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906096", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Dylan Hawkins", Email: "<EMAIL>", JobTitle: "Quality Analyst", PositionName: "QA Tester", LineManagerId: "99906092", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906097", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ella Morgan", Email: "<EMAIL>", JobTitle: "Administrative Specialist", PositionName: "Admin Specialist", LineManagerId: "99906092", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906098", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Freya Davis", Email: "<EMAIL>", JobTitle: "Recruitment Coordinator", PositionName: "Recruitment Coordinator", LineManagerId: "99906092", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906099", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "George Brewer", Email: "<EMAIL>", JobTitle: "Finance Trainee", PositionName: "Finance Trainee", LineManagerId: "99906092", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906100", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Hannah Brooks", Email: "<EMAIL>", JobTitle: "Sales Representative", PositionName: "Sales Rep", LineManagerId: "99906092", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906101", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Ivy Carter", Email: "<EMAIL>", JobTitle: "IT Support Intern", PositionName: "IT Support Intern", LineManagerId: "99906092", B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906102", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Giorgi Chkhaidze", Email: "<EMAIL>", JobTitle: "Senior Director", PositionName: "Department Head", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906103", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "James Ford", Email: "<EMAIL>", JobTitle: "Junior Engineer", PositionName: "Engineering Intern", LineManagerId: "99906102", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906104", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Kelly Addison", Email: "<EMAIL>", JobTitle: "Operations Assistant", PositionName: "Operations Assistant", LineManagerId: "99906102", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906105", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Luke Grant", Email: "<EMAIL>", JobTitle: "Project Assistant", PositionName: "Project Assistant", LineManagerId: "99906102", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906106", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Megan Rhodes", Email: "<EMAIL>", JobTitle: "Quality Analyst", PositionName: "QA Tester", LineManagerId: "99906102", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906107", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Nate Young", Email: "<EMAIL>", JobTitle: "Administrative Specialist", PositionName: "Admin Specialist", LineManagerId: "99906102", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906108", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Olivia Sparks", Email: "<EMAIL>", JobTitle: "Recruitment Coordinator", PositionName: "Recruitment Coordinator", LineManagerId: "99906102", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906109", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Patrick Dunn", Email: "<EMAIL>", JobTitle: "Finance Trainee", PositionName: "Finance Trainee", LineManagerId: "99906102", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906110", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Renee Harvey", Email: "<EMAIL>", JobTitle: "Sales Representative", PositionName: "Sales Rep", LineManagerId: "99906102", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906111", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Sylvia Benson", Email: "<EMAIL>", JobTitle: "IT Support Intern", PositionName: "IT Support Intern", LineManagerId: "99906102", B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906112", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Artur Movsisyan", Email: "<EMAIL>", JobTitle: "Senior Director", PositionName: "Department Head", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906113", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Taylor Cross", Email: "<EMAIL>", JobTitle: "Junior Engineer", PositionName: "Engineering Intern", LineManagerId: "99906112", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906114", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Uma Reed", Email: "<EMAIL>", JobTitle: "Operations Assistant", PositionName: "Operations Assistant", LineManagerId: "99906112", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906115", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Victor Perez", Email: "<EMAIL>", JobTitle: "Project Assistant", PositionName: "Project Assistant", LineManagerId: "99906112", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906116", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Wendy Scott", Email: "<EMAIL>", JobTitle: "Quality Analyst", PositionName: "QA Tester", LineManagerId: "99906112", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906117", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Xander Bishop", Email: "<EMAIL>", JobTitle: "Administrative Specialist", PositionName: "Admin Specialist", LineManagerId: "99906112", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906118", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Yvonne Wade", Email: "<EMAIL>", JobTitle: "Recruitment Coordinator", PositionName: "Recruitment Coordinator", LineManagerId: "99906112", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906119", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Zane Perkins", Email: "<EMAIL>", JobTitle: "Finance Trainee", PositionName: "Finance Trainee", LineManagerId: "99906112", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906120", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Amber Nash", Email: "<EMAIL>", JobTitle: "Sales Representative", PositionName: "Sales Rep", LineManagerId: "99906112", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906121", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Becky Simmons", Email: "<EMAIL>", JobTitle: "IT Support Intern", PositionName: "IT Support Intern", LineManagerId: "99906112", B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "99906122", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Illia Maroz", Email: "<EMAIL>", JobTitle: "Senior Director", PositionName: "Department Head", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906123", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Carla Jenkins", Email: "<EMAIL>", JobTitle: "Junior Engineer", PositionName: "Engineering Intern", LineManagerId: "99906122", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906124", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Derek Fox", Email: "<EMAIL>", JobTitle: "Operations Assistant", PositionName: "Operations Assistant", LineManagerId: "99906122", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906125", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Erin Hayes", Email: "<EMAIL>", JobTitle: "Project Assistant", PositionName: "Project Assistant", LineManagerId: "99906122", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906126", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Finn Oliver", Email: "<EMAIL>", JobTitle: "Quality Analyst", PositionName: "QA Tester", LineManagerId: "99906122", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906127", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Gloria Wood", Email: "<EMAIL>", JobTitle: "Administrative Specialist", PositionName: "Admin Specialist", LineManagerId: "99906122", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906128", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Hazel Clark", Email: "<EMAIL>", JobTitle: "Recruitment Coordinator", PositionName: "Recruitment Coordinator", LineManagerId: "99906122", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906129", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Isaac Mendez", Email: "<EMAIL>", JobTitle: "Finance Trainee", PositionName: "Finance Trainee", LineManagerId: "99906122", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906130", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Julia Nelson", Email: "<EMAIL>", JobTitle: "Sales Representative", PositionName: "Sales Rep", LineManagerId: "99906122", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906131", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Kevin Ramirez", Email: "<EMAIL>", JobTitle: "IT Support Intern", PositionName: "IT Support Intern", LineManagerId: "99906122", B2BManagerId: null, PositionId: null, IsManager : false),

        new(EmployeeId: "********", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Aleksandr Asmeev", Email: "<EMAIL>", JobTitle: "Senior Director", PositionName: "Department Head", LineManagerId: null, B2BManagerId: null, PositionId: null, IsManager : true),
        new(EmployeeId: "99906133", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Lana Cooper", Email: "<EMAIL>", JobTitle: "Junior Engineer", PositionName: "Engineering Intern", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "********", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Mason Reed", Email: "<EMAIL>", JobTitle: "Operations Assistant", PositionName: "Operations Assistant", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "********", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Nora Banks", Email: "<EMAIL>", JobTitle: "Project Assistant", PositionName: "Project Assistant", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "********", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Oscar Chapman", Email: "<EMAIL>", JobTitle: "Quality Analyst", PositionName: "QA Tester", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "********", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Paula Davies", Email: "<EMAIL>", JobTitle: "Administrative Specialist", PositionName: "Admin Specialist", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "********", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Quinn Ellis", Email: "<EMAIL>", JobTitle: "Recruitment Coordinator", PositionName: "Recruitment Coordinator", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906139", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Reed Foster", Email: "<EMAIL>", JobTitle: "Finance Trainee", PositionName: "Finance Trainee", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906140", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Sienna Gordon", Email: "<EMAIL>", JobTitle: "Sales Representative", PositionName: "Sales Rep", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906141", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Troy Hunter", Email: "<EMAIL>", JobTitle: "IT Support Intern", PositionName: "IT Support Intern", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906142", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Dzmitry Siarko", Email: "<EMAIL>", JobTitle: "Software Developer", PositionName: "Developer, Software Engineering", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906143", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Artur Mnyshenko", Email: "<EMAIL>", JobTitle: "Software Developer", PositionName: "Developer, Software Engineering", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99906058", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Aleh Siarheichyk", Email: "<EMAIL>", JobTitle: "Software Developer", PositionName: "Developer, Software Engineering", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
        new(EmployeeId: "99905184", CompanyCode: "1100", CompanyName: "ADNOC HQ", FullNameEnglish: "Aliaksandr Navitski", Email: "<EMAIL>", JobTitle: "Senior Software Developer", PositionName: "Developer", LineManagerId: "********", B2BManagerId: null, PositionId: null, IsManager : false),
    ];
}
