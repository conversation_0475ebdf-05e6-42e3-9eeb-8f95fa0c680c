using OneTalent.AtrAdminService.Application.Employees.Queries.Models;

namespace OneTalent.AtrAdminService.Application.Employees.Queries.Repositories;

public class EmployeeApiMockRepository : IEmployeeApiRepository
{
    public Task<ICollection<NamedOptions>> GetCompaniesAllAsync(CancellationToken cancellationToken)
    {
        ICollection<NamedOptions> companies = EmployeeMock.EmployeeList.Select(
            employee => new NamedOptions(employee.CompanyCode, employee.CompanyName)).DistinctBy(c => c.Id).ToList();

        return Task.FromResult(companies);
    }

    public Task<ICollection<NamedOptions>> GetDirectoratesAllAsync(CancellationToken cancellationToken)
    {
        ICollection<NamedOptions> directorates = OrgStructureMock.DirectorateList.ToList();

        return Task.FromResult(directorates);
    }

    public Task<ICollection<NamedOptions>> GetDivisionsAllAsync(CancellationToken cancellationToken)
    {
        ICollection<NamedOptions> divisions = OrgStructureMock.DivisionList.ToList();

        return Task.FromResult(divisions);
    }

    public Task<IEmployee> GetEmployeeByEmailAsync(EmployeeEmailFilter filter, CancellationToken cancellationToken)
    {
        IEmployee? employee = EmployeeMock.EmployeeList.FirstOrDefault(
            employee => filter.Email.Equals(employee.Email, StringComparison.OrdinalIgnoreCase));

        return Task.FromResult(employee ?? new NotEmployee($"Email = {filter.Email}."));
    }

    public Task<IEmployee> GetEmployeeByIdAsync(
        EmployeeIdFilter filter,
        CancellationToken cancellationToken)
    {
        IEmployee? employee = EmployeeMock.EmployeeList.FirstOrDefault(
            employee => filter.EmployeeId.Equals(employee.EmployeeId));

        return Task.FromResult(employee ?? new NotEmployee($"Id = {filter.EmployeeId}."));
    }

    public Task<ICollection<NamedOptions>> GetFunctionsAllAsync(CancellationToken cancellationToken)
    {
        ICollection<NamedOptions> functions = OrgStructureMock.FunctionList.ToList();

        return Task.FromResult(functions);
    }
}
