using OneTalent.Common.Extensions.Exceptions;

namespace OneTalent.AtrAdminService.Application.Employees.Queries.Models;

public record Employee(
    string EmployeeId,
    string CompanyCode,
    string? CompanyName,
    string FullNameEnglish,
    string Email,
    string JobTitle,
    string? PositionName,
    string? LineManagerId,
    string? B2BManagerId,
    string? PositionId,
    bool? IsManager) : EmployeeBase(EmployeeId);

public record EmployeeBase(
    string EmployeeId) : IEmployee;

public record NotEmployee(
    string Details) : IEmployee;

public interface IEmployee
{
    public Employee AccessForbiddenIfNotEmployee() =>
        this switch
        {
            Employee e => e,
            NotEmployee ne => throw new AccessForbiddenException($"Not employee. {ne.Details}"),
            _ => throw new AccessForbiddenException("Not employee.")
        };
}
