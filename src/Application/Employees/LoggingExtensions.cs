using Microsoft.Extensions.Logging;
using OneTalent.AtrAdminService.Application.Common.Constants;

namespace OneTalent.AtrAdminService.Application.Employees;

public static partial class LoggingExtensions
{
    [LoggerMessage(
        EventId = Events.EmployeeNotFound,
        EventName = nameof(Events.EmployeeNotFound),
        Level = LogLevel.Warning,
        Message = "Employee {Employee} not found")]
    public static partial void EmployeeNotFoundWarning(this ILogger logger, string employee);

    [LoggerMessage(
        EventId = Events.EmployeeIsNull,
        EventName = nameof(Events.EmployeeIsNull),
        Level = LogLevel.Error,
        Message = "Employee is null. {Details}")]
    public static partial void EmployeeIsNull(this ILogger logger, string details);

    [LoggerMessage(
        EventId = Events.UnexpectedError,
        EventName = nameof(Events.UnexpectedError),
        Level = LogLevel.Warning,
        Message = "Unexpected error.")]
    public static partial void UnexpectedExceptionWarning(this ILogger logger, Exception exception);
}
