namespace OneTalent.AtrAdminService.Application.Common.Constants;

public class EnvironmentNames
{
    public const string DevEnvironmentName = "DEV";
    public const string StagingEnvironmentName = "STG";
    public const string UserAcceptanceTestingEnvironmentName = "UAT";
    public const string UserAcceptanceTesting2EnvironmentName = "UAT2";
    public const string PreviewEnvironmentName = "PREVIEW";
    public const string ProductionEnvironmentName = "PRD";
    public const string IntegrationTestEnvironmentName = "Test";
    public const string IntegrationReadScenariosEnvironmentName = "IntegrationTest-ReadScenarios";
    public const string IntegrationWriteScenariosEnvironmentName = "IntegrationTest-WriteScenarios";
}
