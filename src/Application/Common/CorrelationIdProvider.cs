using System.Diagnostics;

namespace OneTalent.AtrAdminService.Application.Common;

internal class CorrelationIdProvider : ICorrelationIdProvider
{
    private Guid? _correlationId;

    public Guid GetCorrelationId()
    {
        if (_correlationId.HasValue)
        {
            return _correlationId.Value;
        }

        if (Activity.Current is null)
        {
            _correlationId = Guid.CreateVersion7();
            return _correlationId.Value;
        }

        var traceId = Activity.Current.TraceId;
        Span<byte> traceIdBytes = stackalloc byte[16];
        traceId.CopyTo(traceIdBytes);
        _correlationId = new Guid(traceIdBytes);

        return _correlationId.Value;
    }

    public void SetCorrelationId(Guid value) =>
        _correlationId = _correlationId is null
            ? value
            : throw new InvalidOperationException($"{nameof(_correlationId)} was not null.");
}
