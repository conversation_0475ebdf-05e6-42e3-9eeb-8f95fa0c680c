using OneTalent.Common.Extensions.Paging;

namespace OneTalent.AtrAdminService.Application.Common.Extensions;

public static class CollectionExtension
{
    public static PagedListResult<TDomainModel> ToPaginatedResult<TDomainModel>(this ICollection<TDomainModel> items,
        OffsetPage offsetPage)
    {
        var result = items
            .Skip((offsetPage.PageNumber - 1) * offsetPage.PageSize)
            .Take(offsetPage.PageSize);

        return new PagedListResult<TDomainModel>(
            offsetPage,
            result,
            items.Count
        );
    }
}

