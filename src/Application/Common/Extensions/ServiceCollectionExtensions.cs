using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OneTalent.AtrAdminService.Application.Access.Queries.Services;
using OneTalent.AtrAdminService.Application.Actions.Services;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Services;
using OneTalent.AtrAdminService.Application.PerformanceForms.Services;

namespace OneTalent.AtrAdminService.Application.Common.Extensions;

public static class ServiceCollectionExtensions
{
    public static void RegisterApplicationServices(this IServiceCollection services, IConfiguration configuration) =>
        services
            .AddScoped<ICorrelationIdProvider, CorrelationIdProvider>()
            .AddScoped<IAccessControlService, AccessControlService>()
            .AddScoped<IAtrAdminQueryService, AtrAdminQueryService>()
            .AddScoped<IAtrAdminCommandService, AtrAdminCommandService>()
            .AddScoped<IActionCommandService, ActionCommandService>()
            .AddScoped<IActionQueryService, ActionQueryService>()
            .AddScoped<IEmployeeQueryService, EmployeeQueryService>()
            .AddScoped<IPerformanceFormQueryService, PerformanceFormQueryService>()
            .AddScoped<IPerformanceFormHistoryQueryService, PerformanceFormHistoryQueryService>();

    public static TService GetServiceImplementation<TService, TMockImplementation, TImplementation>(
        IServiceProvider provider,
        IConfiguration configuration)
        where TMockImplementation : TService
        where TImplementation : TService
    {
        var environment = configuration.GetValue<string>(Constants.Constants.AspNetCoreEnvironmentVariableName);

        return environment == Environments.Development
            ? ActivatorUtilities.CreateInstance<TMockImplementation>(provider: provider)!
            : ActivatorUtilities.CreateInstance<TImplementation>(provider: provider)!;
    }
}
