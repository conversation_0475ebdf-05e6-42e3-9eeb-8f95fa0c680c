using OneTalent.AtrAdminService.Application.Common.Models;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace OneTalent.AtrAdminService.Application.Common.Extensions;

public static class LookupOptionExtension
{
    public static LookupOption<TEnum>[] ToLookupOptions<TEnum>() where TEnum : Enum
    {
        return Enum.GetValues(typeof(TEnum))
            .OfType<TEnum>()
            .Select(e => new LookupOption<TEnum>(
                    e,
                    e.GetDisplayName())
                ).ToArray();
    }

    public static LookupOption<T> ToLookupOption<T>(this T enumValue) where T : Enum
    {
        var memberInfo = enumValue.GetType()
            .GetMember(enumValue.ToString())
            .FirstOrDefault() ?? throw new ArgumentException($"Property '{enumValue}' not found.");

        var displayAttribute = memberInfo.GetCustomAttribute<DisplayAttribute>();

        var obsoleteAttribute = memberInfo.GetCustomAttribute<ObsoleteAttribute>();
        if (obsoleteAttribute != null)
        {
            throw new InvalidOperationException($"Member '{memberInfo.Name}' of '{typeof(T).Name}' Enum type is obsolete.");
        }

        return new LookupOption<T>(
                enumValue,
                displayAttribute?.Name ?? enumValue.ToString()
            );
    }

    public static string GetDisplayName<T>(this T enumValue) where T : Enum
    {
        return enumValue.GetType()
                   .GetMember(enumValue.ToString())
                   .FirstOrDefault()
                   ?.GetCustomAttribute<DisplayAttribute>()
                   ?.Name
               ?? enumValue.ToString();
    }
}
