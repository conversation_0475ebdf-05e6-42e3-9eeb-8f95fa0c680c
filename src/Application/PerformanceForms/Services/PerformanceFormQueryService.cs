using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Repositories;
using OneTalent.Common.Extensions.Paging;

namespace OneTalent.AtrAdminService.Application.PerformanceForms.Services;

public class PerformanceFormQueryService(
    IAtrApiRepository atrApiRepository)
    : IPerformanceFormQueryService
{
    public async Task<PerformanceFormStatus[]> GetPerformanceFormStatusesAsync(CancellationToken cancellationToken) =>
        await atrApiRepository.GetAssessmentStatusesAsync(cancellationToken);

    public async Task<PagedListResult<AssessmentForm>> SearchPerformanceFormsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken) =>
        await atrApiRepository.SearchAssessmentsAsync(employeeId, searchQuery, cancellationToken);
}
