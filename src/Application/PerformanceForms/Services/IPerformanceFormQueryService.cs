using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.Common.Extensions.Paging;

namespace OneTalent.AtrAdminService.Application.PerformanceForms.Services;

public interface IPerformanceFormQueryService
{
    Task<PerformanceFormStatus[]> GetPerformanceFormStatusesAsync(CancellationToken cancellationToken);

    Task<PagedListResult<AssessmentForm>> SearchPerformanceFormsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken);

}
