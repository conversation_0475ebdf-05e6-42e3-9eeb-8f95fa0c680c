using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.Common.Extensions.ItemId;

namespace OneTalent.AtrAdminService.Application.PerformanceForms.Repositories;
public static class MockPerformanceFormHistory
{
    public static readonly List<PerformanceFormHistoryRecord> PerformanceFormHistory = new()
    {
        new(new ItemId(0), new DateTimeOffset(2025, 05, 12, 10, 30, 00, TimeSpan.FromHours(0)), "SelfSubmit",null, "AutoUser","Manager Assessment","NotStarted","Self Assessment","NotStarted","Action Reason", "Developer", "ADNOC","AGT","AGT","AGT","2025 Performance Cycle","John Doe","<PERSON> Johnson","<PERSON> Miller","John Doe",0,string.Empty,"Empty",0,null, null, null,"EOY0022735", string.Empty),
        new(new ItemId(0), new DateTimeOffset(2025, 06, 10, 10, 30, 00, TimeSpan.FromHours(0)), "SelfSubmit",null, "AutoUser","Manager Assessment","NotStarted","Manager Assessment","InProgress","Action Reason", "Developer", "ADNOC","AGT","AGT","AGT","2025 Performance Cycle","<PERSON> Doe","Michael Johnson","Frank Miller","John Doe",0,string.Empty,"Empty",0,null, null, null,"EOY0022735", string.Empty),
        new(new ItemId(0), new DateTimeOffset(2025, 07, 23, 10, 30, 00, TimeSpan.FromHours(0)), "SelfSubmit",null, "AutoUser","Manager Assessment","InProgress","Manager Assessment","Completed","Action Reason", "Developer", "ADNOC","AGT","AGT","AGT","2025 Performance Cycle","John Doe","Michael Johnson","Frank Miller","John Doe",0,string.Empty,"Empty",0,null, null, null,"EOY0022735", string.Empty)
    };
}
