using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Services;
using OneTalent.AtrAdminService.Application.AtrAdmin.Models.Commands;
using OneTalent.AtrAdminService.Application.Common;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.AtrAdminService.Application.Files.Repositories;
using OneTalent.Common.Extensions.Exceptions;
using OneTalent.Common.Extensions.ItemId;
using ScheduleActionCommand = OneTalent.AtrAdminService.Application.AtrAdmin.Models.Commands.ScheduleActionCommand;

namespace OneTalent.AtrAdminService.Application.AtrAdmin.Services;

public class AtrAdminCommandService(
    IEmployeeQueryService employeeQueryService,
    IActionCommandService actionCommandService,
    IFileStorageRepository fileStorageRepository,
    IUploadedFileInfoWriteRepository uploadedFileInfoRepository,
    IUnitOfWork unitOfWork
) : IAtrAdminCommandService
{
    public async Task UploadMyFileAsync(UploadFileCommand command, CancellationToken cancellationToken)
    {
        var employee = await employeeQueryService.GetCurrentUserEmployeeAsync(cancellationToken);

        var fileInfo = new AtrAdminFileInfo(
                employee.EmployeeId,
                command.FileActionType,
                command.FileName,
                FileType.Input,
                StorageArea.Staging,
                command.FileStream.Length
            );

        await uploadedFileInfoRepository.UpsertAsync(fileInfo, cancellationToken);

        await fileStorageRepository.UploadAsync(fileInfo.FilePath, command.FileStream, cancellationToken);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteMyUploadedFileAsync(FileActionType fileActionType, CancellationToken cancellationToken)
    {
        var employee = await employeeQueryService.GetCurrentUserEmployeeAsync(cancellationToken);

        await uploadedFileInfoRepository.DeleteAsync(employee.EmployeeId, fileActionType, cancellationToken);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task<ItemId> ScheduleActionAsync(ScheduleActionCommand command, CancellationToken cancellationToken)
    {
        var employee = await employeeQueryService.GetCurrentUserEmployeeAsync(cancellationToken);

        var fileInfo = await uploadedFileInfoRepository.GetAsync(
                           employee.EmployeeId,
                           command.ActionType.ToFileActionType(),
                           cancellationToken)
                       ?? throw new ItemNotFoundException($"No uploaded file found for action type {command.ActionType}.");

        return await actionCommandService.ScheduleActionAsync(
                new Actions.Models.Commands.ScheduleActionCommand(
                        command.ActionType,
                        command.IsValidation,
                        employee,
                        fileInfo
                    ),
                cancellationToken
            );
    }
}
