using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.AtrAdmin.Models.Commands;
using OneTalent.Common.Extensions.ItemId;
using ScheduleActionCommand = OneTalent.AtrAdminService.Application.AtrAdmin.Models.Commands.ScheduleActionCommand;

namespace OneTalent.AtrAdminService.Application.AtrAdmin.Services;

public interface IAtrAdminCommandService
{
    Task UploadMyFileAsync(UploadFileCommand command, CancellationToken cancellationToken);

    Task DeleteMyUploadedFileAsync(FileActionType fileActionType, CancellationToken cancellationToken);

    Task<ItemId> ScheduleActionAsync(ScheduleActionCommand command, CancellationToken cancellationToken);
}
