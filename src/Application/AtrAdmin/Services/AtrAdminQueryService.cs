using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Models.Queries;
using OneTalent.AtrAdminService.Application.Actions.Services;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.AtrAdminService.Application.Files.Repositories;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Services;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Services;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.Common.Extensions.Paging;
using System.Threading;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Application.AtrAdmin.Services;

public class AtrAdminQueryService(
    IEmployeeQueryService employeeQueryService,
    IActionQueryService actionQueryService,
    IFileStorageRepository fileStorageRepository,
    IUploadedFileInfoReadRepository uploadedFileInfoRepository,
    IPerformanceFormQueryService performanceFormQueryService,
    IPerformanceFormHistoryQueryService historyQueryService
) : IAtrAdminQueryService
{
    public Task<PagedListResult<Action>> SearchMyActionsAsync(SearchActionsQuery query, CancellationToken cancellationToken) =>
        actionQueryService.SearchActionsAsync(query, cancellationToken);

    public Task<ActionStatus?> GetMyActionStatusAsync(ItemId actionId, CancellationToken cancellationToken) =>
        actionQueryService.GetActionStatusAsync(actionId, cancellationToken);

    public async Task<AtrAdminFileInfo?> GetMyUploadedFileInfoAsync(FileActionType fileActionType,
        CancellationToken cancellationToken)
    {
        var employee = await employeeQueryService.GetCurrentUserEmployeeAsync(cancellationToken);

        return await uploadedFileInfoRepository.GetAsync(employee.EmployeeId, fileActionType, cancellationToken);
    }

    public async Task<AtrAdminFile?> DownloadMyUploadedFileAsync(FileActionType fileActionType,
        CancellationToken cancellationToken)
    {
        var employee = await employeeQueryService.GetCurrentUserEmployeeAsync(cancellationToken);

        var fileInfo = await uploadedFileInfoRepository.GetAsync(employee.EmployeeId, fileActionType, cancellationToken);

        if (fileInfo is null)
        {
            return null;
        }

        var stream = await fileStorageRepository.DownloadAsync(fileInfo.FilePath, cancellationToken);

        return new AtrAdminFile(fileInfo.Name, stream);
    }

    public async Task<PerformanceFormStatus[]> GetPerformanceFormStatusesAsync(CancellationToken cancellationToken) =>
        await performanceFormQueryService.GetPerformanceFormStatusesAsync(cancellationToken);

    public async Task<PagedListResult<AssessmentForm>> SearchPerformanceFormsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken) 
    {
        var atrForms = await performanceFormQueryService.SearchPerformanceFormsAsync(employeeId, searchQuery, cancellationToken);
        foreach (var item in atrForms.Items)
        {
            item.EmployeeFullName = await LoadEmployeeNameAsync(item.EmployeeId, cancellationToken);
            item.AssessmentLineManagerName = await LoadEmployeeNameAsync(item.AssessmentLineManagerId, cancellationToken);
            item.AssessmentB2BManagerName = item.AssessmentB2BManagerId is not null ? await LoadEmployeeNameAsync(item.AssessmentB2BManagerId, cancellationToken) : null;
            item.DottedLineManagerName = item.DottedLineManagerId is not null ? await LoadEmployeeNameAsync(item.DottedLineManagerId, cancellationToken) : null;
        }

        // load directorate, company, function, etc
        return atrForms;

    }

    private async Task<string> LoadEmployeeNameAsync(string eployeeId, CancellationToken cancellationToken) =>
          (await employeeQueryService.GetActiveOrTerminatedEmployeeByIdAsync(eployeeId, cancellationToken) as Employee)?.FullNameEnglish;

    public async Task<PagedListResult<PerformanceFormHistoryRecord>> GetPerformanceFormHistoryAsync(PerformanceFormHistoryQuery query, CancellationToken cancellationToken) =>
        await historyQueryService.GetPerformanceFormHistoryAsync(query, cancellationToken);
}
