using Microsoft.Extensions.Logging;
using OneTalent.AtrAdminService.Application.Common.Constants;

namespace OneTalent.AtrAdminService.Application.Access;

internal static partial class LoggerExtensions
{
    [LoggerMessage(
        EventId = Events.AccessDeniedId,
        EventName = nameof(Events.AccessDeniedId),
        Message = "Access to the module is not allowed for EmployeeId: '{EmployeeId}'. {Details}.",
        Level = LogLevel.Warning
    )]
    public static partial void AccessDenied(this ILogger logger, string employeeId, string details);
}
