using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Logging;
using OneTalent.AtrAdminService.Application.Common.Constants;
using OneTalent.Common.Extensions.Caching.Services;
using OneTalent.Common.Extensions.CurrentUser;

namespace OneTalent.AtrAdminService.Application.Access.Queries.Services;

internal sealed class AccessControlService(
    ICacheService cacheService,
    ICurrentUserProvider currentUserProvider,
    IAtrAdminContextService atrAdminContextService,
    ILogger<AccessControlService> logger
) : IAccessControlService
{
    private static readonly HybridCacheEntryOptions _cacheEntryOptions = new()
    {
        Expiration = TimeSpan.FromHours(8),
        LocalCacheExpiration = TimeSpan.FromMinutes(15)
    };

    public Task<bool> EnsureAllAsync(IEnumerable<Rights> acl, CancellationToken cancellationToken) => Task.FromResult(true);
}
