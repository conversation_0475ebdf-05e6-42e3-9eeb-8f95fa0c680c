using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Files.Models;

namespace OneTalent.AtrAdminService.Application.Files.Repositories;

public interface IUploadedFileInfoWriteRepository : IUploadedFileInfoReadRepository
{
    Task UpsertAsync(AtrAdminFileInfo fileInfo, CancellationToken cancellationToken);

    Task DeleteAsync(string atrAdminEmployeeId, FileActionType fileActionType, CancellationToken cancellationToken);
}
