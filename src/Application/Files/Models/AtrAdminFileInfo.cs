using OneTalent.AtrAdminService.Application.Actions.Models;

namespace OneTalent.AtrAdminService.Application.Files.Models;

public enum FileType { Input, Output }

public enum StorageArea { Working, Staging }

public record AtrAdminFileInfo(
    string AtrAdminEmployeeId,
    FileActionType FileActionType,
    string Name,
    FileType FileType,
    StorageArea StorageArea,
    long LengthInBytes
)
{
    public Guid FileId { get; init; } = Guid.NewGuid();

    public string FilePath => $"{ToPath(StorageArea)}/{AtrAdminEmployeeId}/{ToPath(FileActionType)}_{ToPath(FileType)}_{FileId}.csv";

    private static string ToPath(FileActionType fileActionType) => fileActionType switch
    {
        FileActionType.AtrFormsLaunch => "atr-forms-launch",
        FileActionType.BulkTransition => "bulk-transition",
        FileActionType.BulkReAssign => "bulk-reassign",
        _ => throw new ArgumentOutOfRangeException(nameof(fileActionType),
            $"Not expected actionType value: {fileActionType}")
    };

    private static string ToPath(FileType fileType) => fileType switch
    {
        FileType.Input => "input",
        FileType.Output => "output",
        _ => throw new ArgumentOutOfRangeException(nameof(fileType), $"Not expected fileType value: {fileType}"),
    };

    private static string ToPath(StorageArea fileType) => fileType switch
    {
        StorageArea.Staging => "staging",
        StorageArea.Working => "working",
        _ => throw new ArgumentOutOfRangeException(nameof(fileType), fileType, null)
    };
}
