using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Models.Queries;
using OneTalent.AtrAdminService.Application.Actions.Repositories;
using OneTalent.AtrAdminService.Application.Actions.Repositories.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.Common.Extensions.Paging;
using OneTalent.Common.Extensions.Sorting;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Application.Actions.Services;

internal class ActionQueryService(
    IActionReadRepository actionReadRepository,
    IEmployeeQueryService employeeQueryService
) : IActionQueryService
{
    public async Task<PagedListResult<Action>> SearchActionsAsync(SearchActionsQuery query, CancellationToken cancellationToken)
    {
        var actions = await actionReadRepository.SearchAllActionsAsync(
            new ActionsQueryOptions(
                new ActionsFilter(),
                new OrderBy(nameof(Action.Id), SortDirection.Descending),
                query.OffsetPage),
            cancellationToken);

        foreach (var action in actions.Items)
        {
            action.Initiator = await LoadEmployeeAsync(action.Initiator, cancellationToken);
        }

        return actions;
    }

    // TODO: Move to EmployeeQueryService?
    private async Task<IEmployee> LoadEmployeeAsync(IEmployee employee, CancellationToken cancellationToken)
    {
        if (employee is EmployeeBase employeeBase)
        {
            var emp = await employeeQueryService.GetActiveOrTerminatedEmployeeByIdAsync(
                employeeBase.EmployeeId,
                cancellationToken);

            if (emp is not Employee)
            {
                return employeeBase;
            }

            return emp;
        }

        return employee;
    }

    public Task<Action?> GetCurrentActionToProcessAsync(CancellationToken cancellationToken) =>
        actionReadRepository.GetCurrentActionToProcessAsync(cancellationToken);

    public Task<ActionStatus?> GetActionStatusAsync(ItemId actionId, CancellationToken cancellationToken) =>
        actionReadRepository.GetActionStatusAsync(actionId, cancellationToken);
}
