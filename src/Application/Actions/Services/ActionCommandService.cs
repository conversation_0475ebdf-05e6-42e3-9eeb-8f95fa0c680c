using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Models.Commands;
using OneTalent.AtrAdminService.Application.Actions.Repositories;
using OneTalent.AtrAdminService.Application.Common;
using OneTalent.Common.Extensions.ItemId;
using Serilog;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Application.Actions.Services;

internal class ActionCommandService(
    IActionWriteRepository actionWriteRepository,
    IUnitOfWork unitOfWork
) : IActionCommandService
{
    public async Task<ItemId> ScheduleActionAsync(ScheduleActionCommand command, CancellationToken cancellationToken)
    {
        return await actionWriteRepository.AddAndSaveAsync(
            new Action(
                    Id: 0,
                    IsValidation: true,
                    Status: ActionStatus.Pending,
                    Type: command.ActionType,
                    Initiator: command.Initiator,
                    StartDate: DateTimeOffset.UtcNow,
                    EndDate: null,
                    InputFileId: command.AtrAdminFileInfo.FileId,
                    InputFileName: command.AtrAdminFileInfo.Name,
                    OutputFileId: null,
                    OutputFileName: null
                ),
            cancellationToken);
    }

    public async Task ProcessActionAsync(ProcessActionCommandBase command, CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            await Task.Delay(TimeSpan.FromSeconds(10), cancellationToken);
            Log.Warning(command.ActionId.ToString());
            if (true)
            {
                break;
            }
        }
    }

    public async Task CancelActionAsync(ItemId actionId, CancellationToken cancellationToken)
    {
        await actionWriteRepository.UpdateAsync(actionId, ActionStatus.Cancelling, cancellationToken);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}
