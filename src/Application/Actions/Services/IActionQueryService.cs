using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Models.Queries;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.Common.Extensions.Paging;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Application.Actions.Services;

public interface IActionQueryService
{
    Task<PagedListResult<Action>> SearchActionsAsync(SearchActionsQuery query, CancellationToken cancellationToken);

    Task<Action?> GetCurrentActionToProcessAsync(CancellationToken cancellationToken);

    Task<ActionStatus?> GetActionStatusAsync(ItemId actionId, CancellationToken cancellationToken);
}
