using OneTalent.AtrAdminService.Application.Actions.Models.Commands;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.Common.Extensions.ItemId;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Application.Actions.Services;

public interface IActionCommandService
{
    Task<ItemId> ScheduleActionAsync(ScheduleActionCommand command, CancellationToken cancellationToken);
    Task ProcessActionAsync(ProcessActionCommandBase command, CancellationToken cancellationToken);
    Task CancelActionAsync(ItemId actionId, CancellationToken cancellationToken);
}

// TODO: Make abstract?
public record ProcessActionCommandBase(
    long ActionId,
    IEmployee Initiator
    // ItemId InputFileId
)
{
    public static ProcessActionCommandBase Create(Action action) => new(
        action.Id,
        action.Initiator
        // action.InputFileId
    );
};
