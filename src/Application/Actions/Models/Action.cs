using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.Common.Extensions.ItemId;

namespace OneTalent.AtrAdminService.Application.Actions.Models;

public record Action(
    ItemId Id,
    bool IsValidation,
    ActionStatus Status,
    ActionType Type,
    IEmployee Initiator,
    DateTimeOffset StartDate,
    DateTimeOffset? EndDate,
    Guid? InputFileId,
    string? InputFileName,
    Guid? OutputFileId,
    string? OutputFileName
)
{
    public IEmployee Initiator { get; set; } = Initiator;

    // TODO: Update the values
    public AtrAdminFileInfo GetInputFileInfo() => new(
        (Initiator as EmployeeBase ?? throw new InvalidOperationException("Initiator was not EmployeeBase.")).EmployeeId,
        Type.ToFileActionType(),
        InputFileName ?? throw new InvalidOperationException("InputFileName was null."),
        FileType.Input,
        StorageArea.Staging,
        0
    )
    { FileId = InputFileId ?? throw new InvalidOperationException("FileId was null.") };
}
