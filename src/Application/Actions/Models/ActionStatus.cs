using System.ComponentModel.DataAnnotations;

namespace OneTalent.AtrAdminService.Application.Actions.Models;

public enum ActionStatus
{
    [Display(Name = "In Progress")]
    InProgress,

    [Display(Name = "Pending")]
    Pending,

    [Display(Name = "Completed")]
    Completed,

    [Display(Name = "Canceled")]
    Canceled,

    [Display(Name = "Cancelling")]
    Cancelling,

    [Display(Name = "Failed")]
    Failed
}
