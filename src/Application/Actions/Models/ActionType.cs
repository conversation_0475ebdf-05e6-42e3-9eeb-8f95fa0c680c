using System.ComponentModel.DataAnnotations;

namespace OneTalent.AtrAdminService.Application.Actions.Models;

public enum ActionType
{
    [Display(Name = "ATR Forms Launch")]
    AtrFormsLaunch,

    [Display(Name = "Bulk Transition")]
    BulkTransition,

    [Display(Name = "Bulk Re-Assign")]
    BulkReAssign,

    [Display(Name = "Bulk Cancel")]
    BulkCancel,

    [Display(Name = "Export ATR Forms to Excel")]
    ExportAtrFormsToExcel
}

public enum FileActionType
{
    AtrFormsLaunch,
    BulkTransition,
    BulkReAssign
}

public static class Mappers
{
    public static FileActionType ToFileActionType(this ActionType source) => source switch
    {
        ActionType.AtrFormsLaunch => FileActionType.AtrFormsLaunch,
        ActionType.BulkTransition => FileActionType.BulkTransition,
        ActionType.BulkReAssign => FileActionType.BulkReAssign,
        _ => throw new InvalidOperationException($"Unsupported ActionType value: {source}")
    };
}
