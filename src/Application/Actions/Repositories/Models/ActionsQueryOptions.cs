using OneTalent.AtrAdminService.Application.Common.Models;
using OneTalent.Common.Extensions.Paging;
using OneTalent.Common.Extensions.Sorting;

namespace OneTalent.AtrAdminService.Application.Actions.Repositories.Models;

public record ActionsQueryOptions : PagedQueryOptions<ActionsFilter>
{
    public ActionsQueryOptions(ActionsFilter filter, OrderBy? orderBy, OffsetPage offsetPage) : base(filter, orderBy, offsetPage)
    {
    }
}
