using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Repositories.Models;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.Common.Extensions.Paging;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Application.Actions.Repositories;

public interface IActionReadRepository
{
    Task<PagedListResult<Action>> SearchAllActionsAsync(ActionsQueryOptions query, CancellationToken cancellationToken);

    // TODO: Remove?
    Task<Action?> GetCurrentActionToProcessAsync(CancellationToken cancellationToken);

    Task<ActionStatus?> GetActionStatusAsync(ItemId actionId, CancellationToken cancellationToken);
}
