using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.Common.Extensions.ItemId;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Application.Actions.Repositories;

public interface IActionWriteRepository
{
    Task<ItemId> AddAndSaveAsync(Action atrAdminAction, CancellationToken cancellationToken);
    Task UpdateAsync(ItemId actionId, ActionStatus status, CancellationToken cancellationToken);
}
