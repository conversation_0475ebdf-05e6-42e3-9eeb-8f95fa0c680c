using OneTalent.Common.Extensions.ItemId;

namespace OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;

public record PerformanceFormHistoryRecord(
    ItemId PerformanceFormId,
    DateTimeOffset ActionDateTime,
    string ActionType,
    string? ActionOwnerId,
    string ActionOwnerName,
    string MajorStatusTo,
    string MinorStatusTo,
    string? MajorStatusFrom,
    string? MinorStatusFrom,
    string? ActionReason,
    string? ActionOwnerJobRole,
    string GroupCompany,
    string? Directorate,
    string? Function,
    string? Division,
    string PerformanceCycleName,
    string? AssessmentLineManagerName,
    string? AssessmentB2BManagerName,
    string? DottedLineManagerName,
    string CurrentLineManager,
    double ManagerRating,
    string ManagerRatingName,
    string ManagerNineBoxPotential,
    int NineBoxPotentialRating,
    string? NormalizedRating,
    string? NormalizedRatingName,
    string? NormalizedNineBoxPotential,
    string? WorkflowId,
    string? WfAssigneeName);
