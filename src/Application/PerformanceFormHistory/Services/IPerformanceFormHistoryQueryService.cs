using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.Common.Extensions.Paging;
namespace OneTalent.AtrAdminService.Application.PerformanceFormHistory.Services;

public interface IPerformanceFormHistoryQueryService
{
    Task<PagedListResult<PerformanceFormHistoryRecord>> GetPerformanceFormHistoryAsync(
        PerformanceFormHistoryQuery query, 
        CancellationToken cancellationToken);
}
