using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Repositories;
using OneTalent.Common.Extensions.Paging;

namespace OneTalent.AtrAdminService.Application.PerformanceFormHistory.Services;

public class PerformanceFormHistoryQueryService(
    IAtrApiRepository atrApiRepository)
    : IPerformanceFormHistoryQueryService
{
    public async Task<PagedListResult<PerformanceFormHistoryRecord>> GetPerformanceFormHistoryAsync(
        PerformanceFormHistoryQuery query, 
        CancellationToken cancellationToken) =>
        await atrApiRepository.GetPerformanceFormHistoryAsync(query, cancellationToken);
}
