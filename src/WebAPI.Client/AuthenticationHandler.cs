using Microsoft.Extensions.Options;
using OneTalent.Azure.Extensions.CredentialsProvider;
using System.Net.Http.Headers;

namespace OneTalent.AtrAdminService.WebAPI.Client;

public class AuthenticationHandler(
    IOptions<AtrAdminServiceConfig> options,
    IAzureCredentialsProvider azureCredentialsProvider) : DelegatingHandler
{
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await azureCredentialsProvider.GetServiceTokenCredentials().ForServiceAsync(options.Value.Scope, cancellationToken));
        request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/problem+json"));
        return await base.SendAsync(request, cancellationToken);
    }
}
