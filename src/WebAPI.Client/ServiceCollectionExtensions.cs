using Microsoft.Extensions.DependencyInjection;
using OneTalent.Common.Extensions.Configuration;

namespace OneTalent.AtrAdminService.WebAPI.Client;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection RegisterDataSyncClientServices(this IServiceCollection services, HttpClient? testClient = null)
    {
        services.RegisterConfiguration<AtrAdminServiceConfig>()
            .AddTransient<AuthenticationHandler>();
            // .AddHttpClient<IAtrAdminServiceClient, AtrAdminServiceClient>((sp, client) =>
            // {
            //     var dataSync = sp.GetRequiredService<IOptions<AtrAdminServiceConfig>>().Value;
            //     client.BaseAddress = new Uri(dataSync.BaseUrl);
            // }).AddHttpMessageHandler<AuthenticationHandler>()
            // .AddSafeHeaderPropagation();

        return services;
    }
}
