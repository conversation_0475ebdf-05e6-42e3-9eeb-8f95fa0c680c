{"runtime": "Net90", "defaultVariables": null, "documentGenerator": {"fromDocument": {"json": "{}", "url": "", "output": null, "newLineBehavior": "Auto"}}, "codeGenerators": {"openApiToCSharpClient": {"clientBaseClass": null, "configurationClass": null, "generateClientClasses": true, "suppressClientClassesOutput": false, "generateClientInterfaces": true, "suppressClientInterfacesOutput": false, "clientBaseInterface": null, "injectHttpClient": true, "disposeHttpClient": true, "protectedMethods": [], "generateExceptionClasses": true, "exceptionClass": "AtrAdminApiException", "wrapDtoExceptions": false, "useHttpClientCreationMethod": false, "httpClientType": "System.Net.Http.HttpClient", "useHttpRequestMessageCreationMethod": false, "useBaseUrl": false, "generateBaseUrlProperty": false, "generateSyncMethods": false, "generatePrepareRequestAndProcessResponseAsAsyncMethods": false, "exposeJsonSerializerSettings": false, "clientClassAccessModifier": "public", "typeAccessModifier": "public", "propertySetterAccessModifier": "", "generateNativeRecords": true, "generateContractsOutput": false, "contractsNamespace": "", "contractsOutputFilePath": null, "parameterDateTimeFormat": "s", "parameterDateFormat": "yyyy-MM-dd", "generateUpdateJsonSerializerSettingsMethod": true, "useRequestAndResponseSerializationSettings": false, "serializeTypeInformation": false, "queryNullValue": "", "className": "AtrAdminServiceClient", "operationGenerationMode": "SingleClientFromPathSegments", "additionalNamespaceUsages": [], "additionalContractNamespaceUsages": [], "generateOptionalParameters": false, "generateJsonMethods": false, "enforceFlagEnums": false, "parameterArrayType": "System.Collections.Generic.IEnumerable", "parameterDictionaryType": "System.Collections.Generic.IDictionary", "responseArrayType": "System.Collections.Generic.IReadOnlyCollection", "responseDictionaryType": "System.Collections.Generic.IDictionary", "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "namespace": "OneTalent.AtrAdminService.WebAPI.Client.AtrAdminService", "requiredPropertiesMustBeDefined": true, "dateType": "System.DateTimeOffset", "jsonConverters": null, "anyType": "object", "dateTimeType": "System.DateTimeOffset", "timeType": "System.TimeSpan", "timeSpanType": "System.TimeSpan", "arrayType": "System.Collections.Generic.ICollection", "arrayInstanceType": "System.Collections.ObjectModel.Collection", "dictionaryType": "System.Collections.Generic.IDictionary", "dictionaryInstanceType": "System.Collections.Generic.Dictionary", "arrayBaseType": "System.Collections.ObjectModel.Collection", "dictionaryBaseType": "System.Collections.Generic.Dictionary", "classStyle": "Record", "jsonLibrary": "SystemTextJson", "generateDefaultValues": true, "generateDataAnnotations": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateImmutableArrayProperties": false, "generateImmutableDictionaryProperties": false, "jsonSerializerSettingsTransformationMethod": null, "inlineNamedArrays": false, "inlineNamedDictionaries": false, "inlineNamedTuples": true, "inlineNamedAny": false, "generateDtoTypes": true, "generateOptionalPropertiesAsNullable": true, "generateNullableReferenceTypes": true, "templateDirectory": null, "serviceHost": null, "serviceSchemes": null, "output": "AtrAdminService/AtrAdminServiceClient.Generated.cs", "newLineBehavior": "Auto"}}}