using OneTalent.AtrAdminService.Application.Files.Repositories;
using OneTalent.BlobStorage;

namespace OneTalent.AtrAdminService.Infrastructure.Storage;

internal class FileStorageRepository(IFileStorageService fileStorageService) : IFileStorageRepository
{
    public Task UploadAsync(string filePath, Stream fileStream, CancellationToken cancellationToken) =>
        fileStorageService.UploadFileAsync(filePath, fileStream, cancellationToken);

    public Task<Stream> DownloadAsync(string filePath, CancellationToken cancellationToken) =>
        fileStorageService.DownloadFileAsync(filePath, cancellationToken);
}
