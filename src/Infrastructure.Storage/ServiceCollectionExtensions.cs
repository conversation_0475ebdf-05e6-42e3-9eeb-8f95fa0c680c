using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using OneTalent.AtrAdminService.Application.Files.Repositories;
using OneTalent.BlobStorage.Extensions;

namespace OneTalent.AtrAdminService.Infrastructure.Storage;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection RegisterInfrastructureStorageServices(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services.RegisterFileStorageServices(configuration);

        return services
            .AddScoped<IFileStorageRepository, FileStorageRepository>();
    }

    public static void UseBlobStorage(this WebApplication webApplication) =>
        webApplication.Services.RunStorageInitializer();
}
