using OneTalent.Common.Extensions.Configuration;
using System.ComponentModel.DataAnnotations;

namespace OneTalent.AtrAdminService.Infrastructure.Storage;

public sealed class AzureBlobStorageOptions : IConfig
{
    public static string SectionName => "AzureBlobStorage";

    [Required]
    public string ContainerName { get; set; } = null!;

    [Required]
    public string ConnectionString { get; set; } = null!;
}
