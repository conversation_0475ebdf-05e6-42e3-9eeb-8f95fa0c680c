using MassTransit;
using Microsoft.EntityFrameworkCore;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Configurations;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Common;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Interceptors;
using OneTalent.Common.Extensions.ItemId;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer;

public sealed class AtrAdminDbContext(
    DbContextOptions<AtrAdminDbContext> options,
    TimeProvider timeProvider
) : DbContext(options)
{
    internal const string DefaultSchemaName = "ATR_ADMIN";
    internal const string MigrationSchemaName = "ATR_ADMIN_Mig";
    public const string OutboxSchemaName = "ATR_ADMIN_Outbox";
    public const string HangfireSchemaName = "ATR_ADMIN_Hangfire";

    public DbSet<ActionEntity> Actions { get; init; }

    public DbSet<ActionStatusEntity> ActionStatuses { get; init; }

    public DbSet<ActionTypeEntity> ActionTypes { get; init; }

    public DbSet<AtrAdminFileInfoEntity> UploadedFileInfo { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) =>
        optionsBuilder.AddInterceptors(new SuppressOutboxLogsInterceptor());

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema(DefaultSchemaName);

        modelBuilder.AddTransactionalOutboxEntities(x => x.Metadata.SetSchema(OutboxSchemaName));

        modelBuilder.ApplyConfiguration(new ActionEntityConfiguration());
        modelBuilder.ApplyConfiguration(new ActionStatusEntityConfiguration());
        modelBuilder.ApplyConfiguration(new ActionTypeEntityConfiguration());
        modelBuilder.ApplyConfiguration(new AdminFileInfoEntityConfiguration());
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder) =>
        configurationBuilder.Properties<ItemId>().HaveConversion<ItemIdValueConverter>();

    public override int SaveChanges(bool acceptAllChangesOnSuccess)
    {
        HandleTrackedEntities();

        try
        {
            ChangeTracker.AutoDetectChangesEnabled = false;
            var count = base.SaveChanges(acceptAllChangesOnSuccess);
            return count;
        }
        finally
        {
            ChangeTracker.AutoDetectChangesEnabled = true;
        }
    }

    public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
        CancellationToken cancellationToken = default)
    {
        HandleTrackedEntities();
        // var operationItemIds = DetectChanges();
        try
        {
            ChangeTracker.AutoDetectChangesEnabled = false;

            // await NotifyChangesAsync(operationItemIds, cancellationToken);

            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }
        finally
        {
            ChangeTracker.AutoDetectChangesEnabled = true;
        }
    }

    private void HandleTrackedEntities()
    {
        ChangeTracker.DetectChanges();

        var entries = ChangeTracker.Entries().Where(e => e.State is EntityState.Modified or EntityState.Added);

        foreach (var entry in entries)
        {
            var utcNow = timeProvider.GetUtcNow();

            if (entry is { Entity: CreationTrackableEntityBase, State: EntityState.Added })
            {
                entry.Property(nameof(CreationTrackableEntityBase.CreatedDate)).CurrentValue = utcNow;
            }

            if (entry.Entity is ITrackLastModified)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Property(nameof(ITrackLastModified.LastModifiedDate)).CurrentValue = utcNow;

                        break;
                    case EntityState.Modified:
                        if (entry.Properties.Any(p =>
                                p.IsModified && p.Metadata.Name != nameof(ITrackLastModified.LastModifiedDate)))
                        {
                            entry.Property(nameof(ITrackLastModified.LastModifiedDate)).CurrentValue = utcNow;
                        }
                        else
                        {
                            entry.State = EntityState.Unchanged;
                        }

                        break;
                }
            }
        }
    }
}
