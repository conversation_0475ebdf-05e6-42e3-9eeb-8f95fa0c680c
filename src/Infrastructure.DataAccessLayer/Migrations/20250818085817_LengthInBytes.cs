using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Migrations;

/// <inheritdoc />
public partial class LengthInBytes : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<long>(
            name: "LengthInBytes",
            schema: "ATR_ADMIN",
            table: "UploadedFileInfo",
            type: "bigint",
            nullable: false,
            defaultValue: 0L);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "LengthInBytes",
            schema: "ATR_ADMIN",
            table: "UploadedFileInfo");
    }
}
