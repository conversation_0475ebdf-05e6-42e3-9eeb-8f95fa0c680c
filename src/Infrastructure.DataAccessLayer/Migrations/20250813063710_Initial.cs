using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Migrations;

/// <inheritdoc />
public partial class Initial : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.EnsureSchema(
            name: "ATR_ADMIN");

        migrationBuilder.EnsureSchema(
            name: "ATR_ADMIN_Outbox");

        migrationBuilder.CreateTable(
            name: "ActionStatuses",
            schema: "ATR_ADMIN",
            columns: table => new
            {
                Id = table.Column<long>(type: "bigint", nullable: false),
                LastModifiedDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                Value = table.Column<string>(type: "varchar(370)", unicode: false, maxLength: 370, nullable: false),
                Name = table.Column<string>(type: "varchar(370)", unicode: false, maxLength: 370, nullable: false),
                IsActive = table.Column<bool>(type: "bit", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ActionStatuses", x => x.Id);
            });

        migrationBuilder.CreateTable(
            name: "ActionTypes",
            schema: "ATR_ADMIN",
            columns: table => new
            {
                Id = table.Column<long>(type: "bigint", nullable: false),
                LastModifiedDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                Value = table.Column<string>(type: "varchar(370)", unicode: false, maxLength: 370, nullable: false),
                Name = table.Column<string>(type: "varchar(370)", unicode: false, maxLength: 370, nullable: false),
                IsActive = table.Column<bool>(type: "bit", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ActionTypes", x => x.Id);
            });

        migrationBuilder.CreateTable(
            name: "InboxState",
            schema: "ATR_ADMIN_Outbox",
            columns: table => new
            {
                Id = table.Column<long>(type: "bigint", nullable: false)
                    .Annotation("SqlServer:Identity", "1, 1"),
                MessageId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ConsumerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                LockId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                RowVersion = table.Column<byte[]>(type: "rowversion", rowVersion: true, nullable: true),
                Received = table.Column<DateTime>(type: "datetime2", nullable: false),
                ReceiveCount = table.Column<int>(type: "int", nullable: false),
                ExpirationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                Consumed = table.Column<DateTime>(type: "datetime2", nullable: true),
                Delivered = table.Column<DateTime>(type: "datetime2", nullable: true),
                LastSequenceNumber = table.Column<long>(type: "bigint", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_InboxState", x => x.Id);
                table.UniqueConstraint("AK_InboxState_MessageId_ConsumerId", x => new { x.MessageId, x.ConsumerId });
            });

        migrationBuilder.CreateTable(
            name: "OutboxState",
            schema: "ATR_ADMIN_Outbox",
            columns: table => new
            {
                OutboxId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                LockId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                RowVersion = table.Column<byte[]>(type: "rowversion", rowVersion: true, nullable: true),
                Created = table.Column<DateTime>(type: "datetime2", nullable: false),
                Delivered = table.Column<DateTime>(type: "datetime2", nullable: true),
                LastSequenceNumber = table.Column<long>(type: "bigint", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_OutboxState", x => x.OutboxId);
            });

        migrationBuilder.CreateTable(
            name: "UploadedFileInfo",
            schema: "ATR_ADMIN",
            columns: table => new
            {
                AtrAdminEmployeeId = table.Column<string>(type: "varchar(50)", unicode: false, maxLength: 50, nullable: false),
                FileActionTypeId = table.Column<int>(type: "int", nullable: false),
                FileId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                FileName = table.Column<string>(type: "varchar(370)", unicode: false, maxLength: 370, nullable: false),
                LastModifiedDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_UploadedFileInfo", x => new { x.AtrAdminEmployeeId, x.FileActionTypeId });
            });

        migrationBuilder.CreateTable(
            name: "Actions",
            schema: "ATR_ADMIN",
            columns: table => new
            {
                Id = table.Column<long>(type: "bigint", nullable: false)
                    .Annotation("SqlServer:Identity", "1, 1"),
                StatusId = table.Column<long>(type: "bigint", nullable: false),
                TypeId = table.Column<long>(type: "bigint", nullable: false),
                IsValidation = table.Column<bool>(type: "bit", nullable: false),
                AtrCycleId = table.Column<long>(type: "bigint", nullable: false),
                InitiatorId = table.Column<string>(type: "varchar(50)", unicode: false, maxLength: 50, nullable: false),
                StartDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                EndDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                InputFileId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                InputFileName = table.Column<string>(type: "varchar(370)", unicode: false, maxLength: 370, nullable: true),
                OutputFileId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                OutputFileName = table.Column<string>(type: "varchar(370)", unicode: false, maxLength: 370, nullable: true),
                LastModifiedDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                CreatedDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Actions", x => x.Id);
                table.ForeignKey(
                    name: "FK_Actions_ActionStatuses_StatusId",
                    column: x => x.StatusId,
                    principalSchema: "ATR_ADMIN",
                    principalTable: "ActionStatuses",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_Actions_ActionTypes_TypeId",
                    column: x => x.TypeId,
                    principalSchema: "ATR_ADMIN",
                    principalTable: "ActionTypes",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateTable(
            name: "OutboxMessage",
            schema: "ATR_ADMIN_Outbox",
            columns: table => new
            {
                SequenceNumber = table.Column<long>(type: "bigint", nullable: false)
                    .Annotation("SqlServer:Identity", "1, 1"),
                EnqueueTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                SentTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                Headers = table.Column<string>(type: "nvarchar(max)", nullable: true),
                Properties = table.Column<string>(type: "nvarchar(max)", nullable: true),
                InboxMessageId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                InboxConsumerId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                OutboxId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                MessageId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ContentType = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                MessageType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                Body = table.Column<string>(type: "nvarchar(max)", nullable: false),
                ConversationId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                CorrelationId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                InitiatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                RequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                SourceAddress = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                DestinationAddress = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                ResponseAddress = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                FaultAddress = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                ExpirationTime = table.Column<DateTime>(type: "datetime2", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_OutboxMessage", x => x.SequenceNumber);
                table.ForeignKey(
                    name: "FK_OutboxMessage_InboxState_InboxMessageId_InboxConsumerId",
                    columns: x => new { x.InboxMessageId, x.InboxConsumerId },
                    principalSchema: "ATR_ADMIN_Outbox",
                    principalTable: "InboxState",
                    principalColumns: new[] { "MessageId", "ConsumerId" });
                table.ForeignKey(
                    name: "FK_OutboxMessage_OutboxState_OutboxId",
                    column: x => x.OutboxId,
                    principalSchema: "ATR_ADMIN_Outbox",
                    principalTable: "OutboxState",
                    principalColumn: "OutboxId");
            });

        migrationBuilder.CreateIndex(
            name: "IX_Actions_AtrCycleId",
            schema: "ATR_ADMIN",
            table: "Actions",
            column: "AtrCycleId");

        migrationBuilder.CreateIndex(
            name: "IX_Actions_StatusId",
            schema: "ATR_ADMIN",
            table: "Actions",
            column: "StatusId");

        migrationBuilder.CreateIndex(
            name: "IX_Actions_TypeId",
            schema: "ATR_ADMIN",
            table: "Actions",
            column: "TypeId");

        migrationBuilder.CreateIndex(
            name: "IX_InboxState_Delivered",
            schema: "ATR_ADMIN_Outbox",
            table: "InboxState",
            column: "Delivered");

        migrationBuilder.CreateIndex(
            name: "IX_OutboxMessage_EnqueueTime",
            schema: "ATR_ADMIN_Outbox",
            table: "OutboxMessage",
            column: "EnqueueTime");

        migrationBuilder.CreateIndex(
            name: "IX_OutboxMessage_ExpirationTime",
            schema: "ATR_ADMIN_Outbox",
            table: "OutboxMessage",
            column: "ExpirationTime");

        migrationBuilder.CreateIndex(
            name: "IX_OutboxMessage_InboxMessageId_InboxConsumerId_SequenceNumber",
            schema: "ATR_ADMIN_Outbox",
            table: "OutboxMessage",
            columns: new[] { "InboxMessageId", "InboxConsumerId", "SequenceNumber" },
            unique: true,
            filter: "[InboxMessageId] IS NOT NULL AND [InboxConsumerId] IS NOT NULL");

        migrationBuilder.CreateIndex(
            name: "IX_OutboxMessage_OutboxId_SequenceNumber",
            schema: "ATR_ADMIN_Outbox",
            table: "OutboxMessage",
            columns: new[] { "OutboxId", "SequenceNumber" },
            unique: true,
            filter: "[OutboxId] IS NOT NULL");

        migrationBuilder.CreateIndex(
            name: "IX_OutboxState_Created",
            schema: "ATR_ADMIN_Outbox",
            table: "OutboxState",
            column: "Created");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "Actions",
            schema: "ATR_ADMIN");

        migrationBuilder.DropTable(
            name: "OutboxMessage",
            schema: "ATR_ADMIN_Outbox");

        migrationBuilder.DropTable(
            name: "UploadedFileInfo",
            schema: "ATR_ADMIN");

        migrationBuilder.DropTable(
            name: "ActionStatuses",
            schema: "ATR_ADMIN");

        migrationBuilder.DropTable(
            name: "ActionTypes",
            schema: "ATR_ADMIN");

        migrationBuilder.DropTable(
            name: "InboxState",
            schema: "ATR_ADMIN_Outbox");

        migrationBuilder.DropTable(
            name: "OutboxState",
            schema: "ATR_ADMIN_Outbox");
    }
}
