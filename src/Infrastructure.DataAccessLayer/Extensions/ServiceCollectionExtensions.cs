using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using OneTalent.AtrAdminService.Application.Actions.Repositories;
using OneTalent.AtrAdminService.Application.Common;
using OneTalent.AtrAdminService.Application.Files.Repositories;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Repositories.Actions;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Repositories.AtrAdmin;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.SeedData;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection RegisterInfrastructureDataAccessLayerServices(this IServiceCollection services,
        IConfiguration configuration)
    {
        var dbConnectionString = configuration.GetConnectionString(Constants.Constants.DbConnectionStringName);

        if (dbConnectionString is null || dbConnectionString.Length <= 0)
        {
            throw new InvalidOperationException("DB connection string cannot be null.");
        }

        services.AddHealthChecks()
            .AddSqlServer(dbConnectionString, healthQuery: "SELECT 1;", name: "sql",
                failureStatus: HealthStatus.Degraded, tags: ["detailed"]);

        return services
            .AddDbContext<AtrAdminDbContext>(builder =>
            {
#if DEBUG
                builder.EnableSensitiveDataLogging();
                builder.EnableDetailedErrors();
#endif
                builder.UseSqlServer(dbConnectionString,
                    options => options
                        .MigrationsHistoryTable(HistoryRepository.DefaultTableName, AtrAdminDbContext.MigrationSchemaName)
                        .EnableRetryOnFailure());
            })
            .AddScoped<IUnitOfWork, UnitOfWork>()
            .AddScoped<IActionReadRepository, ActionReadRepository>()
            .AddScoped<IActionWriteRepository, ActionWriteRepository>()
            .AddScoped<IUploadedFileInfoReadRepository, UploadedFileInfoReadRepository>()
            .AddScoped<IUploadedFileInfoWriteRepository, UploadedFileInfoWriteRepository>()
            .AddSingleton<ISeedDataService, SeedDataService>();
    }
}
