using Microsoft.EntityFrameworkCore;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Common;
using OneTalent.Common.Extensions.Paging;
using OneTalent.Common.Extensions.Sorting;
using System.Linq.Expressions;
using System.Reflection;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Extensions;

internal static class QueryableExtensions
{
    public const string CaseInsensitiveCollation = "Latin1_General_CI_AS";

    public static async Task<PagedListResult<TDomainModel>> AsPaginatedResultAsync<TDomainModel>(
        this IQueryable<TDomainModel> query,
        OffsetPage offsetPage,
        CancellationToken cancellationToken)
    {
        var result = await query
            .Select(x => new
            {
                Item = x,
                TotalCount = query.Count()
            })
            .Skip((offsetPage.PageNumber - 1) * offsetPage.PageSize)
            .Take(offsetPage.PageSize)
            .ToListAsync(cancellationToken);

        return new PagedListResult<TDomainModel>(
            offsetPage,
            result.Select(x => x.Item).ToList(),
            result.FirstOrDefault()?.TotalCount ?? 0
        );
    }

    public static async Task<SimpleListResult<TDomainModel>> AsSimpleListResultAsync<TDomainModel>(
        this IQueryable<TDomainModel> query,
        CancellationToken cancellationToken) => new(await query.ToListAsync(cancellationToken));

    public static IQueryable<TEntity> WithSorting<TEntity>(this IQueryable<TEntity> query, OrderBy? orderBy)
        where TEntity : EntityBase
    {
        query = orderBy != null
            ? orderBy.Direction switch
            {
                SortDirection.Ascending => query
                    .OrderBy(FieldSortExpression<TEntity>(orderBy.Field))
                    .ThenBy(o => o.Id),
                SortDirection.Descending => query
                    .OrderByDescending(FieldSortExpression<TEntity>(orderBy.Field))
                    .ThenByDescending(o => o.Id),
                _ => query
            }
            : query.OrderBy(e => e.Id);

        return query;
    }

    private static Expression<Func<TEntity, object>> FieldSortExpression<TEntity>(string field) =>
        CreatePropertyExpression<TEntity>(field);

    private static Expression<Func<TEntity, object>> CreatePropertyExpression<TEntity>(string propertyName)
    {
        var propertyInfo = typeof(TEntity).GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance)
                           ?? throw new ArgumentException($"Property '{propertyName}' not found on entity {typeof(TEntity).Name}");

        var parameter = Expression.Parameter(typeof(TEntity), "s");

        var propertyAccess = Expression.MakeMemberAccess(parameter, propertyInfo);

        var convert = Expression.Convert(propertyAccess, typeof(object));

        return Expression.Lambda<Func<TEntity, object>>(convert, parameter);
    }
}
