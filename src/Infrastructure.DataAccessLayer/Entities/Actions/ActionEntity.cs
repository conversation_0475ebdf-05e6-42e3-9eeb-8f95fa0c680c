using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Common;
using OneTalent.Common.Extensions.ItemId;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;

public class ActionEntity : CreationTrackableEntityBase, ITrackLastModified
{
    public required ActionStatus StatusId { get; set; }

    public ActionStatusEntity Status { get; set; } = null!;

    public required ActionType TypeId { get; set; }

    public ActionTypeEntity Type { get; set; } = null!;

    public required bool IsValidation { get; set; }

    public required ItemId AtrCycleId { get; set; }

    public required string InitiatorId { get; set; }

    public required DateTimeOffset StartDate { get; set; }

    public DateTimeOffset? EndDate { get; set; }

    public Guid? InputFileId { get; set; }

    public string? InputFileName { get; set; }

    public Guid? OutputFileId { get; set; }

    public string? OutputFileName { get; set; }

    public DateTimeOffset LastModifiedDate { get; set; }
}
