using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Common;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;

public class AtrAdminFileInfoEntity : ITrackLastModified
{
    public required string AtrAdminEmployeeId { get; set; }

    public required FileActionType FileActionTypeId { get; set; }

    public required Guid FileId { get; set; }

    public required string FileName { get; set; }

    public required long LengthInBytes { get; set; }

    public DateTimeOffset LastModifiedDate { get; set; }
}
