<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" />
    <PackageReference Include="MassTransit.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application\Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="OneTalent.AtrAdminService.WebAPI.Common.IntegrationTests" />
  </ItemGroup>

</Project>
