using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.SeedData;

internal partial class SeedingDataHelper
{
    /// <summary>
    /// Lookup class contains environment agnostic values
    /// that are the same for all environments
    /// </summary>
    internal static class Lookups
    {
        internal static readonly List<ActionStatusEntity> ActionStatusEntities =
        [
            PropertiesHelper.GetBaseLookupEntity<ActionStatusEntity, ActionStatus>(ActionStatus.Pending),
            PropertiesHelper.GetBaseLookupEntity<ActionStatusEntity, ActionStatus>(ActionStatus.InProgress),
            PropertiesHelper.GetBaseLookupEntity<ActionStatusEntity, ActionStatus>(ActionStatus.Completed),
            PropertiesHelper.GetBaseLookupEntity<ActionStatusEntity, ActionStatus>(ActionStatus.Canceled),
            PropertiesHelper.GetBaseLookupEntity<ActionStatusEntity, ActionStatus>(ActionStatus.Failed)
        ];

        internal static readonly List<ActionTypeEntity> ActionTypesEntities =
        [
            PropertiesHelper.GetBaseLookupEntity<ActionTypeEntity, ActionType>(ActionType.AtrFormsLaunch),
            PropertiesHelper.GetBaseLookupEntity<ActionTypeEntity, ActionType>(ActionType.ExportAtrFormsToExcel),
            PropertiesHelper.GetBaseLookupEntity<ActionTypeEntity, ActionType>(ActionType.BulkTransition),
            PropertiesHelper.GetBaseLookupEntity<ActionTypeEntity, ActionType>(ActionType.BulkReAssign),
            PropertiesHelper.GetBaseLookupEntity<ActionTypeEntity, ActionType>(ActionType.BulkCancel)
        ];
    }

    internal static void SeedAtrAdminLookups(AtrAdminDbContext dbContext)
    {
        dbContext.ActionStatuses.AddRange(Lookups.ActionStatusEntities);
        dbContext.ActionTypes.AddRange(Lookups.ActionTypesEntities);

        dbContext.SaveChanges();
    }
}
