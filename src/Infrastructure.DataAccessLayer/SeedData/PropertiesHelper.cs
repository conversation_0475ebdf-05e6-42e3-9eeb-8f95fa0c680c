using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Common;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.SeedData;

internal static class PropertiesHelper
{
    public static T GetBaseLookupEntity<T, TEnum>(TEnum enumValue, IFormatProvider? provider = null) where T : BaseLookupEntity<TEnum>, new()
        where TEnum : Enum
    {
        var memberInfo = enumValue.GetType()
             .GetMember(enumValue.ToString())
            .FirstOrDefault() ?? throw new ArgumentException($"Property '{enumValue}' not found.");

        var displayAttribute = memberInfo.GetCustomAttribute<DisplayAttribute>();

        var obsoleteAttribute = memberInfo.GetCustomAttribute<ObsoleteAttribute>();

        return new T
        {
            Id = enumValue,
            Value = memberInfo.Name,
            Name = displayAttribute?.Name ?? enumValue.ToString(),
            IsActive = obsoleteAttribute == null
        };
    }
}
