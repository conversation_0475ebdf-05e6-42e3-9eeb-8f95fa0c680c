using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.SeedData;

internal partial class SeedingDataHelper
{
    internal static class Actions
    {
        internal static List<ActionEntity> ActionEntities =>
        [
            new()
            {
                AtrCycleId = 2,
                StatusId = ActionStatus.Pending,
                TypeId = ActionType.AtrFormsLaunch,
                StartDate = new DateTimeOffset(new DateTime(2025, 8, 6), TimeSpan.Zero),
                EndDate = new DateTimeOffset(new DateTime(2025, 9, 6), TimeSpan.Zero),
                IsValidation = true,
                InitiatorId = "99905178"
            },
            new()
            {
                AtrCycleId = 2,
                StatusId = ActionStatus.InProgress,
                TypeId = ActionType.AtrFormsLaunch,
                StartDate = new DateTimeOffset(new DateTime(2025, 8, 6), TimeSpan.Zero),
                EndDate = new DateTimeOffset(new DateTime(2025, 9, 6), TimeSpan.Zero),
                IsValidation = true,
                InitiatorId = "99905178"
            },
            new()
            {
                AtrCycleId = 2,
                StatusId = ActionStatus.Completed,
                TypeId = ActionType.AtrFormsLaunch,
                StartDate = new DateTimeOffset(new DateTime(2025, 8, 6), TimeSpan.Zero),
                EndDate = new DateTimeOffset(new DateTime(2025, 9, 6), TimeSpan.Zero),
                IsValidation = false,
                InitiatorId = "99905178"
            }
        ];
    }

    internal static void SeedAtrAdminActions(AtrAdminDbContext dbContext)
    {
        dbContext.Actions.AddRange(Actions.ActionEntities);

        dbContext.SaveChanges();
    }
}
