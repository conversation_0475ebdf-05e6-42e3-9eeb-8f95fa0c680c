using Microsoft.Extensions.Hosting;
using OneTalent.AtrAdminService.Application.Common.Constants;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.SeedData;

internal class SeedDataService : ISeedDataService
{
    public void SeedData(AtrAdminDbContext dbContext, IHostEnvironment environment)
    {
        SeedingDataHelper.SeedAtrAdminLookups(dbContext);

        if (environment.IsDevelopment() || environment.IsEnvironment(EnvironmentNames.DevEnvironmentName))
        {
            SeedingDataHelper.SeedAtrAdminActions(dbContext);
        }
    }
}
