namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer;

internal static class TextLengthTypes
{
    public const int EmployeeIdMaxLength = 50;
    public const int TinyTextMaxLength = 50;
    public const int ShortTextMaxLength = 250;
    public const int MiddleTextMaxLength = 370;
    public const int LongTextMaxLength = 4000;
    public const int VeryLongTextMaxLength = 65000;
    public const int NameMaxLength = 255;
}
