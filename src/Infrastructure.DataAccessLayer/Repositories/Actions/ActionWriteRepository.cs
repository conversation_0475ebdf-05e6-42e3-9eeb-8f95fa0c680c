using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Repositories;
using OneTalent.Common.Extensions.Exceptions;
using OneTalent.Common.Extensions.ItemId;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Repositories.Actions;

internal class ActionWriteRepository(AtrAdminDbContext dbContext) : IActionWriteRepository
{
    // TODO: AddAsync?
    public async Task<ItemId> AddAndSaveAsync(Action action, CancellationToken cancellationToken)
    {
        var entity = action.ToEntity();

        dbContext.Actions.Add(entity);

        await dbContext.SaveChangesAsync(cancellationToken);

        return entity.Id;
    }

    public async Task UpdateAsync(ItemId actionId, ActionStatus status, CancellationToken cancellationToken)
    {
        var entity = await dbContext.Actions.FindAsync([actionId], cancellationToken)
            ?? throw new ItemNotFoundException($"Action {actionId} is not found");

        entity.StatusId = status;
    }
}
