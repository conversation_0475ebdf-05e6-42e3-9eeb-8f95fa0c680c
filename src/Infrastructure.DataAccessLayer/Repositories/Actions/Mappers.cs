using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;
using System.Linq.Expressions;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Repositories.Actions;

internal static class Mappers
{
    public static ActionEntity ToEntity(this Action action) => new()
    {
        TypeId = action.Type,
        IsValidation = action.IsValidation,
        StatusId = action.Status,
        AtrCycleId = 0,
        InitiatorId = ((EmployeeBase)action.Initiator).EmployeeId,
        StartDate = action.StartDate,
        InputFileId = action.InputFileId,
        InputFileName = action.InputFileName,
        OutputFileId = action.OutputFileId,
        OutputFileName = action.OutputFileName
    };

    public static IQueryable<Action> AsActions(this IQueryable<ActionEntity> source) =>
        source.Select(AsAction());

    private static Expression<Func<ActionEntity, Action>> AsAction() =>
        source => new Action(
                source.Id,
                source.IsValidation,
                source.StatusId,
                source.TypeId,
                new EmployeeBase(source.InitiatorId),
                source.StartDate,
                source.EndDate,
                source.InputFileId,
                source.InputFileName,
                source.OutputFileId,
                source.OutputFileName
            );
}
