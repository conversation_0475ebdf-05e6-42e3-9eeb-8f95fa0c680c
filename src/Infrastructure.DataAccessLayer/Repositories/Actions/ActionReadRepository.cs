using Microsoft.EntityFrameworkCore;
using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Repositories;
using OneTalent.AtrAdminService.Application.Actions.Repositories.Models;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Extensions;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.Common.Extensions.Paging;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Repositories.Actions;

internal class ActionReadRepository(AtrAdminDbContext dbContext) : IActionReadRepository
{
    protected AtrAdminDbContext DbContext { get; } = dbContext;

    protected virtual IQueryable<ActionEntity> GetQuery() => DbContext.Actions.AsNoTracking();

    public async Task<Action?> GetCurrentActionToProcessAsync(CancellationToken cancellationToken) =>
        await GetQuery()
            .OrderByDescending(x => x.Id)
            .Where(x => x.StatusId == ActionStatus.InProgress || x.StatusId == ActionStatus.Pending)
            .AsActions()
            .FirstOrDefaultAsync(cancellationToken);

    public async Task<ActionStatus?> GetActionStatusAsync(ItemId actionId, CancellationToken cancellationToken) =>
        await GetQuery()
            .Where(x => x.Id == actionId)
            .Select(x => (ActionStatus?)x.StatusId)
            .FirstOrDefaultAsync(cancellationToken);

    public async Task<PagedListResult<Action>> SearchAllActionsAsync(ActionsQueryOptions options, CancellationToken cancellationToken) => 
        await GetQuery()
            .WithSorting(options.OrderBy)
            .AsActions()
            .AsPaginatedResultAsync(options.OffsetPage, cancellationToken);
}
