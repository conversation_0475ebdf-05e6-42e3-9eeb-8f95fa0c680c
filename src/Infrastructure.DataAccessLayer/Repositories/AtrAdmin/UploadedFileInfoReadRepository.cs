using Microsoft.EntityFrameworkCore;
using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.AtrAdminService.Application.Files.Repositories;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Repositories.AtrAdmin;

public class UploadedFileInfoReadRepository(AtrAdminDbContext dbContext) : IUploadedFileInfoReadRepository
{
    protected AtrAdminDbContext DbContext { get; } = dbContext;

    protected virtual IQueryable<AtrAdminFileInfoEntity> GetQuery() => DbContext.UploadedFileInfo.AsNoTracking();

    public async Task<AtrAdminFileInfo?> GetAsync(
        string atrAdminEmployeeId,
        FileActionType fileActionType,
        CancellationToken cancellationToken) =>
        await GetQuery()
            .Where(x => x.AtrAdminEmployeeId == atrAdminEmployeeId && x.FileActionTypeId == fileActionType)
            .AsDomainModels()
            .FirstOrDefaultAsync(cancellationToken);
}
