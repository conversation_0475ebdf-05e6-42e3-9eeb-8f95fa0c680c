using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.AtrAdminService.Application.Files.Repositories;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;
using OneTalent.Common.Extensions.Exceptions;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Repositories.AtrAdmin;

public class UploadedFileInfoWriteRepository(AtrAdminDbContext dbContext) : UploadedFileInfoReadRepository(dbContext), IUploadedFileInfoWriteRepository
{
    protected override IQueryable<AtrAdminFileInfoEntity> GetQuery() => DbContext.UploadedFileInfo;

    public async Task UpsertAsync(AtrAdminFileInfo fileInfo, CancellationToken cancellationToken)
    {
        var entity = await DbContext.UploadedFileInfo
            .FindAsync([fileInfo.AtrAdminEmployeeId, fileInfo.FileActionType], cancellationToken);

        if (entity is null)
        {
            DbContext.UploadedFileInfo.Add(fileInfo.ToEntity());
        }
        else
        {
            entity.FileName = fileInfo.Name;
            entity.FileId = fileInfo.FileId;
        }
    }

    public async Task DeleteAsync(
        string atrAdminEmployeeId,
        FileActionType fileActionType,
        CancellationToken cancellationToken)
    {
        var entity = await DbContext.UploadedFileInfo.FindAsync([atrAdminEmployeeId, fileActionType], cancellationToken)
                     ?? throw new ItemNotFoundException(
                         $"File info for AtrAdminEmployeeId={atrAdminEmployeeId} FileActionType={fileActionType} not found.");

        DbContext.UploadedFileInfo.Remove(entity);
    }
}
