using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;
using System.Linq.Expressions;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Repositories.AtrAdmin;

internal static class Mappers
{
    public static AtrAdminFileInfoEntity ToEntity(this AtrAdminFileInfo source) => new()
    {
        AtrAdminEmployeeId = source.AtrAdminEmployeeId,
        FileActionTypeId = source.FileActionType,
        FileId = source.FileId,
        FileName = source.Name,
        LengthInBytes = source.LengthInBytes,
    };

    public static IQueryable<AtrAdminFileInfo> AsDomainModels(this IQueryable<AtrAdminFileInfoEntity> source) =>
        source.Select(AsDomainModel());

    private static Expression<Func<AtrAdminFileInfoEntity, AtrAdminFileInfo>> AsDomainModel() =>
        source =>
            new AtrAdminFileInfo(
                    source.AtrAdminEmployeeId,
                    source.FileActionTypeId,
                    source.FileName,
                    FileType.Input,
                    StorageArea.Staging,
                    source.LengthInBytes
                ) { FileId = source.FileId };
}
