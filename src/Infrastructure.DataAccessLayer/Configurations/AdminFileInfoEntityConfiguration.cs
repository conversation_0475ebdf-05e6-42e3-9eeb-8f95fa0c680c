using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Configurations;

internal class AdminFileInfoEntityConfiguration : IEntityTypeConfiguration<AtrAdminFileInfoEntity>
{
    public void Configure(EntityTypeBuilder<AtrAdminFileInfoEntity> builder)
    {
        builder.ToTable("UploadedFileInfo");
        builder.HasKey(p => new { EmployeeId = p.AtrAdminEmployeeId, p.FileActionTypeId });
        builder.Property(p => p.AtrAdminEmployeeId).IsEmployeeId();
        builder.Property(p => p.FileName).HasMaxLength(TextLengthTypes.MiddleTextMaxLength).IsUnicode(false);
    }
}
