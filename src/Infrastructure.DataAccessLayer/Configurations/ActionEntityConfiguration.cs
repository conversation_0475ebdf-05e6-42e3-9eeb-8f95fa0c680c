using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Configurations;

internal class ActionEntityConfiguration : IEntityTypeConfiguration<ActionEntity>
{
    public void Configure(EntityTypeBuilder<ActionEntity> builder)
    {
        builder.ToTable("Actions");
        builder.Property(p => p.Id).UseIdentityColumn();
        builder.Property(p => p.StatusId).HasConversion<long>();
        builder.Property(p => p.TypeId).HasConversion<long>();
        builder.Property(p => p.InitiatorId).IsEmployeeId();
        builder.Property(p => p.InputFileName).HasMaxLength(TextLengthTypes.MiddleTextMaxLength).IsUnicode(false);
        builder.Property(p => p.OutputFileName).HasMaxLength(TextLengthTypes.MiddleTextMaxLength).IsUnicode(false);

        builder.HasIndex(p => p.AtrCycleId);
    }
}
