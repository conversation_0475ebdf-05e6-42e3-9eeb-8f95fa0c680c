using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Entities.Actions;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Configurations;

internal class ActionTypeEntityConfiguration : IEntityTypeConfiguration<ActionTypeEntity>
{
    public void Configure(EntityTypeBuilder<ActionTypeEntity> builder)
    {
        builder.ToTable("ActionTypes", a => a.Metadata.SetSchema(AtrAdminDbContext.DefaultSchemaName));
        builder.<PERSON>Key(p => p.Id);
        builder.Property(p => p.Id).HasConversion<long>();

        builder.Property(p => p.Name).HasMaxLength(TextLengthTypes.MiddleTextMaxLength).IsUnicode(false).IsRequired();
        builder.Property(p => p.Value).HasMaxLength(TextLengthTypes.MiddleTextMaxLength).IsUnicode(false).IsRequired();
        builder.Property(p => p.IsActive).IsRequired();
    }
}
