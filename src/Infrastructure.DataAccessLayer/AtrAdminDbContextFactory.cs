using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.EntityFrameworkCore.Migrations;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer;

public class AtrAdminDbContextFactory : IDesignTimeDbContextFactory<AtrAdminDbContext>
{
    public AtrAdminDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<AtrAdminDbContext>();
        optionsBuilder.UseSqlServer("Server=.;Trusted_Connection=True;Database=FeedbackDb;TrustServerCertificate=True;",
            options => options
                .MigrationsHistoryTable(HistoryRepository.DefaultTableName, AtrAdminDbContext.MigrationSchemaName)
                .EnableRetryOnFailure());
        return new AtrAdminDbContext(optionsBuilder.Options, TimeProvider.System);
    }
}
