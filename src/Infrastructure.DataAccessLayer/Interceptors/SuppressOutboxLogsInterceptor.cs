using Microsoft.EntityFrameworkCore.Diagnostics;
using Serilog.Context;
using System.Data.Common;

namespace OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Interceptors;

public sealed class SuppressOutboxLogsInterceptor : IDbCommandInterceptor
{
    private static readonly ReadOnlyMemory<char> _outboxSchemaName = AtrAdminDbContext.OutboxSchemaName.AsMemory();

    private const string SuppressedPropertyName = "Suppressed";

    private const int ApproximateSchemaPositionStart = 11;
    private const int ApproximateSchemaPositionLength = 30;

    private bool _isPropertyPushed;

    public DbCommand CommandInitialized(CommandEndEventData eventData, DbCommand result)
    {
        if (!_isPropertyPushed && ContainsOutboxSchema(result.CommandText))
        {
            _ = LogContext.PushProperty(SuppressedPropertyName, true);

            _isPropertyPushed = true;
        }

        return result;
    }

    private static bool ContainsOutboxSchema(string commandText)
    {
        return commandText.Length > ApproximateSchemaPositionStart + ApproximateSchemaPositionLength && 
               commandText.AsSpan(ApproximateSchemaPositionStart, ApproximateSchemaPositionLength)
                   .Contains(_outboxSchemaName.Span, StringComparison.InvariantCulture);
    }
}
