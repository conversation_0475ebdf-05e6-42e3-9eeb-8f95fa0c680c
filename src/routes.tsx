import { lazy } from 'react';
import { Navigate, NonIndexRouteObject } from 'react-router-dom';

import { NoAccessPage } from '@/atr/pages';
import { Route } from '@/shared/contracts/router';
import AdminLayout from '@/shared/layouts/AdminCommonLayout/AdminCommonLayout';

const AtrPerformanceFormsPage = lazy(async () => ({
  default: (await import('@/atr/pages')).PerformanceFormsPage
}));

const SupportAdminEmployeePage = lazy(async () => ({
  default: (await import('@/support/general/pages/EmployeePage/index'))
    .EmployeePage
}));

export const AppRoutes: NonIndexRouteObject[] = [
  {
    path: '/',
    element: <AdminLayout />,
    children: [
      {
        path: '/',
        element: <Navigate to={Route.AdminGeneral} replace />
      },
      {
        path: Route.AdminGeneral,
        children: [
          {
            path: Route.AdminGeneralEmployeeData,
            element: <SupportAdminEmployeePage />
          }
        ]
      },
      {
        path: Route.ATRPerformanceForms,
        element: <AtrPerformanceFormsPage />
      }
    ]
  },
  {
    path: Route.NoAccess,
    element: <NoAccessPage />
  },
  { path: '*', element: <Navigate to={Route.NoAccess} replace /> }
];
