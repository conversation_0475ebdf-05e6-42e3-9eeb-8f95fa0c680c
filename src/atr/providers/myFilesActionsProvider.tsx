import React, { createContext, useContext, useState } from 'react';

import { CustomFile } from '@ot/onetalent-ui-kit';
import { QueryObserverResult, useIsMutating } from '@tanstack/react-query';
import noop from 'lodash/noop';

import { Nullable } from '@oh/contracts';

import {
  ActionModel,
  ActionType,
  useDeleteMyFile,
  useMyFiles,
  useUploadFile
} from '@/atr/domain';

export interface MyFilesActionsContextType {
  // getMyFile
  myFile: ActionModel | undefined;
  isPendingMyFile: boolean;
  isMyFileError: boolean;
  refetchMyFile: () => Promise<QueryObserverResult<ActionModel, Error>>;
  // uploadMyFile
  uploadMyFile: (file: File) => void;
  isPendingUploadMyFile: boolean;
  isUploadMyFileError: boolean;
  // deleteMyFile
  deleteMyFile: () => void;
  isPendingDeleteMyFile: boolean;
  isDeleteMyFileError: boolean;
  myLocalFile: Nullable<CustomFile>;
  setMyLocalFile: (file: Nullable<CustomFile>) => void;
  // actionType
  actionType: ActionType;
}

const MyFilesActionsContext = createContext<MyFilesActionsContextType>({
  // getMyFile
  myFile: undefined,
  isPendingMyFile: false,
  isMyFileError: false,
  refetchMyFile: noop as MyFilesActionsContextType['refetchMyFile'],
  //uploadMyFile
  uploadMyFile: noop,
  isPendingUploadMyFile: false,
  isUploadMyFileError: false,
  // deleteMyFile
  deleteMyFile: noop,
  isPendingDeleteMyFile: false,
  isDeleteMyFileError: false,
  //localFile
  myLocalFile: null,
  setMyLocalFile: noop,
  //actionType
  actionType: ActionType.Launch
});

export const MyFilesActionsProvider: React.FC<{
  children: React.ReactNode;
  actionType: ActionType;
}> = ({ children, actionType }) => {
  const [myLocalFile, setMyLocalFile] = useState<Nullable<CustomFile>>(null);
  const {
    data: myFile,
    isLoading: isPendingMyFile,
    isError: isMyFileError,
    refetch: refetchMyFile
  } = useMyFiles(actionType);

  const {
    mutate: uploadMyFile,
    isPending: isPendingUploadMyFile,
    isError: isUploadMyFileError
  } = useUploadFile(actionType);

  const {
    mutate: deleteMyFile,
    isPending: isPendingDeleteMyFile,
    isError: isDeleteMyFileError
  } = useDeleteMyFile(actionType);

  // Track global upload state
  const isUploading =
    useIsMutating({ mutationKey: ['uploadFile', actionType] }) > 0;

  return (
    <MyFilesActionsContext.Provider
      value={{
        myFile,
        isPendingMyFile,
        isMyFileError,
        refetchMyFile,
        uploadMyFile,
        isPendingUploadMyFile: isUploading || isPendingUploadMyFile,
        isUploadMyFileError,
        deleteMyFile,
        isPendingDeleteMyFile,
        isDeleteMyFileError,
        myLocalFile,
        setMyLocalFile,
        actionType
      }}
    >
      {children}
    </MyFilesActionsContext.Provider>
  );
};

export const useMyFilesActions = () => useContext(MyFilesActionsContext);
