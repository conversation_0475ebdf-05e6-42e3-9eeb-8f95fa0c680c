export function getFilenameFromContentDisposition(
  header: string | null
): string | null {
  if (!header) return null;

  // Prefer filename*
  const filenameStarMatch = header.match(/filename\*\s*=\s*([^;]+)/i);
  if (filenameStarMatch) {
    // Example: UTF-8''ATR%20Forms%20Launch.csv
    const value = filenameStarMatch[1].trim();
    const parts = value.split("''");
    if (parts.length === 2) {
      return decodeURIComponent(parts[1]);
    }
    return value;
  }

  // Fallback: match filename
  const filenameMatch = header.match(/filename\s*=\s*"?([^";]+)"?/i);
  if (filenameMatch) {
    return filenameMatch[1];
  }

  return null;
}
