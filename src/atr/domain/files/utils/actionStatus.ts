import { ActionStatus } from '@/atr/domain/actions';

export const isActionStatusDraft = (
  status: ActionStatus | null | undefined
): boolean => status === ActionStatus.Draft;

export const isActionStatusInProgress = (
  status: ActionStatus | null | undefined
): boolean => status === ActionStatus.InProgress;

export const isActionStatusPending = (
  status: ActionStatus | null | undefined
): boolean => status === ActionStatus.Pending;

export const isActionStatusCompleted = (
  status: ActionStatus | null | undefined
): boolean => status === ActionStatus.Completed;

export const isActionStatusCanceled = (
  status: ActionStatus | null | undefined
): boolean => status === ActionStatus.Canceled;

export const isActionStatusCancelling = (
  status: ActionStatus | null | undefined
): boolean => status === ActionStatus.Cancelling;

export const isActionStatusFailed = (
  status: ActionStatus | null | undefined
): boolean => status === ActionStatus.Failed;

export const isActionStatusPendingOrInProgress = (
  status: ActionStatus | null | undefined
): boolean => isActionStatusPending(status) || isActionStatusInProgress(status);
