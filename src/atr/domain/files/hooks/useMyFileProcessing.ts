import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useAdminServiceApi } from '@/api/atr';
import { ACTIONS_REQUEST_KEYS } from '@/atr/domain/actions/actionsRequestKeys';
import {
  ActionModel,
  ActionStatus,
  ActionType
} from '@/atr/domain/actions/models';
import { ScheduleActionRequest } from '@/atr/domain/files/';
import { EMPLOYEES_FILES } from '@/atr/domain/files/filesRequestKeys';
import { logger } from '@/shared/logger';

export const useMyFileProcessing = (type: ActionType) => {
  const { atrActionsCommandsApi } = useAdminServiceApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (model: ScheduleActionRequest) => {
      try {
        return await atrActionsCommandsApi.atrAdminV1AdminsMeActionsPost({
          actionType: type,
          scheduleActionRequest: model
        });
      } catch (error) {
        logger.error('Failed to load atrAdminV1AdminsMeActionsPost', error);

        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.setQueryData(EMPLOYEES_FILES.getMyFiles(type), () => data);
      queryClient.invalidateQueries({
        queryKey: ACTIONS_REQUEST_KEYS.all()
      });
    },
    onError: () => {
      queryClient.setQueryData(
        EMPLOYEES_FILES.getMyFiles(type),
        (old: ActionModel) => {
          return { ...old, status: ActionStatus.Failed };
        }
      );
    }
  });
};
