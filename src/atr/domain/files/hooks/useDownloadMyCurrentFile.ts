import { useAdminServiceApi } from 'api/atr';

import { ActionType } from '@/atr/domain';
import { getFilenameFromContentDisposition } from '@/atr/domain/files/utils';
import { logger } from '@/shared/logger';
import { downloadFile } from '@/shared/utils';

export function useDownloadMyCurrentFile() {
  const { atrActionsQueriesApi } = useAdminServiceApi();

  return async (actionType: ActionType) => {
    try {
      const response =
        await atrActionsQueriesApi.atrAdminV1AdminsMeActionsCurrentInputFileGetRaw(
          {
            actionType: actionType
          }
        );

      const contentDisposition = response.raw.headers.get(
        'content-disposition'
      );
      const parsedFilename =
        getFilenameFromContentDisposition(contentDisposition);

      const blob = await response.raw.blob();

      downloadFile(blob, parsedFilename ?? 'input file.csv');
    } catch (error) {
      logger.error(
        'Failed to load atrAdminV1AdminsMeActionsCurrentInputFileGet',
        error
      );
      throw error;
    }
  };
}
