import { ToastVariant, useToast } from '@ot/onetalent-ui-kit';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useAdminServiceApi } from '@/api/atr';
import { ACTIONS_REQUEST_KEYS } from '@/atr/domain/actions/actionsRequestKeys';
import {
  ActionModel,
  ActionStatus,
  ActionType
} from '@/atr/domain/actions/models';
import { EMPLOYEES_FILES } from '@/atr/domain/files/filesRequestKeys';
import { logger } from '@/shared/logger';

export const useMyFileCancelProcessing = () => {
  const { atrActionsCommandsApi } = useAdminServiceApi();
  const queryClient = useQueryClient();
  const { addToast } = useToast();

  return useMutation({
    mutationFn: async ({
      actionId
    }: {
      actionId: NonNullable<ActionModel['id']>;
      type: ActionType;
    }) => {
      try {
        return await atrActionsCommandsApi.atrAdminV1AdminsMeActionsActionIdPatch(
          {
            actionId: actionId.toString()
          }
        );
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1AdminsMeActionsActionIdPatch',
          error
        );

        throw error;
      }
    },
    onSuccess: (data, { type }) => {
      if (data === ActionStatus.Completed) {
        addToast({
          variant: ToastVariant.Success,
          title: 'The action has been cancelled successfully'
        });
      } else if (data === ActionStatus.Cancelling) {
        addToast({
          variant: ToastVariant.Info,
          title: 'The cancellation process has been initiated. Please wait'
        });
      }

      queryClient.invalidateQueries({
        queryKey: EMPLOYEES_FILES.getMyFiles(type)
      });
      queryClient.invalidateQueries({
        queryKey: ACTIONS_REQUEST_KEYS.all()
      });
    },
    onError: (e: unknown) => {
      const error = e as { response?: { status?: number } };
      if (error.response?.status === 409) {
        addToast({
          variant: ToastVariant.Error,
          title:
            'The action cannot be cancelled as the action has already been processed'
        });
      } else {
        addToast({
          variant: ToastVariant.Error,
          title: 'An error occurred, the action was not cancelled'
        });
      }
    }
  });
};
