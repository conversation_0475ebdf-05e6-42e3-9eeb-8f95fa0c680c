import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { useAdminServiceApi } from 'api/atr';
import { logger } from 'shared/logger';

import { PerformanceFormsSearchPayload } from '../models';
import { PERFORMANCE_FORMS_REQUEST_KEYS } from '../performanceFormsRequestKeys';

import { STALE_TIME } from '../../../../shared/constants';

export const useAtrFormIdSearch = (
  payload: Pick<PerformanceFormsSearchPayload, 'formIds'>,
  enabled = true
) => {
  const { atrPerformanceForms } = useAdminServiceApi();
  const queryFn = useCallback(
    async (input: PerformanceFormsSearchPayload) => {
      try {
        return atrPerformanceForms.atrAdminV1PerformanceFormsSearchPost({
          performanceFormSearchQuery: input
        });
      } catch (error) {
        logger.error('Failed to load v1PerformanceFormsSearchPost', error);

        throw error;
      }
    },
    [atrPerformanceForms]
  );

  return useQuery({
    placeholderData: (prevData) => prevData,
    enabled,
    queryKey: PERFORMANCE_FORMS_REQUEST_KEYS.getAtrFormIdSearch(payload),
    queryFn: () => queryFn(payload),
    select: (rawResponse) =>
      rawResponse.items.map((statusItem) => ({
        id: statusItem.id,
        displayName: statusItem.id
      })),
    staleTime: STALE_TIME.MINUTES.TEN
  });
};
