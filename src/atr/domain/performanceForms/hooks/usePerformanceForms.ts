import { logger } from '@services/logger';
import { useQuery } from '@tanstack/react-query';

import { AdminService, useAdminServiceApi } from '@/api/atr';
import { SortDirection } from '@/api/atr/generated';
import { STALE_TIME } from '@/shared/constants';
import { transformPaginationData } from '@/shared/core/domainModel';

import { PERFORMANCE_FORMS_REQUEST_KEYS } from '../performanceFormsRequestKeys';

export const DEFAULT_SORTING = {
  field: 'id',
  direction: SortDirection.Descending
};

export const usePerformanceForms = (
  input: AdminService.PerformanceFormSearchQuery
) => {
  const { atrPerformanceForms } = useAdminServiceApi();

  return useQuery({
    queryKey: PERFORMANCE_FORMS_REQUEST_KEYS.getPerformanceForms(input),
    queryFn: async () => {
      try {
        const response =
          await atrPerformanceForms.atrAdminV1PerformanceFormsSearchPost({
            performanceFormSearchQuery: input
          });

        return { ...response, page: transformPaginationData(response.paging) };
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1PerformanceFormsSearchPost',
          error
        );

        throw error;
      }
    },
    staleTime: STALE_TIME.MINUTES.TEN
  });
};
