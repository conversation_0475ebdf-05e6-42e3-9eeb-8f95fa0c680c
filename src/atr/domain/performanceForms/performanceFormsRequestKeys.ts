import { AdminService } from '@/api/atr';

export const PERFORMANCE_FORMS_REQUEST_KEYS = {
  all: () => ['performanceForms'] as const,
  getPerformanceForms: (payload: AdminService.PerformanceFormSearchQuery) =>
    [
      ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
      'getPerformanceForms',
      payload
    ] as const,
  getAtrFormIdSearch: (payload: AdminService.PerformanceFormSearchQuery) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getAtrFormIdSearch',
    payload
  ]
} as const;
