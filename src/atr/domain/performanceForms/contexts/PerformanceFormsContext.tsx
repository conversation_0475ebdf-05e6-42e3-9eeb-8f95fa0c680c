import {
  create<PERSON>ontext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useMemo,
  useState
} from 'react';

import { FilterOption } from '@ot/onetalent-ui-kit';
import { UseQueryResult } from '@tanstack/react-query';

import { GenericDataSourceState, useDataSourceState } from 'shared/components';
import { DEFAULT_PAGE_NUMBER, DEFAULT_PAGE_SIZE } from 'shared/constants';
import {
  GenericPaginationData,
  PaginationModel
} from 'shared/core/domainModel';

import { DEFAULT_SORTING, usePerformanceForms } from '../hooks';
import {
  FilterKeys,
  PerformanceFormModel,
  PerformanceFormsSearchPayload
} from '../models';

type PerformanceFormsContext = {
  queryResult: UseQueryResult<{
    page: PaginationModel;
    items: Array<PerformanceFormModel>;
    paging: GenericPaginationData;
  }>;
  selectedFilters: Partial<Record<FilterKeys, FilterOption[]>>;
  dataSource: {
    dataSourceState: GenericDataSourceState<string, unknown>;
    setDataSourceState: (
      nextState: React.SetStateAction<GenericDataSourceState<string, unknown>>
    ) => void;
  };
  onSetFilter: (id: FilterKeys) => (filterValues?: FilterOption[]) => void;
  onSetPageSize: (pageSize: number) => void;
  onClearFilters: () => void;
};

export const PerformanceFormsContext = createContext<PerformanceFormsContext>(
  undefined!
);

export const usePerformanceFormsContext = () =>
  useContext(PerformanceFormsContext);

export const PerformanceFormsContextProvider: FC<PropsWithChildren> = ({
  children
}) => {
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [selectedFilters, setSelectedFilters] = useState<
    Partial<Record<FilterKeys, FilterOption[]>>
  >({});

  const { dataSource, parameters: pagination } = useDataSourceState<string>({
    pageSize,
    initialPageNumber: DEFAULT_PAGE_NUMBER,
    filters: selectedFilters
  });

  const queryPayload = useMemo<PerformanceFormsSearchPayload>(
    () =>
      Object.entries(selectedFilters).reduce(
        (accumulator, [id, filter]) => ({
          ...accumulator,
          [id]: filter.map((option) => option.id)
        }),
        {}
      ),
    [selectedFilters]
  );

  const queryResult = usePerformanceForms({
    ...queryPayload,
    ...pagination,
    orderBy: DEFAULT_SORTING
  });

  const handleClearFilters = useCallback(() => setSelectedFilters({}), []);

  const handleSetFilter = useCallback(
    (id: FilterKeys) => (filterValues?: FilterOption[]) => {
      setSelectedFilters((currentSelectedFilters) => {
        if (filterValues) {
          return {
            ...currentSelectedFilters,
            [id]: filterValues
          };
        }
        const newSelectedFilters = { ...currentSelectedFilters };
        delete newSelectedFilters[id];
        return {
          ...newSelectedFilters
        };
      });
    },
    []
  );

  const contextValue = useMemo(
    () => ({
      dataSource,
      queryResult,
      selectedFilters,
      onSetPageSize: setPageSize,
      onSetFilter: handleSetFilter,
      onClearFilters: handleClearFilters
    }),
    [
      dataSource,
      handleClearFilters,
      handleSetFilter,
      queryResult,
      selectedFilters
    ]
  );

  return (
    <PerformanceFormsContext.Provider value={contextValue}>
      {children}
    </PerformanceFormsContext.Provider>
  );
};
