import { useCallback, useMemo } from 'react';
import { useLocation } from 'react-router-dom';

import { Button, Drawer } from '@ot/onetalent-ui-kit';

import { ActionLog } from '@/atr/components';
import { Drawer as DrawerType } from '@/shared/constants';
import { useToggleVisibility } from '@/shared/hooks';

export const ActionLogDrawer = () => {
  const { toggleDrawer } = useToggleVisibility();

  const { search } = useLocation();

  const referred = useMemo(() => {
    const params = new URLSearchParams(search);

    return params.get('referred') as DrawerType | null;
  }, [search]);

  const closeActionLogDrawer = useCallback(() => {
    toggleDrawer({
      name: DrawerType.ActionLogDrawer,
      show: false,
      referred
    });
  }, [referred, toggleDrawer]);

  const showReferredDrawer = useCallback(() => {
    referred &&
      toggleDrawer({
        name: referred,
        show: true
      });
  }, [referred, toggleDrawer]);

  const onBack = useCallback(() => {
    closeActionLogDrawer();
    showReferredDrawer();
  }, [closeActionLogDrawer, showReferredDrawer]);

  return (
    <Drawer
      dataAttributes="Drawer"
      header="Action Log"
      variant="Primary"
      showExpand={false}
      isExpanded={true}
      onClose={closeActionLogDrawer}
      onBack={onBack}
      footer={
        <Button variant="Secondary" onClick={closeActionLogDrawer}>
          Close
        </Button>
      }
      isOpen
      showBack={!!referred}
      showClose
      fullHeight
    >
      <ActionLog />
    </Drawer>
  );
};
