import { FC } from 'react';

import { Filters } from '@ot/onetalent-ui-kit';

import { FilterKeys, usePerformanceFormsContext } from 'atr/domain';

import { FilterAtrFormId } from './FilterAtrFormId';

export const FiltersSection: FC = () => {
  const { onSetFilter, selectedFilters, onClearFilters } =
    usePerformanceFormsContext();

  return (
    <Filters<FilterKeys>
      className="w-full"
      onClearFilters={onClearFilters}
      selectedFilters={selectedFilters}
      onFilterChanged={onSetFilter}
    >
      <FilterAtrFormId />
    </Filters>
  );
};
