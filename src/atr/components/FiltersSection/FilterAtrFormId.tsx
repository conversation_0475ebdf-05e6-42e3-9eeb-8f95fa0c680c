import { useMemo, useState } from 'react';

import { MultiselectFilter } from '@ot/onetalent-ui-kit';
import debounce from 'lodash/debounce';

import { useAtrFormIdSearch } from '../../domain';

export const FilterAtrFormId = () => {
  const [search, setSearch] = useState('');

  const handleSearch = useMemo(() => debounce(setSearch, 500), [setSearch]);

  const { data: responseOptions = [] } = useAtrFormIdSearch({
    formIds: search ? [search] : undefined
  });

  return (
    <MultiselectFilter
      id="formIds"
      label="ATR Form ID"
      onSearch={handleSearch}
      options={responseOptions}
      visibilityMode="always"
      basis="242"
    />
  );
};
