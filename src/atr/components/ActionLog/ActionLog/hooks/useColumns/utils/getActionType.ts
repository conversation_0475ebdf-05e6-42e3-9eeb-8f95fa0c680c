import { logger } from '@services/logger';

import { ActionType } from '@/atr/domain';

export const getActionType = (type: ActionType) => {
  switch (type) {
    case ActionType.Launch:
      return 'ATR Forms Launch';
    case ActionType.ReAssign:
      return 'Bulk Re-Assign';
    case ActionType.Transition:
      return 'Bulk Transition';
    case ActionType.Export:
      return 'Export ATR Forms';
    default: {
      const _: never = type;

      logger.error('Not all action types are covered');

      return null;
    }
  }
};
