import { Tag } from '@ot/onetalent-ui-kit';
import { logger } from '@services/logger';

import { ActionStatus } from '@/atr/domain';

export const renderStatus = (status: ActionStatus) => {
  switch (status) {
    case ActionStatus.Canceled:
      return <Tag variant="Grey">Cancelled</Tag>;
    case ActionStatus.Cancelling:
      return <Tag variant="Grey">Cancelling</Tag>;
    case ActionStatus.Draft:
      return <Tag variant="Grey">Draft</Tag>;
    case ActionStatus.Completed:
      return <Tag variant="Green">Completed</Tag>;
    case ActionStatus.Failed:
      return <Tag variant="Red">Failed</Tag>;
    case ActionStatus.InProgress:
      return <Tag variant="PurpleHeart">In Progress</Tag>;
    case ActionStatus.Pending:
      return <Tag variant="Orange">Pending</Tag>;
    default: {
      const _: never = status;

      logger.error('Not all action statuses are covered');

      return null;
    }
  }
};
