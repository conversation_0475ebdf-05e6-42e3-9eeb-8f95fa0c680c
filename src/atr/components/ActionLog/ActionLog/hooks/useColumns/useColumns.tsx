import { useMemo } from 'react';

import { DataColumnProps } from '@epam/uui-core';
import {
  ContextMenuTrigger,
  ContextualMenu,
  ContextualMenuList,
  ContextualMenuListItem,
  Icon,
  IconButton,
  Tooltip
} from '@ot/onetalent-ui-kit';

import {
  ActionModel,
  ActionType,
  useDownloadMyInputFile,
  useDownloadMyOutputFile
} from '@/atr/domain';
import { isActionStatusPendingOrInProgress } from '@/atr/domain/files/utils';
import { isActionStatusCompleted } from '@/atr/utils';
import { CellContent, UserInfo, UserInfoSize } from '@/shared/components';
import { formatDateToDateTime } from '@/shared/utils';

import { getActionType, renderStatus } from './utils';

enum ColumnName {
  ActionID = 'Action ID',
  ActionType = 'Action Type',
  ValidationRun = 'Validation/Run',
  Status = 'Status',
  InitiatorID = 'Initiator ID',
  InitiatorName = 'Initiator Name',
  StartDateTime = 'Start Date/Time',
  EndDateTime = 'End Date/Time',
  Menu = 'Menu'
}

export const useColumns = (
  setActionId: (actionId: ActionModel['id'], type: ActionType) => void
): DataColumnProps<ActionModel>[] => {
  const downloadMyInputFile = useDownloadMyInputFile();
  const downloadMyOutputFile = useDownloadMyOutputFile();

  return useMemo(
    () => [
      {
        key: ColumnName.ActionID,
        caption: ColumnName.ActionID,
        width: 128,
        minWidth: 128,
        render: (action) => <CellContent>{action.id}</CellContent>
      },
      {
        key: ColumnName.ActionType,
        caption: ColumnName.ActionType,
        width: 160,
        minWidth: 160,
        grow: 1,
        render: (action) => (
          <CellContent>{getActionType(action.type)}</CellContent>
        )
      },
      {
        key: ColumnName.ValidationRun,
        caption: ColumnName.ValidationRun,
        width: 128,
        minWidth: 128,
        render: (action) => (
          <CellContent>
            {action.isValidation ? 'Validation' : 'Run'}
          </CellContent>
        )
      },
      {
        key: ColumnName.Status,
        caption: ColumnName.Status,
        width: 140,
        minWidth: 140,
        render: (action) => (
          <CellContent classNames="flex items-center gap-4">
            {renderStatus(action.status)}
            {isActionStatusCompleted(action.status) &&
              action.rowsFailed > 0 && (
                <Tooltip title="The output file contains errors" variant="Dark">
                  <Icon
                    name="Warning_ring"
                    className="h-20 min-h-20 w-20 min-w-20 text-main-warning-60"
                  />
                </Tooltip>
              )}
          </CellContent>
        )
      },
      {
        key: ColumnName.InitiatorID,
        caption: ColumnName.InitiatorID,
        width: 104,
        minWidth: 104,
        render: (action) => (
          <CellContent>{action.initiator?.employeeId}</CellContent>
        )
      },
      {
        key: ColumnName.InitiatorName,
        caption: ColumnName.InitiatorName,
        width: 180,
        minWidth: 180,
        grow: 1,
        render: (action) => (
          <CellContent>
            <UserInfo
              {...action.initiator}
              size={UserInfoSize.ExtraSmall}
              fullName={action.initiator?.fullNameEnglish}
              titleClassName="text-body-2-regular"
              hasTooltip
            />
          </CellContent>
        )
      },
      {
        key: ColumnName.StartDateTime,
        caption: ColumnName.StartDateTime,
        width: 192,
        minWidth: 192,
        render: (action) => (
          <CellContent>
            {action.startDate && formatDateToDateTime(action.startDate)}
          </CellContent>
        )
      },
      {
        key: ColumnName.EndDateTime,
        caption: ColumnName.EndDateTime,
        width: 192,
        minWidth: 192,
        render: (action) => (
          <CellContent>
            {action.endDate && formatDateToDateTime(action.endDate)}
          </CellContent>
        )
      },
      {
        key: ColumnName.Menu,
        width: 72,
        minWidth: 72,
        render: (action) => {
          const onInputFileClick = () => {
            action.id && downloadMyInputFile(action.id);
          };
          const onOutputFileClick = () => {
            action.id && downloadMyOutputFile(action.id);
          };
          const onCancelClick = () => setActionId(action.id, action.type);

          const hasInputFile = !!action.inputFileName;
          const hasOutputFile = !!action.outputFileName;
          const canCancel = isActionStatusPendingOrInProgress(action.status);
          const isMenuDisabled = !hasInputFile && !hasOutputFile && !canCancel;

          return (
            <CellContent>
              <ContextualMenu dataAttributes="ContextualMenu">
                <ContextMenuTrigger>
                  <Tooltip title={isMenuDisabled && 'No actions available'}>
                    <IconButton
                      icon="Kebab"
                      variant="Tertiary"
                      disabled={isMenuDisabled}
                    />
                  </Tooltip>
                </ContextMenuTrigger>
                <ContextualMenuList>
                  {hasInputFile && (
                    <ContextualMenuListItem
                      tooltip={{ title: action.inputFileName }}
                      onClick={onInputFileClick}
                    >
                      Download Input File
                    </ContextualMenuListItem>
                  )}
                  {hasOutputFile && (
                    <ContextualMenuListItem
                      tooltip={{ title: action.outputFileName }}
                      onClick={onOutputFileClick}
                    >
                      Download Output File
                    </ContextualMenuListItem>
                  )}
                  {canCancel && (
                    <ContextualMenuListItem onClick={onCancelClick}>
                      Cancel
                    </ContextualMenuListItem>
                  )}
                </ContextualMenuList>
              </ContextualMenu>
            </CellContent>
          );
        }
      }
    ],
    [downloadMyInputFile, downloadMyOutputFile, setActionId]
  );
};
