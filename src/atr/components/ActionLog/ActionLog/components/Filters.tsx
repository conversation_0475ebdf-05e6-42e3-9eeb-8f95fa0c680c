import { FC } from 'react';

import { Button } from '@ot/onetalent-ui-kit';

interface FiltersProps {
  isFetching: boolean;
  onReload(): void;
}

export const Filters: FC<FiltersProps> = ({ onReload, isFetching }) => (
  <div
    data-attributes="ActionLogFilters"
    className="grid grid-cols-[1fr_auto] gap-24"
  >
    <div>{/* TODO: add filters to this container */}</div>
    <Button
      variant="Secondary"
      leftIcon="Refresh"
      disabled={isFetching}
      onClick={onReload}
    >
      Reload
    </Button>
  </div>
);
