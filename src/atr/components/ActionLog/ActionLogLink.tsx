import { FC, useCallback } from 'react';

import { Link, LinkVariant } from '@ot/onetalent-ui-kit';

import { Drawer as DrawerType } from '@/shared/constants';
import { useToggleVisibility } from '@/shared/hooks';

interface ActionLogLinkProps {
  className?: string;
}

export const ActionLogLink: FC<ActionLogLinkProps> = ({ className }) => {
  const { toggleDrawer } = useToggleVisibility();
  const showActionLogDrawer = useCallback(() => {
    toggleDrawer({
      name: DrawerType.ActionLogDrawer,
      referred: DrawerType.ExcelBulkActionsDrawer
    });
  }, [toggleDrawer]);

  return (
    <Link
      dataAttributes="ActionLogLink"
      variant={LinkVariant.Primary}
      onClick={showActionLogDrawer}
      className={className}
    >
      Action Log
    </Link>
  );
};
