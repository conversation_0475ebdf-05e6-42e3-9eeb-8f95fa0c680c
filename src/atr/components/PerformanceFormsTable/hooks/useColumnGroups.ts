import { useMemo } from 'react';

import { DataColumnGroupProps } from '@epam/uui-core';

export enum ColumnGroup {
  AtrForm = 'AtrForm',
  AtrTemplate = 'AtrTemplate',
  AtrFormStatus = 'AtrFormStatus',
  Employee = 'Employee',
  ATRGroup = 'ATRGroup',
  AssessmentManagerLM = 'AssessmentManagerLM',
  AssessmentManagerB2B = 'AssessmentManagerB2B',
  DottedLineManager = 'DottedLineManager',
  LastUpdated = 'LastUpdated',
  UpdatedBy = 'UpdatedBy'
}

const getColumnGroupDisplayName = (columnGroupName: ColumnGroup) => {
  switch (columnGroupName) {
    case ColumnGroup.AtrForm:
      return 'ATR Form';
    case ColumnGroup.AtrTemplate:
      return 'ATR Template';
    case ColumnGroup.AtrFormStatus:
      return 'ATR Form Status';
    case ColumnGroup.Employee:
      return 'Employee';
    case ColumnGroup.ATRGroup:
      return 'ATR Group';
    case ColumnGroup.AssessmentManagerLM:
      return 'Assessment Manager (LM)';
    case ColumnGroup.AssessmentManagerB2B:
      return 'Assessment Manager (B2B)';
    case ColumnGroup.DottedLineManager:
      return 'Dotted Line Manager';
    case ColumnGroup.LastUpdated:
      return 'Last Updated';
    case ColumnGroup.UpdatedBy:
      return 'Last Updated By';
    default:
      return '';
  }
};

export const useColumnGroups = (): DataColumnGroupProps[] => {
  return useMemo(
    () => [
      {
        key: ColumnGroup.AtrForm,
        caption: getColumnGroupDisplayName(ColumnGroup.AtrForm),
        textAlign: 'left'
      },
      {
        key: ColumnGroup.AtrTemplate,
        caption: getColumnGroupDisplayName(ColumnGroup.AtrTemplate),
        textAlign: 'left'
      },
      {
        key: ColumnGroup.AtrFormStatus,
        caption: getColumnGroupDisplayName(ColumnGroup.AtrFormStatus),
        textAlign: 'left'
      },
      {
        key: ColumnGroup.Employee,
        caption: getColumnGroupDisplayName(ColumnGroup.Employee),
        textAlign: 'left'
      },
      {
        key: ColumnGroup.ATRGroup,
        caption: getColumnGroupDisplayName(ColumnGroup.ATRGroup),
        textAlign: 'left'
      },
      {
        key: ColumnGroup.AssessmentManagerLM,
        caption: getColumnGroupDisplayName(ColumnGroup.AssessmentManagerLM),
        textAlign: 'left'
      },
      {
        key: ColumnGroup.AssessmentManagerB2B,
        caption: getColumnGroupDisplayName(ColumnGroup.AssessmentManagerB2B),
        textAlign: 'left'
      },
      {
        key: ColumnGroup.DottedLineManager,
        caption: getColumnGroupDisplayName(ColumnGroup.DottedLineManager),
        textAlign: 'left'
      },
      {
        key: ColumnGroup.LastUpdated,
        caption: getColumnGroupDisplayName(ColumnGroup.LastUpdated),
        textAlign: 'left'
      },
      {
        key: ColumnGroup.UpdatedBy,
        caption: getColumnGroupDisplayName(ColumnGroup.UpdatedBy),
        textAlign: 'left'
      }
    ],
    []
  );
};
