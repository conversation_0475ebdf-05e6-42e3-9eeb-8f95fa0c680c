import { FC } from 'react';

import { usePerformanceFormsContext } from 'atr/domain';

import { PerformanceDataTable } from '@/shared/components';
import { DEFAULT_PAGE_SIZES } from '@/shared/constants/pagination';

import { useColumnGroups, useColumns } from './hooks';
import { PerformanceFormsEmpty } from './PerformanceFormsEmpty';
import { PerformanceFormsError } from './PerformanceFormsError';

export interface PerformanceFormsTableProps {
  emptyScreenSubtitle?: string;
  className?: string;
}

export const PerformanceFormsTable: FC<PerformanceFormsTableProps> = ({
  className
}) => {
  const { queryResult, dataSource, onSetPageSize } =
    usePerformanceFormsContext();

  const columns = useColumns();
  const columnGroups = useColumnGroups();

  return (
    <div data-attributes="PerformanceFormsTable" className={className}>
      <PerformanceDataTable
        dataAttributes="PerformanceFormsTable"
        {...dataSource}
        showContainer
        styles={{
          minHeight: !queryResult.data?.items.length ? 440 : 0
        }}
        queryResult={queryResult}
        columns={columns}
        columnGroups={columnGroups}
        pageSizes={DEFAULT_PAGE_SIZES}
        setPageSize={onSetPageSize}
        renderNoResults={PerformanceFormsEmpty}
        renderError={PerformanceFormsError}
        loaderText="ATR Forms are being loaded"
        paginationSize="24"
      />
    </div>
  );
};
