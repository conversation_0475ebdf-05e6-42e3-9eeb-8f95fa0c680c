import { ReactNode } from 'react';

import { Informer, InformerVariant } from '@ot/onetalent-ui-kit';

import { ActionStatus } from '@/api/atr/generated';
import { ActionLogLink } from '@/atr/components/ActionLog';
import { isActionStatusDraft } from '@/atr/domain/files/utils';
import { useMyFilesActions } from '@/atr/providers';

export const BulkActionsFormInformer = () => {
  const { myFile } = useMyFilesActions();
  const informerProps: {
    variant: InformerVariant;
    title: ReactNode;
    description: ReactNode;
  } = { variant: InformerVariant.Warning, title: null, description: null };

  if (!myFile || isActionStatusDraft(myFile.status)) return null;

  switch (myFile.status) {
    case ActionStatus.Pending:
    case ActionStatus.InProgress: {
      informerProps.variant = InformerVariant.Warning;
      informerProps.title = `The action is now processed on the background with the following Action ID: ${myFile?.id}`;
      informerProps.description = (
        <>
          You may leave this page or remain here to monitor the execution
          results. Alternatively, you can open{' '}
          <ActionLogLink className="inline text-body-2-regular" /> to track
          progress and review the results.
        </>
      );
      break;
    }
    case ActionStatus.Canceled: {
      informerProps.variant = InformerVariant.Info;
      informerProps.title = 'The action has been cancelled';
      informerProps.description =
        'Please download the Results File to review the output';
      break;
    }
    case ActionStatus.Completed: {
      if (myFile.rowsSucceeded === myFile.totalRowsProcessed) {
        informerProps.variant = InformerVariant.Success;
        informerProps.title = 'No errors have been found during the execution';
        informerProps.description = (
          <>
            Total rows processed: {myFile.totalRowsProcessed}. Success:{' '}
            {myFile.rowsSucceeded}. Please download the Results File to see the
            details
          </>
        );
      } else {
        informerProps.variant = InformerVariant.Warning;
        informerProps.title = 'Errors have been found during the execution';
        informerProps.description = (
          <>
            Total rows processed: {myFile.totalRowsProcessed}. Success:{' '}
            {myFile.rowsSucceeded}. Error: {myFile.rowsFailed}. Please download
            the Results File to see the details
          </>
        );
      }
      break;
    }
    case ActionStatus.Failed: {
      informerProps.variant = InformerVariant.Error;
      informerProps.title = 'The execution has been failed';
      informerProps.description =
        'The execution process may have failed due to a corrupted input file or an unexpected error preventing the generation of the output file. Please verify your input file and try again. If the issue persists, contact support for further assistance"';
      break;
    }

    default: {
      return null;
    }
  }

  return <Informer className="mt-24" {...informerProps} />;
};
