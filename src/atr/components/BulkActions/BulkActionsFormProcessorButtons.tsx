import React, { FC } from 'react';

import { Button, ButtonSize, ButtonVariant } from '@ot/onetalent-ui-kit';
import { UseMutateFunction } from '@tanstack/react-query';

import { ActionModel, ScheduleActionRequest } from '@/atr/domain';
import { isActionStatusCompleted } from '@/atr/domain/files/utils';
import { useMyFilesActions } from '@/atr/providers';

interface BulkActionsFormProcessorButtonsProps {
  processMyFile: UseMutateFunction<
    ActionModel,
    Error,
    ScheduleActionRequest,
    unknown
  >;
}

export const BulkActionsFormProcessorButtons: FC<
  BulkActionsFormProcessorButtonsProps
> = ({ processMyFile }) => {
  const { myFile, setMyLocalFile, deleteMyFile } = useMyFilesActions();
  const isValidationDisabled = !(!myFile?.id && !!myFile?.inputFileId);
  const isImportDisabled = !(
    myFile?.id &&
    myFile?.isValidation === true &&
    isActionStatusCompleted(myFile?.status)
  );
  const isHideValidationAndImportButtons =
    myFile?.id &&
    isActionStatusCompleted(myFile?.status) &&
    myFile.isValidation === false;

  const onValidateHandler = () => {
    if (myFile) {
      processMyFile({ ...myFile, id: myFile?.id });
    }
  };
  const onImportHandler = () => {
    if (myFile) {
      processMyFile({ ...myFile, id: myFile?.id });
    }
  };

  const onResetClickHandler = () => {
    setMyLocalFile(null);
    deleteMyFile();
  };
  return (
    <div
      data-attributes="BulkActionsFormControls"
      className="mt-24 flex flex-wrap justify-end gap-24 border-t border-solid border-divider-dark py-24"
    >
      {isHideValidationAndImportButtons ? (
        <Button
          size={ButtonSize.Medium}
          variant={ButtonVariant.Primary}
          tooltip={{
            title:
              'The uploaded file has already been validated and imported. Please click the button to upload a new file'
          }}
          onClick={onResetClickHandler}
        >
          Upload New File
        </Button>
      ) : (
        <>
          <Button
            variant={ButtonVariant.Secondary}
            size={ButtonSize.Medium}
            tooltip={{
              title: !myFile?.inputFileId
                ? 'Please upload the file first'
                : !isImportDisabled && 'The file has already been validated'
            }}
            disabled={isValidationDisabled}
            onClick={onValidateHandler}
          >
            Validate
          </Button>
          <Button
            size={ButtonSize.Medium}
            variant={ButtonVariant.Primary}
            tooltip={{
              title: isImportDisabled && 'Please validate the file first'
            }}
            disabled={isImportDisabled}
            onClick={onImportHandler}
          >
            Import
          </Button>
        </>
      )}
    </div>
  );
};
