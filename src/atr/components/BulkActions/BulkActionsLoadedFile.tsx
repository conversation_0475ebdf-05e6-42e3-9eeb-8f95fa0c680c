import React from 'react';

import {
  CustomFileStatuses,
  Link,
  UploadedFileList
} from '@ot/onetalent-ui-kit';

import {
  useDownloadMyCurrentFile,
  useDownloadMyOutputFile
} from '@/atr/domain';
import {
  isActionStatusCanceled,
  isActionStatusCancelling,
  isActionStatusCompleted,
  isActionStatusPendingOrInProgress
} from '@/atr/domain/files/utils';
import { useMyFilesActions } from '@/atr/providers';

export const BulkActionsLoadedFile = () => {
  const {
    deleteMyFile,
    uploadMyFile,
    setMyLocalFile,
    myFile,
    myLocalFile,
    isUploadMyFileError,
    actionType
  } = useMyFilesActions();
  const file = {
    name: myFile?.inputFileName ?? myLocalFile?.name ?? '',
    size: myFile?.inputFileSize ?? myLocalFile?.size,
    id: myFile?.id?.toString() ?? myLocalFile?.id ?? '',
    status: isUploadMyFileError
      ? CustomFileStatuses.FAILURE
      : CustomFileStatuses.DONE
  };
  const downloadMyCurrentFile = useDownloadMyCurrentFile();
  const downloadMyOutputFile = useDownloadMyOutputFile();

  const onDeleteClickHandler = () => {
    setMyLocalFile(null);
    myFile?.inputFileId && deleteMyFile();
  };
  const onDownloadClickHandler = () => {
    downloadMyCurrentFile(actionType);
  };
  const onRetryClickHandler = () => {
    myLocalFile?.originFile && uploadMyFile(myLocalFile.originFile);
  };

  const onDownloadResultHandler = () => {
    myFile?.id && downloadMyOutputFile(myFile.id);
  };

  return (
    <div data-attributes="ATRFormsLaunchFile">
      <div className="rounded-xl border border-solid border-divider-dark">
        <UploadedFileList
          fileList={[file]}
          onRemove={onDeleteClickHandler}
          onRetryUpload={onRetryClickHandler}
          onDownload={
            file.status !== CustomFileStatuses.FAILURE
              ? onDownloadClickHandler
              : undefined
          }
          viewOnly={
            isActionStatusPendingOrInProgress(myFile?.status) ||
            isActionStatusCancelling(myFile?.status)
          }
        />
      </div>
      {(isActionStatusCompleted(myFile?.status) ||
        isActionStatusCanceled(myFile?.status)) &&
        myFile?.outputFileName && (
          <Link
            className="mx-auto mb-12 mt-[10px]"
            leftIcon="Download"
            onClick={onDownloadResultHandler}
          >
            Download Results file
          </Link>
        )}
    </div>
  );
};
