import React, { useState } from 'react';

import { Link, Text } from '@ot/onetalent-ui-kit';

import { BulkActionsFormInformer } from '@/atr/components/BulkActions/BulkActionsFormInformer';
import { BulkActionsFormLoader } from '@/atr/components/BulkActions/BulkActionsFormLoader';
import { BulkActionsFormProcessorButtons } from '@/atr/components/BulkActions/BulkActionsFormProcessorButtons';
import { useMyFileCancelProcessing, useMyFileProcessing } from '@/atr/domain';
import { useGetMyFilesPolling } from '@/atr/domain/files/hooks/useGetMyFilesPolling';
import {
  isActionStatusCancelling,
  isActionStatusPendingOrInProgress
} from '@/atr/domain/files/utils';
import { useMyFilesActions } from '@/atr/providers';

import { BulkActionsCancelProcessModal } from './BulkActionsCancelProcessModal';

export const BulkActionsFormProcessor = () => {
  const { myFile, actionType } = useMyFilesActions();
  const { mutate: processMyFile, isPending: isPendingProcess } =
    useMyFileProcessing(actionType);
  const [isCancelModalVisible, setIsCancelModalVisible] = useState(false);
  const { mutate: cancelProcessing, isPending: isPendingCancelProcess } =
    useMyFileCancelProcessing();

  const cancelProcess = () => {
    myFile?.id && cancelProcessing({ actionId: myFile.id, type: actionType });
  };
  const changeModalVisibility = () => {
    setIsCancelModalVisible((state) => !state);
  };

  useGetMyFilesPolling();

  return (
    <>
      <BulkActionsFormInformer />
      {!(
        isPendingProcess ||
        isPendingCancelProcess ||
        isActionStatusPendingOrInProgress(myFile?.status) ||
        isActionStatusCancelling(myFile?.status)
      ) ? (
        <BulkActionsFormProcessorButtons processMyFile={processMyFile} />
      ) : (
        <div className="flex grow items-center">
          <BulkActionsFormLoader>
            <Text className="text-center text-header-3-medium">
              Please wait,
              <br /> your file is being{' '}
              {isPendingProcess ||
              isActionStatusPendingOrInProgress(myFile?.status)
                ? myFile?.isValidation
                  ? 'validated'
                  : 'imported'
                : 'canceled'}
              ...
              {!(
                isPendingCancelProcess ||
                isActionStatusCancelling(myFile?.status)
              ) && (
                <Link className="mx-auto mt-24" onClick={changeModalVisibility}>
                  Cancel
                </Link>
              )}
            </Text>
          </BulkActionsFormLoader>
          <BulkActionsCancelProcessModal
            confirmCb={cancelProcess}
            isOpen={isCancelModalVisible}
            changeModalVisibility={changeModalVisibility}
          />
        </div>
      )}
    </>
  );
};
