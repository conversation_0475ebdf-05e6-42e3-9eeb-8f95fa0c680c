using System.ComponentModel.DataAnnotations;

namespace OneTalent.AtrAdminService.Contracts.Constants;

[Flags]
public enum PerformanceFormStep
{
    [Display(Name = "No ATR Form")]
    None = 0x0,

    [Display(Name = "Draft")]
    Draft = 0x100,

    [Display(Name = "Self Assessment")]
    SelfAssessment = 0x200,

    [Display(Name = "Manager Assessment")]
    ManagerAssessment = 0x400,

    [Display(Name = "Dotted Line Manager Endorsement")]
    DottedLineManagerEndorsement = 0x800,

    [Display(Name = "Second Line Manager Endorsement")]
    SecondLineManagerEndorsement = 0x1000,

    [Display(Name = "Normalization")]
    Normalization = 0x2000,

    [Display(Name = "Rating Approval")]
    RatingApproval = 0x4000,

    [Display(Name = "Rating Announcement")]
    RatingAnnouncement = 0x8000,

    [Display(Name = "Completed")]
    Completed = 0x16000
}

public enum PerformanceFormMinorStep
{
    None = 0x0,

    NotStarted = 0x10,

    InProgress = 0x20,

    Completed = 0x30
}
