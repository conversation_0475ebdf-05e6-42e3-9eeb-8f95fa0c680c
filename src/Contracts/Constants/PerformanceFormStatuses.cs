using System.ComponentModel.DataAnnotations;

namespace OneTalent.AtrAdminService.Contracts.Constants;

public enum PerformanceFormStatuses : long
{
    [Display(Name = "Draft")]
    Draft = (long)PerformanceFormStep.Draft,

    [Display(Name = "Self-Assessment - Not Started")]
    SelfAssessmentNotStarted = (long)PerformanceFormStep.SelfAssessment + (long)PerformanceFormMinorStep.NotStarted,

    [Display(Name = "Self-Assessment - In Progress")]
    SelfAssessmentInProgress = (long)PerformanceFormStep.SelfAssessment + (long)PerformanceFormMinorStep.InProgress,

    [Display(Name = "Manager Assessment - Not Started")]
    ManagerAssessmentNotStarted = (long)PerformanceFormStep.ManagerAssessment + (long)PerformanceFormMinorStep.NotStarted,

    [Display(Name = "Manager Assessment - In Progress")]
    ManagerAssessmentInProgress = (long)PerformanceFormStep.ManagerAssessment + (long)PerformanceFormMinorStep.InProgress,

    [Display(Name = "Manager Assessment - Completed")]
    ManagerAssessmentCompleted = (long)PerformanceFormStep.ManagerAssessment + (long)PerformanceFormMinorStep.Completed,

    [Display(Name = "Dotted-Line Manager Endorsement - Not Started")]
    DottedLineManagerEndorsementNotStarted = (long)PerformanceFormStep.DottedLineManagerEndorsement + (long)PerformanceFormMinorStep.NotStarted,

    [Display(Name = "Second-Line Manager Endorsement - Not Started")]
    SecondLineManagerEndorsementNotStarted = (long)PerformanceFormStep.SecondLineManagerEndorsement + (long)PerformanceFormMinorStep.NotStarted,

    [Display(Name = "Second-Line Manager Endorsement - Completed")]
    SecondLineManagerEndorsementCompleted = (long)PerformanceFormStep.SecondLineManagerEndorsement + (long)PerformanceFormMinorStep.Completed,

    [Display(Name = "Normalization - Not Started")]
    NormalizationNotStarted = (long)PerformanceFormStep.Normalization + (long)PerformanceFormMinorStep.NotStarted,

    [Display(Name = "Normalization - In Progress")]
    NormalizationInProgress = (long)PerformanceFormStep.Normalization + (long)PerformanceFormMinorStep.InProgress,

    [Display(Name = "Normalization - Completed")]
    NormalizationCompleted = (long)PerformanceFormStep.Normalization + (long)PerformanceFormMinorStep.Completed,

    [Display(Name = "Rating Approval - Pending")]
    RatingApprovalPending = (long)PerformanceFormStep.RatingApproval + (long)PerformanceFormMinorStep.NotStarted,

    [Display(Name = "Rating Approval - Approved")]
    RatingApprovalApproved = (long)PerformanceFormStep.RatingApproval + (long)PerformanceFormMinorStep.InProgress,

    [Display(Name = "Completed")]
    Completed = (long)PerformanceFormStep.Completed
}
