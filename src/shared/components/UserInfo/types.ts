import { ReactNode } from 'react';

import { TooltipVariant } from '@ot/onetalent-ui-kit';

import { Nullable } from '@oh/contracts';

export enum UserInfoSize {
  ExtraSmall,
  Small,
  Medium,
  Large
}

export enum AlignmentVertical {
  Start,
  Center
}

export interface UserInfoProps {
  email?: Nullable<string>;
  fullName?: Nullable<string>;
  companyName?: Nullable<string>;
  positionName?: Nullable<string>;
  className?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  avatarClassName?: string;
  highlightText?: string;
  headerText?: string;
  size?: UserInfoSize;
  alignmentVertical?: AlignmentVertical;
  isTitleTruncated?: boolean;
  isSubtitleTruncated?: boolean;
  hasTooltip?: boolean;
  tooltipVariant?: TooltipVariant;
  tooltipClassName?: string;
  customTooltip?: ReactNode;
  alwaysShowCustomTooltip?: boolean;
  bottomContent?: ReactNode;
  titleContent?: ReactNode;
  dataAttributes?: string;
}
