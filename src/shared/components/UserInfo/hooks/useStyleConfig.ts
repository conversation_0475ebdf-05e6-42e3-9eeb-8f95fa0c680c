import { useMemo } from 'react';

import { UserInfoSize } from '../types';

interface StyleConfig {
  containerClassName: string;
  infoContainerClassName: string;
  titleTypographyClassName: string;
  subTitleTypographyClassName?: string;
  avatarSize: number;
}

export const useStyleConfig = (size: UserInfoSize): StyleConfig => {
  return useMemo(() => {
    switch (size) {
      case UserInfoSize.ExtraSmall:
        return {
          containerClassName: 'gap-x-[5px]',
          infoContainerClassName: 'gap-y-0',
          titleTypographyClassName: 'text-body-1-medium',
          subTitleTypographyClassName: 'hidden',
          avatarSize: 24
        };
      case UserInfoSize.Small:
        return {
          containerClassName: 'gap-x-8',
          infoContainerClassName: 'gap-y-[3px]',
          titleTypographyClassName: 'text-body-2-medium',
          subTitleTypographyClassName: 'text-body-4-regular',
          avatarSize: 40
        };
      case UserInfoSize.Medium:
        return {
          containerClassName: 'gap-x-12',
          infoContainerClassName: 'gap-y-0',
          titleTypographyClassName: 'text-body-1-medium',
          subTitleTypographyClassName: 'text-body-2-regular',
          avatarSize: 40
        };
      case UserInfoSize.Large:
        return {
          containerClassName: 'gap-x-16',
          infoContainerClassName: 'gap-y-4',
          titleTypographyClassName: 'text-header-3-medium',
          subTitleTypographyClassName: 'text-body-1-regular',
          avatarSize: 60
        };
      default: {
        const _: never = size;
        throw 'Not all cases are covered.';
      }
    }
  }, [size]);
};
