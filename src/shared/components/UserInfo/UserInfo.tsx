import { forwardRef, memo, useMemo } from 'react';

import clsx from 'clsx';

import { TextMatchHighlighter } from '@oh/components';

import { useStyleConfig } from './hooks';
import { AlignmentVertical, UserInfoProps, UserInfoSize } from './types';
import { UserInfoTooltip } from './UserInfoTooltip';

import { Text } from '../text/Text';
import { UserProfileAvatar } from '../UserProfileAvatar';

import { useIsOverflowed } from '../../hooks/useIsOverflowed';

const UserInfoComponent = forwardRef<HTMLDivElement, UserInfoProps>(
  (
    {
      email,
      fullName,
      companyName,
      positionName,
      highlightText,
      className,
      titleClassName,
      subtitleClassName,
      avatarClassName,
      headerText,
      size = UserInfoSize.Small,
      alignmentVertical = AlignmentVertical.Center,
      isTitleTruncated = true,
      isSubtitleTruncated = false,
      hasTooltip = false,
      tooltipVariant = 'Dark',
      tooltipClassName,
      customTooltip,
      bottomContent,
      dataAttributes,
      alwaysShowCustomTooltip = false,
      titleContent = null
    },
    ref
  ) => {
    const {
      containerClassName,
      infoContainerClassName,
      titleTypographyClassName,
      subTitleTypographyClassName,
      avatarSize
    } = useStyleConfig(size);
    const {
      overflowContainerRef: overflowNameRef,
      isOverflowed: isNameOverflowed
    } = useIsOverflowed<HTMLDivElement>();
    const {
      overflowContainerRef: overflowCompanyRef,
      isOverflowed: isCompanyOverflowed
    } = useIsOverflowed<HTMLDivElement>();

    const content = useMemo(
      () => (
        <div
          ref={ref}
          className={clsx(
            'flex min-w-0',
            {
              'items-start': alignmentVertical === AlignmentVertical.Start,
              'items-center': alignmentVertical === AlignmentVertical.Center,
              '[overflow-wrap:anywhere]':
                !isTitleTruncated || !isSubtitleTruncated
            },
            containerClassName,
            className
          )}
          data-attributes={dataAttributes}
        >
          <UserProfileAvatar
            email={email}
            name={fullName}
            size={avatarSize}
            className={avatarClassName}
          />

          <div
            className={clsx(
              'flex min-w-0 flex-col justify-center',
              infoContainerClassName
            )}
          >
            {headerText && (
              <Text
                className={clsx(
                  'truncate text-text-body',
                  subTitleTypographyClassName,
                  subtitleClassName
                )}
              >
                {headerText}
              </Text>
            )}
            <div className="flex gap-x-4">
              <Text
                className={clsx(titleTypographyClassName, titleClassName, {
                  truncate: isTitleTruncated
                })}
                ref={overflowNameRef}
              >
                {highlightText ? (
                  <TextMatchHighlighter
                    className="inline"
                    text={fullName}
                    textToHighlight={highlightText}
                  />
                ) : (
                  fullName
                )}
              </Text>
              {titleContent}
            </div>
            {(positionName || companyName) && (
              <Text
                className={clsx(
                  'text-text-body',
                  { truncate: isSubtitleTruncated },
                  subTitleTypographyClassName,
                  subtitleClassName
                )}
                ref={overflowCompanyRef}
              >
                {positionName}
                {positionName && companyName && ', '}
                {companyName}
              </Text>
            )}

            {bottomContent}
          </div>
        </div>
      ),
      [
        ref,
        alignmentVertical,
        containerClassName,
        className,
        dataAttributes,
        email,
        fullName,
        avatarSize,
        avatarClassName,
        infoContainerClassName,
        headerText,
        subTitleTypographyClassName,
        subtitleClassName,
        titleTypographyClassName,
        titleClassName,
        isTitleTruncated,
        overflowNameRef,
        highlightText,
        titleContent,
        positionName,
        companyName,
        isSubtitleTruncated,
        overflowCompanyRef,
        bottomContent
      ]
    );

    const isOverflowed =
      (isNameOverflowed || isCompanyOverflowed) && !alwaysShowCustomTooltip;

    return hasTooltip ? (
      <UserInfoTooltip
        tooltipVariant={tooltipVariant}
        fullName={fullName}
        isOverflowed={isOverflowed}
        companyName={companyName}
        positionName={positionName}
        subtitleClasses="mt-8 text-body-3-regular"
        titleClasses="text-cta-2-medium"
        className={tooltipClassName}
        customTooltipContent={!isOverflowed && customTooltip}
      >
        {content}
      </UserInfoTooltip>
    ) : (
      content
    );
  }
);

export const UserInfo = memo(UserInfoComponent);

UserInfo.displayName = 'UserInfo';
