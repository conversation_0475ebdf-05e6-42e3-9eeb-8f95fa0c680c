import { useMemo, useState } from 'react';

import clsx from 'clsx';

import { GenericDataTable, GenericTableProps } from './GenericDataTable';

import './styles.performance.scss';

export type PerformanceDataTableProps<TItem, TId, TFilters> = GenericTableProps<
  TItem,
  TId,
  TFilters
> & {
  maxVisibleRowsWithoutScroll?: number;
  showContainer?: boolean;
  pageSizes?: number[];
  setPageSize?: (pageSize: number) => void;
};

export function PerformanceDataTable<TItem, TId, TFilters>({
  maxVisibleRowsWithoutScroll,
  showContainer = false,
  classNames,
  ...props
}: PerformanceDataTableProps<TItem, TId, TFilters>) {
  const [visibleRowsCount, setVisibleRowsCount] = useState<
    number | undefined
  >();
  const scrollClassName = useMemo(
    () =>
      maxVisibleRowsWithoutScroll &&
      visibleRowsCount &&
      visibleRowsCount > maxVisibleRowsWithoutScroll
        ? 'with-scroll'
        : '',
    [maxVisibleRowsWithoutScroll, visibleRowsCount]
  );

  if (showContainer) {
    return (
      <div
        data-attributes="PerformanceDataTable"
        className="rounded-xl bg-surface-grey_0 p-12"
      >
        <GenericDataTable
          classNames={clsx('performance-table', scrollClassName, classNames)}
          styles={{ minHeight: 'auto' }}
          onVisibleRowsCountChanged={setVisibleRowsCount}
          errorContainerClassName="!h-[468px]"
          {...props}
        />
      </div>
    );
  }

  return (
    <GenericDataTable
      classNames={clsx('performance-table', scrollClassName, classNames)}
      styles={{ minHeight: 'auto' }}
      onVisibleRowsCountChanged={setVisibleRowsCount}
      {...props}
    />
  );
}
