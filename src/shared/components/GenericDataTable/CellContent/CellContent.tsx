import { FC, memo, PropsWithChildren } from 'react';

import clsx from 'clsx';

import { Tooltip, Typography, TypographyVariant } from '@oh/components';
import { TooltipProps } from '@oh/components/dist/types/components/tooltip/types';

import { useConditionalTooltipProps } from 'shared/hooks';

interface CellContentProps {
  tooltipProps?: Partial<TooltipProps>;
  classNames?: string;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
}

export const CellContent: FC<PropsWithChildren<CellContentProps>> = memo(
  ({ children, tooltipProps, classNames = 'line-clamp-1', ...props }) => {
    const cellContent = (
      <Typography
        variant={TypographyVariant.Body2Regular}
        className={clsx('cell-content ml-4 w-full text-ellipsis', classNames)}
        as="div"
        {...props}
      >
        {children}
      </Typography>
    );

    const conditionalTooltipProps = useConditionalTooltipProps(tooltipProps);

    if (tooltipProps) {
      return <Tooltip {...conditionalTooltipProps}>{cellContent}</Tooltip>;
    }

    return cellContent;
  }
);
