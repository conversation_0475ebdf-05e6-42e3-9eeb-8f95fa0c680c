import { FC } from 'react';

import clsx from 'clsx';

import { AvatarLoadManager, UserAvatarProps } from '@oh/components';

import { useGetAvatarRequest } from './useGetAvatarRequest';

import { getUserAvatarEndpoint } from '../../core/domainModel/api';

export interface UserProfileAvatarProps
  extends Omit<Omit<UserAvatarProps, 'sourceUrl'>, 'getRequestFunction'> {
  email?: string | null;
}

export const UserProfileAvatar: FC<UserProfileAvatarProps> = ({
  email,
  name,
  className,
  size,
  dataAttributes
}) => {
  const getAvatarRequest = useGetAvatarRequest();
  const sourceUrl = email ? getUserAvatarEndpoint(email) : undefined;

  return (
    <AvatarLoadManager
      sourceUrl={sourceUrl}
      name={name}
      className={clsx('border border-solid border-surface-grey_0', className)}
      size={size}
      getRequestFunction={getAvatarRequest}
      dataAttributes={dataAttributes}
    />
  );
};
