import { useMemo } from 'react';

import { TooltipProps } from '@oh/components/dist/types/components/tooltip/types';
import { TooltipVariant } from '@oh/constants';

const isHTMLElement = (element: unknown): element is HTMLElement =>
  element instanceof HTMLElement;

type WhileElementsMounted = NonNullable<
  TooltipProps['floatingOptions']
>['whileElementsMounted'];

const showTooltipIfTextIsTruncated: WhileElementsMounted = (
  ref,
  floating,
  update
) => {
  if (!isHTMLElement(ref)) return;

  const isTruncated =
    ref.offsetWidth < ref.scrollWidth || ref.offsetHeight < ref.scrollHeight;

  if (isTruncated) {
    floating.style.display = '';
    update();
  } else {
    floating.style.display = 'none';
  }
};

const defaultTooltipProps: Partial<TooltipProps> = {
  variant: TooltipVariant.Dark,
  floatingOptions: { whileElementsMounted: showTooltipIfTextIsTruncated }
};

export const useConditionalTooltipProps = (
  tooltipProps?: Partial<TooltipProps>
): Partial<TooltipProps> =>
  useMemo(
    () => ({
      ...defaultTooltipProps,
      ...tooltipProps,
      floatingOptions: {
        ...defaultTooltipProps.floatingOptions,
        ...tooltipProps?.floatingOptions
      }
    }),
    [tooltipProps]
  );
