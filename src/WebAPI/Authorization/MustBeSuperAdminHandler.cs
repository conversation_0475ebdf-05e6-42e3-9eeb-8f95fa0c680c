using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using OneTalent.AtrAdminService.WebAPI.Authorization.Settings;
using OneTalent.Common.Extensions.CurrentUser;

namespace OneTalent.AtrAdminService.WebAPI.Authorization;

public class MustBeSuperAdminRequirement : IAuthorizationRequirement { }

public class MustBeSuperAdminHandler(
    ICurrentUserProvider currentUserProvider,
    IOptionsMonitor<SuperAdminConfig> superAdminConfig)
    : AuthorizationHandler<MustBeSuperAdminRequirement>
{
    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        MustBeSuperAdminRequirement requirement)
    {
        var user = currentUserProvider.CurrentUser;
        var isServiceAccount = user?.Id == user?.FullName;
        var email = user?.Email ?? string.Empty;
        var isSuperAdmin = superAdminConfig.CurrentValue.Emails.Contains(email, StringComparer.InvariantCultureIgnoreCase);
        if (isServiceAccount || isSuperAdmin)
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}
