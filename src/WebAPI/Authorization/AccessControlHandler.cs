using Microsoft.AspNetCore.Authorization;
using OneTalent.AtrAdminService.Application.Access.Queries.Services;

namespace OneTalent.AtrAdminService.WebAPI.Authorization;

public class AccessControlRequirement : IAuthorizationRequirement { }

public class AccessControlHandler(
    IAccessControlService accessControlService
) : AuthorizationHandler<AccessControlRequirement>
{
    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        AccessControlRequirement requirement)
    {
        if (context.Resource is not HttpContext httpContext || !(httpContext.User.Identity?.IsAuthenticated ?? false))
        {
            return;
        }

        var cancellationToken = httpContext.RequestAborted;

        var acl = httpContext.GetEndpoint()
            ?.Metadata.OfType<AclAttribute>()
            .SelectMany(x => x.Acl).ToList() ?? [];

        if (await accessControlService.EnsureAllAsync(acl, cancellationToken))
        {
            context.Succeed(requirement);
        }
    }
}
