{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft.EntityFrameworkCore.Database.Command": "Information", "Hangfire": "Information"}}}, "ConnectionStrings": {"OpenTelemetryHost": "http://localhost:4317", "AtrAdminDb": "Server=127.0.0.1,1433;TrustServerCertificate=true;User Id=SA;Password=**********;", "AzureAppConfiguration": "", "RedisCaching": "", "MasstransitDb": "Server=127.0.0.1,1433;TrustServerCertificate=true;Database=master;User Id=SA;Password=**********;", "BlobStorage": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "TenantId": "74892fe7-b6cb-43e7-912b-52194d3fd7c8", "ClientId": "82cd72ca-0c69-4d5c-980b-20856c1d62fe", "Audience": "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe", "ClientSecret": ""}, "Swagger": {"Enabled": true, "AuthEnabled": true}, "DataSyncServiceClient": {"BaseUrl": "https://api.dev.onetalent.adnoc.ae/data-sync/", "Scope": "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"}, "AtrServiceClient": {"BaseUrl": "http://localhost:5024", "Scope": "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"}, "Caching": {"ExpirationInMinutes": 5, "LocalCacheExpirationInMinutes": 5, "MaximumKeyLength": 1024, "Prefix": "atr-admin", "Namespace": "local"}, "SuperAdmins": {"Emails": []}, "UserMapping": {"Enabled": true, "BaseUrl": "https://servicebox-uat.adnoc.ae/OneHub.Api/", "TargetScope": "https://servicebox-uat.adnoc.ae/.default"}, "HttpLogging": {"Disabled": false, "AllowedHeaders": ["sec-ch-ua", "sec-ch-ua-platform", "sec-ch-ua-mobile", "Sec-Fetch-Site", "Sec-Fetch-Mode", "Sec-Fetch-Dest"]}, "MassTransit": {"ServiceBusConfiguration": {"IsEnabled": false, "UseInMemory": false, "ConnectionString": "mock", "EntityNamingConfiguration": {"QueueNamePrefix": "local", "TopicNamePrefix": "local", "SubscriptionNamePrefix": "atr-admin"}, "ConsumersConfiguration": {"ConcurrentMessageLimit": 1, "RetryConfiguration": {"MessageRetryCount": 1, "MessageRetryDelayInMs": 1000}}, "Topics": {"AtrCycleTopic": {"IsEnabled": false, "Subscriptions": {"AtrCycleChangedSubscription": {"IsEnabled": false, "Consumers": {"AtrCycleUpdatedConsumer": {"IsEnabled": true, "ConsumerConfiguration": {"BatchConfiguration": {"MessagesLimit": 20, "TimeLimitInSec": 2, "ConcurrencyLimit": 1}}}}}}}, "EmployeeTopic": {"IsEnabled": false, "Subscriptions": {"EmployeeChangedSubscription": {"IsEnabled": false, "Consumers": {"EmployeeChangedConsumer": {"IsEnabled": true, "ConsumerConfiguration": {"BatchConfiguration": {"MessagesLimit": 20, "TimeLimitInSec": 2, "ConcurrencyLimit": 1}}}}}}}, "FeedbackTopic": {"IsEnabled": false, "Subscriptions": {"FeedbackChangedSubscription": {"IsEnabled": true, "Consumers": {"FeedbackChangedConsumer": {"IsEnabled": true, "ConsumerConfiguration": {"BatchConfiguration": {"MessagesLimit": 20, "TimeLimitInSec": 2, "ConcurrencyLimit": 1}}}}}}}}}}, "BlobStorage": {"ContainerName": "local-atr-admin"}}