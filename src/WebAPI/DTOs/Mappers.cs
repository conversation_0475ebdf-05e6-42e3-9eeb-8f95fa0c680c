using OneTalent.AtrAdminService.Application.Common.Extensions;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;

namespace OneTalent.AtrAdminService.WebAPI.DTOs;

internal static class Mappers
{
    public static EmployeeShortDto ToEmployeeShortDto(this IEmployee source)
    {
        return source switch
        {
            Employee employee => new EmployeeShortDto(
                employee.EmployeeId,
                employee.CompanyCode,
                employee.CompanyName,
                employee.FullNameEnglish,
                employee.Email,
                employee.JobTitle,
                employee.PositionName
            ),
            EmployeeBase employeeBase => new EmployeeShortDto(
                employeeBase.EmployeeId,
                "-",
                "-",
                "N/A",
                string.Empty, // Empty e-mail is used by UI logic to filter-out unknown users
                "-",
                "-"
            ),
            _ => new EmployeeShortDto(
                "-1",
                "-",
                "-",
                "N/A",
                string.Empty,  // Empty e-mail is used by UI logic to filter-out unknown users
                "-",
                "-"
            ),
        };
    }

    public static EmployeeInfoDto ToEmployeeInfoDto(this Employee source) => new(
        source.EmployeeId,
        source.CompanyCode,
        source.CompanyName,
        source.FullNameEnglish,
        source.Email,
        source.JobTitle,
        source.PositionName,
        source.IsManager ?? false,
        []
       );

    public static ActionDto ToActionDto(this Application.Actions.Models.Action source) => new(
        source.Id,
        source.IsValidation,
        source.Status,
        source.Type,
        source.Initiator.ToEmployeeShortDto(),
        source.StartDate,
        source.EndDate,
        source.InputFileName,
        source.OutputFileName);

    public static FileInfoDto ToFileInfoDto(this AtrAdminFileInfo source) => new(source.Name, source.LengthInBytes);

    public static IEnumerable<PerformanceFormStatusDto> ToStatusesDto(this IEnumerable<PerformanceFormStatus> source) =>
        source.Select(x => new PerformanceFormStatusDto
        {
            Id = x.Id,
            Name = x.Name,
        });

    public static IEnumerable<NamedOptionsDto> ToNamedOptionsDto(this IEnumerable<NamedOptions> source) =>
        source.Select(x => new NamedOptionsDto(x.Id, x.Name));

    public static TDto ToDto<TEnum, TDto>(this TEnum source) where TEnum : Enum
        where TDto : class?, INamedOptionDto<TEnum>?, new()
    {
        var option = source.ToLookupOption();

        return new TDto { Id = option.Id, Name = option.Name };
    }

    public static PerformanceFormHistoryDto ToPerformanceFormHistoryDto(this PerformanceFormHistoryRecord history) =>
       new (
           history.PerformanceFormId,
           history.ActionDateTime,
           history.ActionType,
           history.ActionOwnerId,
           history.ActionOwnerName,
           history.MajorStatusTo,
           history.MinorStatusTo,
           history.MajorStatusFrom,
           history.MinorStatusFrom,
           history.ActionReason,
           history.ActionOwnerJobRole,
           history.GroupCompany,
           history.Directorate,
           history.Function,
           history.Division,
           history.PerformanceCycleName,
           history.AssessmentLineManagerName,
           history.AssessmentB2BManagerName,
           history.DottedLineManagerName,
           history.CurrentLineManager,
           history.ManagerRating,
           history.ManagerRatingName,
           history.ManagerNineBoxPotential,
           history.NineBoxPotentialRating,
           history.NormalizedRating,
           history.NormalizedRatingName,
           history.NormalizedNineBoxPotential,
           history.WorkflowId,
           history.WfAssigneeName);

    public static PerformanceFormDto ToPerformanceFormDto(this AssessmentForm source) =>
      new(
          source.Id,
          source.TemplateId,
          source.TemplateName,
          source.MajorStatus,
          source.MinorStatus,
          source.EmployeeId,
          source.EmployeeFullName,
          source.CompanyName,
          source.DirectorateName,
          source.FunctionName,
          source.DivisionName,
          source.AtrGroupName,
          source.AssessmentLineManagerId,
          source.AssessmentLineManagerName,
          source.AssessmentB2BManagerId,
          source.AssessmentB2BManagerName,
          source.DottedLineManagerId,
          source.DottedLineManagerName,
          source.LastUpdated,
          source.UpdatedBy);
}
