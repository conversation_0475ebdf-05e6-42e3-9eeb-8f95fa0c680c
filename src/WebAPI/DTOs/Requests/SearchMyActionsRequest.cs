using OneTalent.AtrAdminService.Application.Actions.Models.Queries;
using OneTalent.Common.Extensions.Paging;
using OneTalent.WebAPI.Extensions.Common;

namespace OneTalent.AtrAdminService.WebAPI.DTOs.Requests;

internal class SearchMyActionsRequest : IPageRequest
{
    public int? PageNumber { get; set; }

    public int? PageSize { get; set; }

    public SearchActionsQuery ToQuery(OffsetPage offsetPage) => new(offsetPage);
}
