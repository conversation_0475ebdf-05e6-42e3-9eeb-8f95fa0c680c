using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.WebAPI.Extensions.Common;

namespace OneTalent.AtrAdminService.WebAPI.DTOs.Requests;

public class GetPerformanceFormHistoryRequest : IPageRequest
{
    public int? PageNumber { get; set; }

    public int? PageSize { get; set; }

    public PerformanceFormHistoryQuery ToQueryModel(ItemId PerformanceFormId) => new(
        PerformanceFormId,
        PageNumber ?? 1,
        PageSize ?? 0
    );
}
