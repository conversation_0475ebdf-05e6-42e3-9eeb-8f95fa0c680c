using FluentValidation;
using OneTalent.WebAPI.Extensions.Common;

namespace OneTalent.AtrAdminService.WebAPI.DTOs.Requests.Validators;

public class GetPerformanceFormHistoryRequestV1Validator : AbstractValidator<GetPerformanceFormHistoryRequest>
{
    public GetPerformanceFormHistoryRequestV1Validator(
        IValidator<IPageRequest> pageValidator
    )
    {
        Include(pageValidator);
    }
}
