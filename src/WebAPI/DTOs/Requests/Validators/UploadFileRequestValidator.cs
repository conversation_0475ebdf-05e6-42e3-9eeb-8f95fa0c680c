using FluentValidation;
using Microsoft.Extensions.Options;
using OneTalent.BlobStorage.Configuration;

namespace OneTalent.AtrAdminService.WebAPI.DTOs.Requests.Validators;

public class UploadFileRequestValidator : AbstractValidator<UploadFileRequest>
{
    public UploadFileRequestValidator(IOptions<StorageConfig> storageConfig)
    {
        RuleFor(x => x.File.Length)
            .LessThanOrEqualTo(storageConfig.Value.MaxFileSizeBytes)
            .WithMessage("File size exceeds 5MB limit.");

        // TODO: RuleFor(x => x.File.ContentType)
    }
}
