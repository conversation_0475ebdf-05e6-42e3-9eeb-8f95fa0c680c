using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.Extensions;
using OneTalent.AtrAdminService.Infrastructure.Integrations.Common;
using OneTalent.AtrAdminService.Infrastructure.Jobs;
using OneTalent.AtrAdminService.Infrastructure.Messaging.Extensions;
using OneTalent.AtrAdminService.Infrastructure.Storage;
using OneTalent.AtrAdminService.WebAPI.Extensions;
using OneTalent.AtrAdminService.WebAPI.Swagger;
using OneTalent.Common.Extensions.Audit;
using OneTalent.Hangfire.Extensions;
using OneTalent.Sql.Extensions;
using OneTalent.WebAPI.Extensions;
using OneTalent.WebAPI.Extensions.Monitoring;
using Swashbuckle.AspNetCore.SwaggerGen;

await OneTalentWebApplication
    .Create(args)
    .ConfigureServices(builder =>
    {
        builder.ConfigureMonitoring(MonitoringSettings.GlobalLoggingConfig);
        builder.ConfigureInfrastructure();

        builder.Services
            .RegisterSqlExtensions()
            .RegisterApplicationServices(builder.Configuration, builder.Environment)
            .RegisterInfrastructureDataAccessLayerServices(builder.Configuration)
            .RegisterInfrastructureIntegrationsServices(builder.Configuration)
            .RegisterInfrastructureMessagingServices(builder.Configuration, builder.Environment)
            .RegisterInfrastructureJobsServices()
            .RegisterInfrastructureStorageServices(builder.Configuration)
            .ConfigureAudit()
            .RegisterPolicies();

        builder.Services.PostConfigure<SwaggerGenOptions>(o =>
        {
            o.SchemaFilter<HideObsoleteEnumValueSchemaFilter>(); 
        });
    })
    .ConditionallyApplyMigration(app =>
    {
        app.Services.RunDbInitializer(args.Contains("--force-migration"));
    })
    .ConfigureWebApp(app =>
    {
        app.UseHeaderPropagation();
        app.UseHangfire();
        app.UseBlobStorage();
        app.RegisterEndpoints();
    })
    .RunAsync();
