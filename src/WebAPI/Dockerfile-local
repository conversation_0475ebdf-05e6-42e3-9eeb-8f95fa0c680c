#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base

RUN mkdir /app && chown -R app:app /app
USER app
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build

ARG BUILD_CONFIGURATION=Release
ARG VERSION=0.1.0
ARG ADNOC_FEED_TOKEN

COPY ["src/Application/Application.csproj", "src/Application/"]
COPY ["src/Infrastructure.Integrations/Infrastructure.Integrations.csproj", "src/Infrastructure.Integrations/"]
COPY ["src/Infrastructure.DataAccessLayer/Infrastructure.DataAccessLayer.csproj", "src/Infrastructure.DataAccessLayer/"]
COPY ["src/Infrastructure.Messaging/Infrastructure.Messaging.csproj", "src/Infrastructure.Messaging/"]
COPY ["src/Infrastructure.Storage/Infrastructure.Storage.csproj", "src/Infrastructure.Storage/"]
COPY ["src/Infrastructure.Jobs/Infrastructure.Jobs.csproj", "src/Infrastructure.Jobs/"]
COPY ["src/Infrastructure.Storage/Infrastructure.Storage.csproj", "src/Infrastructure.Storage/"]
COPY ["src/Contracts/Contracts.csproj", "src/Contracts/"]
COPY ["src/WebAPI/WebAPI.csproj", "src/WebAPI/"]
COPY ["src/WebAPI.Client/WebAPI.Client.csproj", "src/WebAPI.Client/"]
COPY ["Directory.Build.props", "/"]
COPY ["Directory.Packages.props", "/"]
COPY ["NuGet.config", "/"]

RUN dotnet restore "src/WebAPI/WebAPI.csproj"
COPY . .
WORKDIR "/src/WebAPI"
RUN dotnet build "WebAPI.csproj" -c $BUILD_CONFIGURATION --no-restore /p:Version=$VERSION

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./WebAPI.csproj" -c $BUILD_CONFIGURATION --no-build -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "OneTalent.AtrAdminService.WebAPI.dll"]