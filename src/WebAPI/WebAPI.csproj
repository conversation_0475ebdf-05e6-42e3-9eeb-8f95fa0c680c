<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <AssemblyTitle>OneTalent.AtrAdminService</AssemblyTitle>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <IsPublishable>true</IsPublishable>
    <UserSecretsId>f84c1ffa-a0dd-4c91-b724-fa738e2880a5</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="OneTalent.Sql.Extensions" />
    <PackageReference Include="OneTalent.WebAPI.Extensions" />
    <PackageReference Include="Serilog.Expressions" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application\Application.csproj" />
    <ProjectReference Include="..\Infrastructure.DataAccessLayer\Infrastructure.DataAccessLayer.csproj" />
    <ProjectReference Include="..\Infrastructure.Jobs\Infrastructure.Jobs.csproj" />
    <ProjectReference Include="..\Infrastructure.Integrations\Infrastructure.Integrations.csproj" />
    <ProjectReference Include="..\Infrastructure.Messaging\Infrastructure.Messaging.csproj" />
    <ProjectReference Include="..\Infrastructure.Storage\Infrastructure.Storage.csproj" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="$(AssemblyName).IntegrationTests" />
    <InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Templates\ATR Forms Launch.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
