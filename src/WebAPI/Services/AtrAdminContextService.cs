using OneTalent.AtrAdminService.Application.Access.Queries.Services;
using OneTalent.AtrAdminService.Application.Common.Constants;
using OneTalent.Common.Extensions.ItemId;

namespace OneTalent.AtrAdminService.WebAPI.Services;

internal sealed class AtrAdminContextService(IHttpContextAccessor httpContextAccessor) : IAtrAdminContextService
{
    public ItemId? Id
    {
        get
        {
            var id = httpContextAccessor
                .HttpContext?
                .Request
                .RouteValues
                .GetValueOrDefault("id")?.ToString();

            return id is null ? null : new ItemId(id);
        }
    }

    public ItemId? SubordinateId
    {
        get
        {
            var id = httpContextAccessor
                .HttpContext?
                .Request
                .RouteValues
                .GetValueOrDefault("subordinateId")?
                .ToString();

            return id is null ? null : new ItemId(id);
        }
    }

    public string? ClientId => httpContextAccessor
        .HttpContext?
        .Request
            .Headers[Headers.ClientId]
            .FirstOrDefault();
}
