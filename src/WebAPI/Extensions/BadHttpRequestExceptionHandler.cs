using Microsoft.AspNetCore.Mvc;
using OneTalent.WebAPI.Extensions.Exceptions;

namespace OneTalent.AtrAdminService.WebAPI.Extensions;

internal sealed class BadHttpRequestExceptionHandler : ICustomExceptionHandler
{
    public Type[] SupportedExceptions => [typeof(BadHttpRequestException)];

    public ProblemDetails Handle(Exception exception, HttpContext context)
    {
        var exactException = (BadHttpRequestException)exception;

        return new ProblemDetails
        {
            Status = exactException.StatusCode,
            Title = "Bad Request.",
            Detail = exactException.Message,
            Extensions = { { "exceptionType", exception.GetType().AssemblyQualifiedName } }
        };
    }
}
