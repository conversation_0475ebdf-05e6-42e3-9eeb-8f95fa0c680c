using OneTalent.AtrAdminService.Application.Common.Constants;
using OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Actions;
using OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Files;
using OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;
using OneTalent.AtrAdminService.WebAPI.Endpoints.Employees;
using OneTalent.WebAPI.Extensions;
using OneTalent.WebAPI.Extensions.Jobs.Endpoints;

namespace OneTalent.AtrAdminService.WebAPI.Extensions;

internal static class EndpointExtensions
{
    internal static WebApplication RegisterEndpoints(this WebApplication app)
    {
        var versionSet = ApiVersions.PublicVersionSet;

        app.RegisterAdminActionEndpoints(versionSet);
        app.RegisterAdminFileEndpoints(versionSet);
        app.RegisterEmployeeQueryEndpoints(versionSet);
        app.RegisterPerformanceFormsQueryEndpoints(versionSet);

        if (!app.Environment.IsEnvironment(EnvironmentNames.ProductionEnvironmentName))
        {
            app.RegisterJobCommandEndpoints(ApiVersions.InternalVersionSet);
        }

        return app;
    }

    public static TBuilder AddCacheControl<TBuilder>(this TBuilder builder, string cacheControl)
        where TBuilder : IEndpointConventionBuilder => builder.AddEndpointFilter(CacheAsync(cacheControl));

    private static Func<EndpointFilterInvocationContext, EndpointFilterDelegate, ValueTask<object?>> CacheAsync(string cacheControl)
    {
        return async (context, next) =>
        {
            var result = await next(context);

            return result switch
            {
                IStatusCodeHttpResult { StatusCode: >= 200 and < 300 } => SetCacheControl(result, context),
                _ => result
            };
        };

        object? SetCacheControl(object? result, EndpointFilterInvocationContext context)
        {
            if (context.HttpContext.Response.HasStarted)
            {
                return result;
            }

            context.HttpContext.Response.Headers["Cache-Control"] = cacheControl;
            context.HttpContext.Response.Headers["Vary"] = cacheControl.Contains("private")
                ? "Accept, Accept-Encoding, Accept-Language, Authorization, X-Impersonation-User"
                : "Accept, Accept-Encoding, Accept-Language";

            return result;
        }
    }
}
