using OneTalent.WebAPI.Extensions.Monitoring;

namespace OneTalent.AtrAdminService.WebAPI.Extensions;

public class MonitoringSettings
{
    const string HangfireDashboardPath = "/dashboard";

    public static GlobalLoggingConfig GlobalLoggingConfig =>
        new()
        {
            TracingWebFilter = FilterWebTracing,
            LogRequestFilters = [HangfireDashboardPath]
        };

    private static bool FilterWebTracing(HttpContext context)
        => !context.Request.Path.StartsWithSegments(HangfireDashboardPath, StringComparison.OrdinalIgnoreCase);
}
