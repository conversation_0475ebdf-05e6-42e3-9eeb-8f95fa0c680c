using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.FeatureManagement;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;
using OneTalent.AtrAdminService.Application.Access.Queries.Services;
using OneTalent.AtrAdminService.Application.Common.Constants;
using OneTalent.AtrAdminService.Application.Common.Extensions;
using OneTalent.AtrAdminService.WebAPI.Authorization;
using OneTalent.AtrAdminService.WebAPI.Authorization.Settings;
using OneTalent.AtrAdminService.WebAPI.Services;
using OneTalent.Azure.Extensions;
using OneTalent.Common.Extensions.Configuration;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.DataSyncService.WebAPI.Client;
using OneTalent.WebAPI.Extensions.Exceptions;
using OneTalent.WebAPI.Extensions.Swagger;
using Constants = OneTalent.AtrAdminService.WebAPI.Authorization.Constants;

namespace OneTalent.AtrAdminService.WebAPI.Extensions;

public static class ServiceCollectionExtensions
{
    public static void ConfigureInfrastructure(
        this WebApplicationBuilder builder)
    {
        builder.Services.AddSingleton<ICustomExceptionHandler, BadHttpRequestExceptionHandler>();

        SwaggerExtensions.AddCustomMapping<ItemId>(new OpenApiSchema { Type = "string", Format = "int64" });

        builder.Services.AddHeaderPropagation(o =>
        {
            o.Headers.Add(HeaderNames.CacheControl);
        });
    }

    public static IServiceCollection RegisterApplicationServices(this IServiceCollection services, IConfiguration configuration,
        IHostEnvironment environment)
    {
        services.AddAzureExtensions();
        services.AddValidatorsFromAssemblyContaining<Program>(includeInternalTypes: true);
        services.RegisterDataSyncClientServices();

        services.RegisterApplicationServices(configuration);

        if (environment.IsEnvironment(EnvironmentNames.ProductionEnvironmentName))
        {
            services.AddSingleton<IFeatureDefinitionProvider, ConfigurationFeatureDefinitionProvider>();
        }

        services.AddScoped<IAtrAdminContextService, AtrAdminContextService>();

        return services;
    }

    public static void RegisterPolicies(this IServiceCollection services)
    {
        services.RegisterConfiguration<SuperAdminConfig>();

        services.AddAuthorization(options =>
        {
            options.AddPolicy(Constants.SuperAdminOnlyPolicy, policy =>
                policy.Requirements.Add(new MustBeSuperAdminRequirement()));
            options.AddPolicy(Constants.AclPolicy, policy =>
                policy.Requirements.Add(new AccessControlRequirement()));
        });

        services.AddScoped<IAuthorizationHandler, MustBeSuperAdminHandler>();
        services.AddScoped<IAuthorizationHandler, AccessControlHandler>();
    }
}
