namespace OneTalent.AtrAdminService.WebAPI.Extensions;

public static partial class LoggerExtensions
{
    private static class Events
    {
        public const int UnexpectedExceptionId = 8001;
        public const int PendingMigrationsId = 8002;
        public const int DatabaseCleanupId = 8003;
    }

    [LoggerMessage(
        EventId =  Events.PendingMigrationsId,
        EventName = nameof(Events.PendingMigrationsId),
        Message = "Pending migrations count: {PendingMigrations}. Applied Migrations: {AppliedMigrations}",
        Level = LogLevel.Information)]
    public static partial void PendingMigrations(
        this ILogger logger,
        int pendingMigrations,
        int appliedMigrations);

    [LoggerMessage(
        EventId =  Events.DatabaseCleanupId,
        EventName = nameof(Events.DatabaseCleanupId),
        Message = "Starting database cleanup...",
        Level = LogLevel.Information)]
    public static partial void DatabaseCleanup(
        this ILogger logger);

    [LoggerMessage(
        EventId = Events.UnexpectedExceptionId,
        EventName = nameof(Events.UnexpectedExceptionId),
        Message = "Unexpected behaviour.",
        Level = LogLevel.Information)]
    public static partial void UnexpectedException(
        this ILogger logger, Exception exception);
}
