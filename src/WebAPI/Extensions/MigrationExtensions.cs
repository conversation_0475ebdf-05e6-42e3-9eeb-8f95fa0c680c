using Microsoft.EntityFrameworkCore;
using OneTalent.AtrAdminService.Application.Common.Constants;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer;
using OneTalent.AtrAdminService.Infrastructure.DataAccessLayer.SeedData;
using OneTalent.Common.Extensions.Diagnostics;

namespace OneTalent.AtrAdminService.WebAPI.Extensions;

public static class MigrationExtensions
{
    public static void RunDbInitializer(this IServiceProvider serviceProvider, bool forceMigration)
    {
        using var scope = serviceProvider.CreateScope();

        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger("DbMigration");

        RunDbInitializerInternal(scope.ServiceProvider, logger, forceMigration);
    }

    private static void RunDbInitializerInternal(IServiceProvider serviceProvider, ILogger logger, bool forceMigration)
    {
        var environment = serviceProvider.GetRequiredService<IHostEnvironment>();
        var dbContext = serviceProvider.GetRequiredService<AtrAdminDbContext>();

        var performCleanup = forceMigration
                             || environment.IsDevelopment()
                             || environment.IsEnvironment(EnvironmentNames.IntegrationTestEnvironmentName)
                             || environment.IsEnvironment(EnvironmentNames.IntegrationReadScenariosEnvironmentName)
                             || environment.IsEnvironment(EnvironmentNames.IntegrationWriteScenariosEnvironmentName);

        using var activityPerformance = Activities.AppActivitySource.StartActivity("DbMigration");

        var pendingMigrations = dbContext.Database.GetPendingMigrations().ToArray();
        var appliedMigrations = dbContext.Database.GetAppliedMigrations().ToArray();

        logger.PendingMigrations(pendingMigrations.Length, appliedMigrations.Length);

        if (pendingMigrations.Length > 0)
        {
            if (performCleanup)
            {
                logger.DatabaseCleanup();
                CleanDb(dbContext);
            }

            dbContext.Database.Migrate();

            if (appliedMigrations.Length == 0 || performCleanup)
            {
                serviceProvider.GetRequiredService<ISeedDataService>().SeedData(dbContext, environment);
            }
        }
    }

    private static void CleanDb(AtrAdminDbContext client)
    {
        var done = false;

        while (!done)
        {
            try
            {
                client.Database.ExecuteSqlRaw("""
                                              DECLARE @sql NVARCHAR(max)='';

                                              -- Step 1: Drop all DB foreign key constraints
                                              SELECT @sql += 'ALTER TABLE ' + QUOTENAME(TABLE_SCHEMA) + '.' + QUOTENAME(TABLE_NAME) + 
                                                             ' DROP CONSTRAINT ' + QUOTENAME(CONSTRAINT_NAME) + ';'
                                              FROM   INFORMATION_SCHEMA.TABLE_CONSTRAINTS
                                              WHERE  CONSTRAINT_TYPE = 'FOREIGN KEY'
                                                AND  CONSTRAINT_SCHEMA IN (
                                                'ATR_ADMIN', 'ATR_ADMIN_Mig', 'ATR_ADMIN_Outbox'
                                                );

                                              -- Step 2: Drop all DB tables and views

                                              SELECT @sql += 'DROP TABLE ' + QUOTENAME(TABLE_SCHEMA) + '.' + QUOTENAME(TABLE_NAME) + ';'
                                              FROM   INFORMATION_SCHEMA.TABLES
                                              WHERE  TABLE_TYPE = 'BASE TABLE'
                                                AND  TABLE_SCHEMA IN (
                                                'ATR_ADMIN', 'ATR_ADMIN_Mig', 'ATR_ADMIN_Outbox'
                                                );

                                              EXEC sp_executesql @sql;
                                              """);
            }
            catch (Exception)
            {
                continue;
            }

            done = true;
        }
    }
}
