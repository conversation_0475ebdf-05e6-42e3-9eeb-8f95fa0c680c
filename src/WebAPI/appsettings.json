{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information"
    },
    "Filter": [
      {
        "Name": "ByExcluding",
        "Args": {
          "expression": "Suppressed"
        }
      }
    ]
  },
  "AllowedHosts": "*",
  "PagingOptions": {
    "DefaultPageSize": 10,
    "MaxPageSize": 500
  },
  "ConnectionStrings": {
    "OpenTelemetryHost": "",
    "AtrAdminDb": "",
    "AzureAppConfiguration": "",
    "RedisCaching": "",
    "MasstransitDb": "",
    "BlobStorage": ""
  },
  "ApplicationInsights": {
    "ConnectionString": ""
  },
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "",
    "ClientId": "",
    //"Audience": "",
    "ClientSecret": "",
    "AllowWebApiToBeAuthorizedByACL": true
  },
  "Seq": {
    "Enabled": false,
    "Host": "",
    "ApiKey": ""
  },
  "Caching": {
    "ExpirationInMinutes": 5,
    "LocalCacheExpirationInMinutes": 5,
    "MaximumKeyLength": 1024,
    "Prefix": "",
    "Namespace": ""
  },
  "Audit": {
    "Enabled": false,
    "UseSeqDataProvider": false
  },
  "Swagger": {
    "Enabled": false,
    "AuthEnabled": true
  },
  "DataSyncServiceClient": {
    "BaseUrl": "",
    "Scope": ""
  },
  "AtrServiceClient": {
    "BaseUrl": "",
    "Scope": ""
  },
  "SuperAdmins": {
    "Emails": []
  },
  "UserMapping": {
    "Enabled": false,
    "BaseUrl": "",
    "TargetScope": ""
  },
  "HttpLogging": {
    "Disabled": false,
    "AllowedHeaders": [
      "sec-ch-ua",
      "sec-ch-ua-platform",
      "sec-ch-ua-mobile",
      "Sec-Fetch-Site",
      "Sec-Fetch-Mode",
      "Sec-Fetch-Dest"
    ]
  },
  "FeatureManagement": {
  },
  "MassTransit": {
    "ServiceBusConfiguration": {
      "IsEnabled": false,
      "UseInMemory": false,
      "ConnectionString": "mock",
      "EntityNamingConfiguration": {
        "QueueNamePrefix": "mock",
        "TopicNamePrefix": "mock",
        "SubscriptionNamePrefix": "mock"
      },
      "ConsumersConfiguration": {
        "ConcurrentMessageLimit": 20,
        "RetryConfiguration": {
          "MessageRetryCount": 2,
          "MessageRetryDelayInMs": 1000
        }
      },
      "Topics": {
        "AtrCycleTopic": {
          "IsEnabled": false,
          "Subscriptions": {
            "AtrCycleChangedSubscription": {
              "IsEnabled": false,
              "Consumers": {
                "AtrCycleUpdatedConsumer": {
                  "IsEnabled": true,
                  "ConsumerConfiguration": {
                    "BatchConfiguration": {
                      "MessagesLimit": 20,
                      "TimeLimitInSec": 2,
                      "ConcurrencyLimit": 1
                    }
                  }
                }
              }
            }
          }
        },
        "EmployeeTopic": {
          "IsEnabled": false,
          "Subscriptions": {
            "EmployeeChangedSubscription": {
              "IsEnabled": false,
              "Consumers": {
                "EmployeeChangedConsumer": {
                  "IsEnabled": true,
                  "ConsumerConfiguration": {
                    "BatchConfiguration": {
                      "MessagesLimit": 20,
                      "TimeLimitInSec": 2,
                      "ConcurrencyLimit": 1
                    }
                  }
                }
              }
            }
          }
        },
        "FeedbackTopic": {
          "IsEnabled": false,
          "Subscriptions": {
            "FeedbackChangedSubscription": {
              "IsEnabled": false,
              "Consumers": {
                "FeedbackChangedConsumer": {
                  "IsEnabled": true,
                  "ConsumerConfiguration": {
                    "BatchConfiguration": {
                      "MessagesLimit": 20,
                      "TimeLimitInSec": 2,
                      "ConcurrencyLimit": 1
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  "Hangfire": {
    "Dashboard": {
      "Enabled": false,
      "Login": "login",
      "Password": "password"
    },
    "BackgroundJobServerName": "AtrAdminServiceHangfire",
    "InMemoryStorage": true,
    "HangfireScheme": "ATR_ADMIN_Hangfire",
    "DbConnectionStringName": "AtrAdminDb"
  },
  "BlobStorage": {
    "AllowedFileFormats": [
      {
        "Extension": ".csv",
        "SkipSignatureCheck": true
      }
    ],
    "MaxFileSizeBytes": 5242880
  }
}
