{"profiles": {"https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger/index.html", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7027;http://localhost:5027"}, "migrate": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger/index.html", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "commandLineArgs": "--migrate-only", "dotnetRunMessages": true, "applicationUrl": "https://localhost:7027;http://localhost:5027"}, "http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger/index.html", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5027"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "environmentVariables": {"ASPNETCORE_HTTPS_PORTS": "8081", "ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true, "useSSL": true}}, "$schema": "http://json.schemastore.org/launchsettings.json"}