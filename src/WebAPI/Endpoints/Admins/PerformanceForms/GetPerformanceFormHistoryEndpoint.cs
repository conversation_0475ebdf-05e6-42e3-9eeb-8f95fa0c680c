using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.AtrAdminService.WebAPI.DTOs.Requests;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.Common.Extensions.Paging;
using System.ComponentModel.DataAnnotations;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public static class GetPerformanceFormHistoryEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromRoute][Required] ItemId performanceFormId,
        [FromBody] GetPerformanceFormHistoryRequest request,
        IAtrAdminQueryService atrAdminQueryService,
        IValidator<GetPerformanceFormHistoryRequest> validator,
        CancellationToken cancellationToken)
    {
        var requestModel = request ?? new GetPerformanceFormHistoryRequest();

        await validator.ValidateAndThrowAsync(requestModel, cancellationToken);

        var history = await atrAdminQueryService.GetPerformanceFormHistoryAsync(
            requestModel.ToQueryModel(performanceFormId), 
            cancellationToken);

        return Results.Ok(
          new PagedListResult<PerformanceFormHistoryDto>(
              history.Paging,
              history.Items.Select(Mappers.ToPerformanceFormHistoryDto)));
    }
}
