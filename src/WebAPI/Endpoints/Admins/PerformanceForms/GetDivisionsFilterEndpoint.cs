using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public class GetDivisionsFilterEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromQuery] string? search,
        IEmployeeQueryService employeeQueryService,
        CancellationToken cancellationToken)
    {
        var divisions = await employeeQueryService.GetFilteredDivisionsAsync(search, cancellationToken);

        return divisions is not null ?
           Results.Ok(divisions.ToNamedOptionsDto()) :
           Results.Ok(Array.Empty<NamedOptionsDto>);
    }
}
