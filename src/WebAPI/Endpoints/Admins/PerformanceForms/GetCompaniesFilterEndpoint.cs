using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public class GetCompaniesFilterEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromQuery] string? search,
        IEmployeeQueryService employeeQueryService,
        CancellationToken cancellationToken)
    {
        var companies = await employeeQueryService.GetFilteredCompaniesAsync(search, cancellationToken);
        
        return companies is not null ?
           Results.Ok(companies.ToNamedOptionsDto()) :
           Results.Ok(Array.Empty<NamedOptionsDto>());
    }
}

