using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public class GetDirectoratesFilterEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromQuery] string? search,
        IEmployeeQueryService employeeQueryService,
        CancellationToken cancellationToken)
    {
        var directorates = await employeeQueryService.GetFilteredDirectoratesAsync(search, cancellationToken);

        return directorates is not null ?
           Results.Ok(directorates.ToNamedOptionsDto()) :
           Results.Ok(Array.Empty<NamedOptions>());
    }
}
