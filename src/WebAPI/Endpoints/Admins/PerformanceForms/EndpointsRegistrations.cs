using Asp.Versioning.Builder;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.Common.Extensions.Paging;
using OneTalent.WebAPI.Extensions;
using OneTalent.WebAPI.Extensions.Authorization;
using System.Net.Mime;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public static class EndpointsRegistrations
{
    public static void RegisterPerformanceFormsQueryEndpoints(this WebApplication app, ApiVersionSet versionSet)
    {
        var group = app.MapGroup($"{ApiVersions.ApiPrefix}/performance-forms")
            .WithTags("Admin - Performance Forms - Queries")
            .WithApiVersionSet(versionSet)
            .RequireAuthorization(AuthPolicies.ValidUser);

        group
            .MapGet("statuses", GetPerformanceFormStatusesEndpoint.ExecuteAsync)
            .Produces<PerformanceFormStatusDto[]>(contentType: MediaTypeNames.Application.Json)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .MapToApiVersion(ApiVersions.V1);

        group
           .MapPost("search", SearchPerformanceFormsEndpoint.ExecuteAsync)
           .Produces<PagedListResult<PerformanceFormDto>>(contentType: MediaTypeNames.Application.Json)
           .ProducesProblem(StatusCodes.Status404NotFound)
           .MapToApiVersion(ApiVersions.V1);

        group
           .MapGet("companies", GetCompaniesFilterEndpoint.ExecuteAsync)
           .Produces<PagedListResult<NamedOptionsDto>>(contentType: MediaTypeNames.Application.Json)
           .ProducesProblem(StatusCodes.Status404NotFound)
           .MapToApiVersion(ApiVersions.V1);

        group
          .MapGet("divisions", GetDivisionsFilterEndpoint.ExecuteAsync)
          .Produces<PagedListResult<NamedOptionsDto>>(contentType: MediaTypeNames.Application.Json)
          .ProducesProblem(StatusCodes.Status404NotFound)
          .MapToApiVersion(ApiVersions.V1);

        group
          .MapGet("directorates", GetDirectoratesFilterEndpoint.ExecuteAsync)
          .Produces<PagedListResult<NamedOptionsDto>>(contentType: MediaTypeNames.Application.Json)
          .ProducesProblem(StatusCodes.Status404NotFound)
          .MapToApiVersion(ApiVersions.V1);

        group
         .MapGet("functions", GetFunctionsFilterEndpoint.ExecuteAsync)
         .Produces<PagedListResult<NamedOptionsDto>>(contentType: MediaTypeNames.Application.Json)
         .ProducesProblem(StatusCodes.Status404NotFound)
         .MapToApiVersion(ApiVersions.V1);

        group
           .MapPost(
               "{performanceFormId}/history",
               GetPerformanceFormHistoryEndpoint.ExecuteAsync)
           .Produces<PagedListResult<PerformanceFormHistoryDto>>(contentType: MediaTypeNames.Application.Json)
           .ProducesProblem(StatusCodes.Status401Unauthorized)
           .MapToApiVersion(ApiVersions.V1);
    }
}
