using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.Common.Extensions.Paging;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public class SearchPerformanceFormsEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        PerformanceFormSearchQuery searchQuery,
        IEmployeeQueryService employeeQueryService,
        IAtrAdminQueryService atrAdminQueryService,
        CancellationToken cancellationToken)
    {
        var currentEmployee = await employeeQueryService.GetCurrentUserEmployeeAsync(cancellationToken);
        var forms = await atrAdminQueryService.SearchPerformanceFormsAsync(currentEmployee.EmployeeId, searchQuery, cancellationToken);

        return Results.Ok(new PagedListResult<PerformanceFormDto>(forms.Paging, forms.Items.Select(x => x.ToPerformanceFormDto())));
    }
}
