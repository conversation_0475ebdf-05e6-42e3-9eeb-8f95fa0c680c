using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public static class GetPerformanceFormStatusesEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        IAtrAdminQueryService atrAdminQueryService,
        CancellationToken cancellationToken)
    {
        var statuses = await atrAdminQueryService.GetPerformanceFormStatusesAsync(cancellationToken);

        return Results.Ok(statuses.ToStatusesDto());
    }
}
