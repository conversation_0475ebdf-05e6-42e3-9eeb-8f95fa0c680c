using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public class GetFunctionsFilterEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromQuery] string? search,
        IEmployeeQueryService employeeQueryService,
        CancellationToken cancellationToken)
    {
        var functions = await employeeQueryService.GetFilteredFunctionsAsync(search, cancellationToken);

        return functions is not null ?
           Results.Ok(functions.ToNamedOptionsDto()) :
           Results.Ok(Array.Empty<NamedOptions>());
    }
}
