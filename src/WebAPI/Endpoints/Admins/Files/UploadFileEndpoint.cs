using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.AtrAdmin.Models.Commands;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs.Requests;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Files;

internal static class UploadFileEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromForm] UploadFileRequest request,
        IAtrAdminCommandService atrAdminCommandService,
        IValidator<UploadFileRequest> validator,
        CancellationToken cancellationToken)
    {
        await validator.ValidateAndThrowAsync(request, cancellationToken);

        await atrAdminCommandService.UploadMyFileAsync(
            new UploadFileCommand(request.File.FileName, request.FileActionType, request.File.OpenReadStream()),
            cancellationToken);

        return Results.Ok();
    }
}
