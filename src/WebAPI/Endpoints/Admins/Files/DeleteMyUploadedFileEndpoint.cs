using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Files;

internal static class DeleteMyUploadedFileEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromRoute] FileActionType fileActionType,
        IAtrAdminCommandService atrAdminCommandService,
        CancellationToken cancellationToken)
    {
        await atrAdminCommandService.DeleteMyUploadedFileAsync(fileActionType, cancellationToken);

        return Results.NoContent();
    }
}
