using Asp.Versioning.Builder;
using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.WebAPI.Extensions;
using OneTalent.WebAPI.Extensions.Authorization;
using System.Net.Mime;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Files;

internal static class EndpointsRegistrations
{
    public static WebApplication RegisterAdminFileEndpoints(this WebApplication app, ApiVersionSet versionSet)
    {
        var queries = app
            .MapGroup(EndpointPaths.AdminsEndpointPrefixPath)
            .WithTags("Admin - Files - Queries")
            .WithApiVersionSet(versionSet)
            .RequireAuthorization(AuthPolicies.ValidUser);

        queries
            .MapGet("me/templates/{fileActionType}/download", DownloadMyTemplateFileEndpoint.ExecuteAsync)
            .Produces<FileResult>(contentType: MediaTypeNames.Application.Octet)
            .Produces(StatusCodes.Status404NotFound)
            .MapToApiVersion(ApiVersions.V1);

        queries
            .MapGet($"{EndpointPaths.Files.MyFilesPath}/{{fileActionType}}", GetMyUploadedFileInfoEndpoint.ExecuteAsync)
            .Produces<FileInfoDto>()
            .Produces(StatusCodes.Status404NotFound)
            .MapToApiVersion(ApiVersions.V1);

        queries.MapGet($"{EndpointPaths.Files.MyFilesPath}/{{fileActionType}}/download",
                DownloadMyUploadedFileEndpoint.ExecuteAsync)
            .Produces<FileResult>(contentType: MediaTypeNames.Application.Octet)
            .Produces(StatusCodes.Status404NotFound)
            .MapToApiVersion(ApiVersions.V1);

        var commands = app
            .MapGroup(EndpointPaths.AdminsEndpointPrefixPath)
            .WithTags("Admin - Files - Commands")
            .WithApiVersionSet(versionSet)
            .RequireAuthorization(AuthPolicies.ValidUser);

        commands
            .MapPost(EndpointPaths.Files.MyFilesPath, UploadFileEndpoint.ExecuteAsync)
            .Produces(StatusCodes.Status200OK)
            .DisableAntiforgery()
            .MapToApiVersion(ApiVersions.V1);

        commands
            .MapDelete($"{EndpointPaths.Files.MyFilesPath}/{{fileActionType}}", DeleteMyUploadedFileEndpoint.ExecuteAsync)
            .Produces(StatusCodes.Status204NoContent)
            .Produces(StatusCodes.Status404NotFound)
            .MapToApiVersion(ApiVersions.V1);

        return app;
    }
}
