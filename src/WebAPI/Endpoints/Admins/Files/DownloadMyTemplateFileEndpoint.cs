using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using System.Net.Mime;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Files;

internal static class DownloadMyTemplateFileEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromRoute] FileActionType fileActionType,
        IAtrAdminQueryService atrAdminQueryService,
        CancellationToken cancellationToken)
    {
        string? fileName;
        switch (fileActionType)
        {
            case FileActionType.AtrFormsLaunch:
                fileName = "ATR Forms Launch.csv";
                break;
            case FileActionType.BulkTransition:
            case FileActionType.BulkReAssign:
                return Results.NotFound();
            default:
                throw new ArgumentOutOfRangeException(nameof(fileActionType), fileActionType, null);
        }

        var path = Path.Combine(AppContext.BaseDirectory, $"Templates/{fileName}");

        if (!File.Exists(path))
        {
            return Results.NotFound();
        }

        var fileStream = new FileStream(path, FileMode.Open);

        return Results.File(fileStream, MediaTypeNames.Application.Octet, fileName);
    }
}
