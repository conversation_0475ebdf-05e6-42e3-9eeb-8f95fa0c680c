using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using System.Net.Mime;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Files;

internal static class DownloadMyUploadedFileEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromRoute] FileActionType fileActionType,
        IAtrAdminQueryService atrAdminQueryService,
        CancellationToken cancellationToken)
    {
        var file = await atrAdminQueryService.DownloadMyUploadedFileAsync(fileActionType, cancellationToken);

        return file is null
            ? Results.NotFound()
            : Results.File(file.FileStream, MediaTypeNames.Application.Octet, file.FileName);
    }
}
