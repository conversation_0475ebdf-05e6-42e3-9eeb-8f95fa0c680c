using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Files;

internal static class GetMyUploadedFileInfoEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromRoute] FileActionType fileActionType,
        IAtrAdminQueryService atrAdminQueryService,
        CancellationToken cancellationToken)
    {
        var fileInfo = await atrAdminQueryService.GetMyUploadedFileInfoAsync(fileActionType, cancellationToken);

        return fileInfo is null ? Results.NotFound() : Results.Ok(fileInfo.ToFileInfoDto());
    }
}
