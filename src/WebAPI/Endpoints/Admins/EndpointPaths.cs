using OneTalent.WebAPI.Extensions;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins;

public class EndpointPaths
{
    public static readonly string AdminsEndpointPrefixPath = $"{ApiVersions.ApiPrefix}/admins";

    public class Actions
    {
        public const string MyActionsPath = "me/actions";
        public static readonly string GetActionStatusEndpointPath = AdminsEndpointPrefixPath + "/" + MyActionsPath;
    }

    public class Files
    {
        public const string MyFilesPath = "me/files";
    }
}
