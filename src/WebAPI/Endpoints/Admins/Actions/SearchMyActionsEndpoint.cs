using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.AtrAdminService.WebAPI.DTOs.Requests;
using OneTalent.Common.Extensions.Paging;
using OneTalent.WebAPI.Extensions.Common;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Actions;

internal static class SearchMyActionsEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromBody] SearchMyActionsRequest? request,
        IAtrAdminQueryService atrAdminQueryService,
        IPagingProvider pagingProvider,
        IValidator<IPageRequest> validator,
        CancellationToken cancellationToken)
    {
        // TODO: Shared code for validation and ParseOffsetPage (IPageRequests.ToQuery())?
        request ??= new SearchMyActionsRequest();

        await validator.ValidateAndThrowAsync(
            request,
            cancellationToken);

        var offsetPage = pagingProvider.ParseOffsetPage(
            new PagingRequest(
                request.PageNumber ?? Common.Constants.DefaultPageNumber,
                request.PageSize ?? 0));

        var actionLogs = await atrAdminQueryService.SearchMyActionsAsync(
            request.ToQuery(offsetPage),
            cancellationToken);

        return Results.Ok(new PagedListResult<ActionDto>(
            actionLogs.Paging,
            actionLogs.Items.Select(x => x.ToActionDto())));
    }
}
