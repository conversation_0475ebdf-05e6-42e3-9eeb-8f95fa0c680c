using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.Common.Extensions.ItemId;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Actions;

internal static class GetActionStatusEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromRoute] ItemId actionId,
        IAtrAdminQueryService atrAdminQueryService,
        CancellationToken cancellationToken)
    {
        var status = await atrAdminQueryService.GetMyActionStatusAsync(
            actionId,
            cancellationToken);

        return status.HasValue ?
            Results.Ok(status.Value.ToDto<ActionStatus, ActionStatusDto>()) :
            Results.NotFound();
    }
}
