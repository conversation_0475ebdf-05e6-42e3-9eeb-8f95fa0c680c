using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.Actions.Services;
using OneTalent.Common.Extensions.ItemId;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Actions;

internal static class UpdateActionEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromRoute] ItemId actionId,
        IActionCommandService actionCommandService,
        CancellationToken cancellationToken)
    {
        await actionCommandService.CancelActionAsync(actionId, cancellationToken);

        return Results.Accepted();
    }
}
