using Asp.Versioning.Builder;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.Common.Extensions.Paging;
using OneTalent.WebAPI.Extensions;
using OneTalent.WebAPI.Extensions.Authorization;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Actions;

internal static class EndpointsRegistrations
{
    public static WebApplication RegisterAdminActionEndpoints(this WebApplication app, ApiVersionSet versionSet)
    {
        var queries = app
            .MapGroup(EndpointPaths.AdminsEndpointPrefixPath)
            .WithTags("Admin - Actions - Queries")
            .WithApiVersionSet(versionSet)
            .RequireAuthorization(AuthPolicies.ValidUser);

        queries
            .MapPost("me/actions/search", SearchMyActionsEndpoint.ExecuteAsync)
            .Produces<PagedListResult<ActionDto>>()
            .MapToApiVersion(ApiVersions.V1);

        queries
            .MapGet($"{EndpointPaths.Actions.MyActionsPath}/{{actionId}}", GetActionStatusEndpoint.ExecuteAsync)
            .Produces<ActionStatusDto>()
            .Produces(StatusCodes.Status404NotFound)
            .MapToApiVersion(ApiVersions.V1);

        var commands = app
            .MapGroup(EndpointPaths.AdminsEndpointPrefixPath)
            .WithTags("Admin - Actions - Commands")
            .WithApiVersionSet(versionSet)
            .RequireAuthorization(AuthPolicies.ValidUser);

        commands
            .MapPost(EndpointPaths.Actions.MyActionsPath, ScheduleActionEndpoint.ExecuteAsync)
            .Produces(StatusCodes.Status202Accepted)
            .MapToApiVersion(ApiVersions.V1);

        commands
            .MapPatch($"{EndpointPaths.Actions.MyActionsPath}/{{actionId}}", UpdateActionEndpoint.ExecuteAsync)
            .Produces(StatusCodes.Status202Accepted)
            .Produces(StatusCodes.Status404NotFound)
            .MapToApiVersion(ApiVersions.V1);

        return app;
    }
}
