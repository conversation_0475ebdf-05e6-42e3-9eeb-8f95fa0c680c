using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.AtrAdminService.WebAPI.DTOs.Requests;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.Actions;

internal static class ScheduleActionEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromBody] ScheduleActionRequest request,
        IAtrAdminCommandService atrAdminCommandService,
        CancellationToken cancellationToken)
    {
        var actionId = await atrAdminCommandService.ScheduleActionAsync(request.ToCommand(), cancellationToken);

        return Results.Accepted(value: new ActionShortDto(actionId));
    }
}
