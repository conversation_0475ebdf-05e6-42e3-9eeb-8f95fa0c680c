using Asp.Versioning.Builder;
using OneTalent.AtrAdminService.WebAPI.Common;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.AtrAdminService.WebAPI.Extensions;
using OneTalent.WebAPI.Extensions;
using OneTalent.WebAPI.Extensions.Authorization;
using System.Net.Mime;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Employees;

public static class EndpointsRegistrations
{
    public static void RegisterEmployeeQueryEndpoints(this WebApplication app, ApiVersionSet versionSet)
    {
        var group = app.MapGroup($"{ApiVersions.ApiPrefix}/employees")
            .WithTags("Employees - Queries")
            .WithApiVersionSet(versionSet)
            .RequireAuthorization(AuthPolicies.ValidUser);

        group
            .MapGet("me/employee-info", GetMyEmployeeInfoEndpoint.ExecuteAsync)
            .AddCacheControl(CacheControlConstants.PrivateDefault)
            .Produces<EmployeeInfoDto>(contentType: MediaTypeNames.Application.Json)
            .MapToApiVersion(ApiVersions.V1);
    }
}
