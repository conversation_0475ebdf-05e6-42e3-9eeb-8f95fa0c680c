using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Employees;

public static class GetMyEmployeeInfoEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        IEmployeeQueryService employeeQueryService,
        CancellationToken cancellationToken)
    {
        var currentEmployee = await employeeQueryService.GetCurrentUserEmployeeAsync(cancellationToken);

        return Results.Ok(currentEmployee.ToEmployeeInfoDto());
    }
}
