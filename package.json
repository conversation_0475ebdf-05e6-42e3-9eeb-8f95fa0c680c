{"name": "@ot/onetalent-ui-kit", "version": "8.0.0", "type": "module", "description": "Shared UI components library for One Talent project mini apps", "author": "Adnoc", "license": "ISC", "repository": {"type": "git", "url": "*********************:v3/DevOpsAD/OneTalent/onetalent-ui-kit"}, "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/lib/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}, "./styles.css": "./dist/style.css"}, "files": ["dist"], "sideEffects": ["**/*.css"], "scripts": {"setup": "node scripts/setup-npmrc-config-universal.cjs", "build": "vite build && yarn copy:icons && yarn copy:illustrations && yarn copy:tailwind:config && yarn copy:scss", "build:dev": "yarn build --mode development", "typecheck": "tsc --noEmit --project ./tsconfig.json", "check": "yarn typecheck && yarn lint && yarn test --run", "check:fix": "yarn check:codestyle:fix && yarn lint:fix", "lint": "eslint --debug --ext .ts,.tsx,.js,.jsx --ignore-path .gitignore --cache --cache-location .eslintcache .", "lint:fix": "yarn lint --fix", "test": "vitest", "check:codestyle:fix": "prettier --write \"**/*.+(ts|tsx|js|jsx|json|css|md)\"", "sync:styles": "tsx ./tailwind/scripts/main.ts", "prepublishOnly": "yarn build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "loki:test": "loki test", "loki:approve": "loki approve", "copy:icons": "copyfiles -u 1 'lib/assets/icons/**/*.svg' 'dist/lib'", "copy:illustrations": "copyfiles -u 1 'lib/assets/illustrations/**/*.svg' 'dist/lib'", "copy:scss": "copyfiles -u 1 'lib/styles/scss/**/*' 'dist/lib'", "copy:tailwind:config": "copyfiles -u 1 'tailwind/config/**/*' 'dist/tailwind'"}, "devDependencies": {"@epam/uui": "6.1.2", "@epam/uui-core": "6.1.2", "@changesets/cli": "2.29.5", "@chromatic-com/storybook": "^3", "@hookform/resolvers": "3.9.0", "@storybook/addon-essentials": "8.6.12", "@storybook/addon-interactions": "8.6.12", "@storybook/addon-onboarding": "8.6.12", "@storybook/addon-themes": "8.6.12", "@storybook/blocks": "8.6.12", "@storybook/builder-vite": "8.6.12", "@storybook/jest": "^0.2.3", "@storybook/preview-api": "8.6.12", "@storybook/react": "8.6.12", "@storybook/react-vite": "8.6.12", "@storybook/test": "8.6.12", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "4.17.16", "@types/node": "22.13.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-infinite-scroller": "1.2.5", "@types/recharts": "^2.0.1", "@typescript-eslint/eslint-plugin": "8.34.0", "@typescript-eslint/parser": "8.34.0", "@vitejs/plugin-react": "4.5.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.0.9", "@vitest/ui": "^3.0.9", "autoprefixer": "^10.4.19", "browserslist": "^4.22.2", "browserslist-to-esbuild": "^2.1.1", "ci-info": "^4.0.0", "copyfiles": "2.4.1", "dotenv": "16.4.7", "eslint": "8.57.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-local-rules": "3.0.2", "eslint-plugin-prettier": "5.0.0", "eslint-plugin-react": "7.29.4", "eslint-plugin-react-hooks": "4.5.0", "eslint-plugin-storybook": "0.12.0", "eslint-plugin-unicorn": "42.0.0", "fs-extra": "^11.2.0", "happy-dom": "^17.4.4", "history": "^5.3.0", "jest": "^29.7.0", "loki": "^0.35.1", "postcss": "^8.4.47", "postcss-import": "^16.1.0", "postcss-preset-env": "^9.5.14", "prettier": "^3.2.4", "prettier-plugin-tailwindcss": "^0.6.5", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "7.54.2", "sass-embedded": "1.86.0", "storybook": "8.6.12", "storybook-dark-mode": "4.0.2", "tailwindcss": "^3.4.13", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "5.8.3", "vite": "^5.0.12", "vite-plugin-checker": "^0.6.2", "vite-plugin-dts": "4.5.3", "vite-plugin-sass-dts": "1.3.31", "vite-plugin-svgr": "4.3.0", "vitest": "^3.0.9"}, "peerDependencies": {"@epam/uui": "6.1.2", "@epam/uui-core": "6.1.2", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "7.54.2"}, "dependencies": {"@floating-ui/react": "0.27.7", "@tailwindcss/container-queries": "0.1.1", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "date-fns": "4.1.0", "lodash": "4.17.21", "lottie-react": "^2.4.1", "react-day-picker": "9.7.0", "react-infinite-scroller": "1.2.6", "recharts": "3.0.2", "swiper": "11.2.10", "tailwind-merge": "3.1.0", "tailwind-scrollbar": "4.0.2", "yup": "1.4.0"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "loki": {"configurations": {"chrome.laptop": {"target": "chrome.docker", "width": 1440, "height": 900, "deviceScaleFactor": 1, "mobile": false}, "chrome.iphone7": {"target": "chrome.docker", "preset": "iPhone 7", "width": 375, "height": 800}}}}