services:

  mssql_db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    environment:
      MSSQL_SA_PASSWORD: ${MSSQL_SA_PASSWORD}
      ACCEPT_EULA: "Y"
    ports:
      - "1433:1433"
    # healthcheck:
    #   test: /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P ${MSSQL_SA_PASSWORD} -Q "SELECT 1" -b -o /dev/null
    #   interval: 10s
    #   timeout: 2s
    #   retries: 10
    #   start_period: 10s

  web:
    image: one-talent/be-feedback-service:0.0.1-beta
    environment:
      ASPNETCORE_ENVIRONMENT: "Development"
      ConnectionStrings__FeedbackDb: "Server=mssql_db,1433;TrustServerCertificate=True;User Id=SA;Password=${MSSQL_SA_PASSWORD};"
      AzureAd__ClientSecret: "****************************************"
      # Swagger__Enabled: false
    ports:
      - "8080:8080"
    depends_on:
      - mssql_db
