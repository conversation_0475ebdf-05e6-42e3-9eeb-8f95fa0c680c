replicas: 2
name: onehub-backend-atr-admin-service
imageVersion: local

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

pagingOptions:
  defaultPageSize: 10
  maxPageSize: 500

image:
  repository: acrshduatotl01.azurecr.io/onetalent-backend-atr-admin-service
  port: 8080
  args: []

swagger:
  serverRelativePath: "atr-admin"
  useSsl: true

caching:
  expirationInMinutes: 5
  localCacheExpirationInMinutes: 5
  maximumKeyLength: 1024
  prefix: "atr-admin"

ingress:
  corsAllowHeaders: "DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,traceparent,tracestate,request-id,request-context,test-header"

atrService:
  baseUrl: "https://example.com"
  scope: "example-scope"

userMapping:
  impersonate<PERSON><PERSON><PERSON><PERSON>romHeader: false

httpLogging:
  Disabled: false
  AllowedHeaders:
    - "sec-ch-ua"
    - "sec-ch-ua-platform"
    - "sec-ch-ua-mobile"
    - "Sec-Fetch-Site"
    - "Sec-Fetch-Mode"
    - "Sec-Fetch-Dest"

initMigration: true

featureManagement: {}

massTransit:
  isEnabled: true
  sharedAccessKey: mock
  sharedAccessKeyName: mock
  endpoint: mock
  subscriptionPrefix: atr-admin
  topics:
    atrCycleTopic:
      isEnabled: true
      subscriptions:
        atrCycleChangedSubscription:
          isEnabled: true
    employeeTopic:
      isEnabled: true
      subscriptions:
        employeeChangedSubscription:
          isEnabled: true
    feedbackTopic:
      isEnabled: true
      subscriptions:
        feedbackChangedSubscription:
          isEnabled: true

hangfire:
  dashboard:
    enabled: false
    login: admin
    password: mock
    prefixPath: /atr-admin
  inMemoryStorage: false
  backgroundJobServerName: AtrAdminServiceHangfire
  dbConnectionStringName: AtrAdminDb
  hangfireScheme: fhf

blobStorage:
  connectionString: mock
  allowedFileFormats:
    - extension: .csv
      skipSignatureCheck: true
      signatures:
        - csv has no signature
