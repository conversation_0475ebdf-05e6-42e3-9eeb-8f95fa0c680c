replicas: 1
name: onehub-backend-feedback-service-prd

environment: "PRD"

hpaConf:
  reqCpu: 50m
  reqMem: 0.5Gi
  #limCpu: 4000m # remove limit to avoid throttling
  limMem: 1.2Gi
  minRep: 2
  maxRep: 4
  avuCpu: 75
  avuMem: 80

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

applicationInsights:
  connectionString: mock
  enableAdaptiveSampling: false

appConfig:
  connectionString: mock

azureAd:
  instance: https://login.microsoftonline.com/
  tenantId: 74892fe7-b6cb-43e7-912b-52194d3fd7c8
  clientId: 890c7a52-d30e-4d62-80ae-19f90750a0ef
  clientSecret: mock
  audience: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef"

swagger:
  enabled: false

workflow:
  baseUrl: "https://waas.adnoc.ae"
  module: "FeedbackApproval"
  workflowScope: "oneTalentFeedback"
  remoteEntryUrl: "https://onetalent.adnoc.ae/feedback/remoteEntry.js"
  flows:
    Feedback Request:
      NotificationType: "Feedback"
      OneHubUrl: "https://onehub.adnoc.ae"
    Voluntary Feedback:
      NotificationType: "Voluntary Feedback"
      OneHubUrl: "https://onehub.adnoc.ae"
      ViewDetailsUrl: "https://onehub.adnoc.ae/e-service-item/feedback/0990a64e-8129-4d37-b56d-0974e745b4b7/my/feedback?drawer=FeedbackDetailsReceived"

caching:
  namespace: "prd."

ai:
  baseUrl: "https://api.adnoc.ae/dataiku/onetalent/"

surveyApi:
  baseUrl: "https://api.adnoc.ae/servicebox/onetalent/"

employeeSearchService:
  baseUrl: "https://api.adnoc.ae/search/onetalent"
  scope: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef/.default"

database:
  login: mock
  password: mock
  server: sql-shd-prd-01.database.windows.net,1433
  additionalParams: Initial Catalog=sql-shd-prd-otl-fbk-01;TrustServerCertificate=true;Min Pool Size=10;Max Pool Size=1000;

seq:
  enabled: true
  host: "http://seq-forwarder-prd.onetalent-prd.svc.cluster.local"
  apiKey: mock

ingress:
  host: api.onetalent.adnoc.ae
  path: /feedback
  corsAllowOrigin: "https://onehub.adnoc.ae"

adminService:
  baseUrl: "http://onetalent-backend-query-service-prd.onetalent-prd.svc.cluster.local"
  scope: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef/.default"

synchronizationSfService:
  baseUrl: "http://onetalent-backend-sap-sf-sync-service-prd.onetalent-prd.svc.cluster.local"
  scope: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef/.default"

performanceService:
  baseUrl: "http://onetalent-backend-performance-service-prd.onetalent-prd.svc.cluster.local"
  scope: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef/.default"

userMapping:
  enabled: false

featureManagement:
  Feedback.Approvals.Notifications: true
  Feedback.Employees.OneHubSearch: true
# mass transit prefix is omitted here because queues for PRD should not have prefix.
