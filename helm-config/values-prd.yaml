replicas: 1
name: onehub-backend-atr-admin-service-prd

environment: "PRD"

hpaConf:
  reqCpu: 50m
  reqMem: 0.5Gi
  #limCpu: 4000m # remove limit to avoid throttling
  limMem: 1Gi
  minRep: 2
  maxRep: 4
  avuCpu: 75
  avuMem: 80

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

applicationInsights:
  connectionString: mock
  enableAdaptiveSampling: false

appConfig:
  connectionString: mock

azureAd:
  instance: https://login.microsoftonline.com/
  tenantId: 74892fe7-b6cb-43e7-912b-52194d3fd7c8
  clientId: 890c7a52-d30e-4d62-80ae-19f90750a0ef
  clientSecret: mock
  audience: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef"

swagger:
  enabled: false

caching:
  namespace: "prd."

database:
  login: mock
  password: mock
  server: sql-shd-prd-01.database.windows.net,1433
  additionalParams: Initial Catalog=sql-shd-prd-otl-01;Encrypt=True;Trusted_Connection=True;TrustServerCertificate=True;Integrated Security=False;

seq:
  enabled: true
  host: "http://seq-forwarder-prd.onetalent-prd.svc.cluster.local"
  apiKey: mock

ingress:
  host: api.onetalent.adnoc.ae
  path: /atr-admin
  corsAllowOrigin: "https://onehub.adnoc.ae"

dataSyncService:
  baseUrl: "http://onetalent-backend-data-sync-service-prd.onetalent-prd.svc.cluster.local"
  scope: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef/.default"

atrService:
  baseUrl: "http://onetalent-backend-atr-service-prd.onetalent-prd.svc.cluster.local"
  scope: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef/.default"

userMapping:
  enabled: false

blobStorage:
  containerName: prd-atr-admin

# mass transit prefix is omitted here because queues for PRD should not have prefix.
