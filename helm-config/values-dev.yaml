replicas: 1
name: onehub-backend-atr-admin-service-dev

environment: "DEV"

hpaConf:
  reqCpu: 50m
  reqMem: 0.5Gi
  #limCpu: 1000m # remove limit to avoid throttling
  limMem: 1Gi
  minRep: 2
  maxRep: 3
  avuCpu: 95
  avuMem: 80

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

applicationInsights:
  connectionString: mock
  enableAdaptiveSampling: false

appConfig:
  connectionString: mock

azureAd:
  instance: https://login.microsoftonline.com/
  tenantId: 74892fe7-b6cb-43e7-912b-52194d3fd7c8
  clientId: 82cd72ca-0c69-4d5c-980b-20856c1d62fe
  clientSecret: mock
  audience: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe"

swagger:
  enabled: true

caching:
  namespace: "dev."

database:
  login: mock
  password: mock
  server: sql-shd-dev-01.database.windows.net,1433
  additionalParams: Database=sql-shd-dev-otl-01;TrustServerCertificate=true;

seq:
  enabled: true
  host: "http://seq-forwarder-non-prod.seq-forwarder.svc.cluster.local"
  apiKey: mock

ingress:
  host: api.dev.onetalent.adnoc.ae
  path: /atr-admin
  corsAllowOrigin: "https://onehub2-stg.adnoc.ae"

dataSyncService:
  baseUrl: "http://onetalent-backend-data-sync-service.onetalent-dev.svc.cluster.local"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"

atrService:
  baseUrl: "http://onetalent-backend-atr-service.onetalent-dev.svc.cluster.local"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"

userMapping:
  enabled: true
  baseUrl: "https://servicebox-uat.adnoc.ae/OneHub.Api/"
  targetScope: "https://servicebox-uat.adnoc.ae/.default"
  impersonateAsUserFromHeader: true

massTransit:
  queueTopicPrefix: dev

hangfire:
  dashboard:
    enabled: true

blobStorage:
  containerName: dev-atr-admin
