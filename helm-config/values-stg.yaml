replicas: 1
name: onehub-backend-feedback-service-stg

environment: "STG"

hpaConf:
  reqCpu: 50m
  reqMem: 0.5Gi
  #limCpu: 2000m # remove limit to avoid throttling
  limMem: 1.2Gi
  minRep: 2
  maxRep: 3
  avuCpu: 85
  avuMem: 80

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

applicationInsights:
  connectionString: mock
  enableAdaptiveSampling: false

appConfig:
  connectionString: mock

azureAd:
  instance: https://login.microsoftonline.com/
  tenantId: 74892fe7-b6cb-43e7-912b-52194d3fd7c8
  clientId: 82cd72ca-0c69-4d5c-980b-20856c1d62fe
  clientSecret: mock
  audience: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe"

swagger:
  enabled: true

workflow:
  baseUrl: "https://servicebox-test.adnoc.ae/WaaS"
  module: "FeedbackApproval"
  workflowScope: "oneTalentFeedback"
  remoteEntryUrl: "https://stg.onetalent.adnoc.ae/feedback/remoteEntry.js"
  flows:
    Feedback Request:
      NotificationType: "Feedback"
      OneHubUrl: "https://onehub2-uat.adnoc.ae"
    Voluntary Feedback:
      NotificationType: "Voluntary Feedback"
      OneHubUrl: "https://onehub2-uat.adnoc.ae"
      ViewDetailsUrl: "https://onehub2-stg.adnoc.ae/e-service-item/feedback/9735972d-09a3-476f-842f-969427b76d57/my/feedback?drawer=FeedbackDetailsReceived"

ai:
  baseUrl: "https://dev.api.adnoc.ae/dataiku/onetalent/"
  environmentPrefix: "stg"

surveyApi:
  baseUrl: "https://dev.api.adnoc.ae/servicebox/onetalent/"

employeeSearchService:
  baseUrl: "https://dev.api.adnoc.ae/search/onetalent/stg"

database:
  login: mock
  password: mock
  server: sql-shd-uat-01.database.windows.net,1433
  additionalParams: Initial Catalog=sql-shd-stg-otl-fbk-01;TrustServerCertificate=true;Min Pool Size=10;Max Pool Size=1000;

caching:
  namespace: "stg."

seq:
  enabled: true
  host: "http://seq-forwarder-non-prod.seq-forwarder.svc.cluster.local"
  apiKey: mock

ingress:
  host: api.stg.onetalent.adnoc.ae
  path: /feedback
  corsAllowOrigin: "https://onehub2-stg.adnoc.ae"

adminService:
  baseUrl: "http://onetalent-backend-query-service-stg.onetalent-stg.svc.cluster.local"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"

synchronizationSfService:
  baseUrl: "http://onetalent-backend-sap-sf-sync-service-stg.onetalent-stg.svc.cluster.local"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"

performanceService:
  baseUrl: "http://onetalent-backend-performance-service-stg.onetalent-stg.svc.cluster.local"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"

userMapping:
  enabled: true
  baseUrl: "https://servicebox-uat.adnoc.ae/OneHub.Api/"
  targetScope: "https://servicebox-uat.adnoc.ae/.default"
  impersonateAsUserFromHeader: true

featureManagement:
  Feedback.Approvals.Notifications: true

massTransit:
  queueTopicPrefix: stg

hangfire:
  dashboard:
    enabled: true
