trigger: none
appendCommitMessageToRunName: false

resources:
  repositories:
  - repository: devops-common
    type: git
    name: OneTalent/devops-common
    ref: main

  pipelines:
  - pipeline: build-pipeline
    source: build-atr-admin-service
    trigger: 
      branches:
        include: 
        - main

variables: 
- name: migration_arg
  value: ''

stages:
- template: pipelines/templates/helm-deploy-all-envs.template.yml@devops-common
  parameters:
    application_name: onetalent-backend-atr-admin-service
    build_output_name: build-pipeline
    config_map_path: "helm-config"
    additional_deploy_params: >-
      --set database.password=$(DB_Admin_sql_password)
      --set database.login=$(DB_Admin_sql_username)
      --set blobStorage.connectionString=$(AzureBlobConfig_ConnString)
      --set azureAd.clientSecret=$(Client_Secret)
      --set seq.apiKey=$(Seq_ApiKey)
      --set applicationInsights.connectionString=$(AppInsights_ConnString)
      --set massTransit.endpoint=$(MassTransit_Endpoint)
      --set massTransit.sharedAccessKey=$(MassTransit_SharedAccessKey)
      --set massTransit.sharedAccessKeyName=$(MassTransit_SharedAccessKeyName)
      --set appConfig.connectionString=$(AppConfig_ConnString)
      --set caching.connectionString=$(RedisConfig_ConnString)
      --set hangfire.dashboard.password=$(Hangfire_Password)
      $(migration_arg)
