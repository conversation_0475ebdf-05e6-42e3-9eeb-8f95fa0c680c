trigger:
  branches:
    include:
      - main
      - release/*
  paths:
    include:
      - /*
    exclude:
      - /devops/*
      - /helm-config/*
      - /scripts/*

resources:
  repositories:
    - repository: devops-common
      type: git
      name: OneTalent/devops-common
      ref: main

    - repository: devops-helm-template
      type: git
      name: OneTalent/devops-helm-template
      ref: main

stages:
- template: pipelines/templates/dotnet-build.v2.template.yml@devops-common
  parameters:
    service_name: "onetalent-backend-feedback-service"
    repository_name: "onetalent-backend-feedback-service"
    dockerfile_path: "src/WebAPI/Dockerfile"
    build_context_path: "."
    helm_chart_folder: "helm/"
    version_prefix: "4.0"
