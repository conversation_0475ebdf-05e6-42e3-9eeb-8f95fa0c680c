trigger: none

schedules:
  - cron: "0 2 * * *"
    displayName: "Every day at 02:00 UTC"
    branches:
      include:
        - main

parameters:
  - name: test_coverage
    type: boolean
    default: false

resources:
  repositories:
    - repository: devops-common
      type: git
      name: OneTalent/devops-common
      ref: main
    
    - repository: devops-helm-template
      type: git
      name: OneTalent/devops-helm-template
      ref: main

stages:
- template: pipelines/templates/dotnet-ci.v4.template.yml@devops-common
  parameters:
    project_name: "FeedbackService"
    helm_chart_folder: "helm/"
    helm_values_folder: "helm-config/"
    continue_on_error: false
    ${{ if or(eq(parameters.test_coverage, true), eq(variables['Build.Reason'], 'Schedule')) }}:
      test_coverage: true