Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.35906.104
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{8EC462FD-D22E-90A8-E5CE-7E832BA40C5D}"
	ProjectSection(SolutionItems) = preProject
		.dockerignore = .dockerignore
		.editorconfig = .editorconfig
		.gitattributes = .gitattributes
		.gitignore = .gitignore
		compose.yml = compose.yml
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		NuGet.config = NuGet.config
		README.md = README.md
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Application", "src\Application\Application.csproj", "{661C8064-0CBF-4B4B-A7B1-E034E74EB626}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure.DataAccessLayer", "src\Infrastructure.DataAccessLayer\Infrastructure.DataAccessLayer.csproj", "{001892B1-DF3D-4ADF-A336-E227C45C43CF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebAPI", "src\WebAPI\WebAPI.csproj", "{B8DC3F20-D6CE-4FF2-B3D4-5F1693BA8B3D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebAPI.Client", "src\WebAPI.Client\WebAPI.Client.csproj", "{4E037D97-8262-420D-9A7B-0F56A94B2D2C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		tests\.editorconfig = tests\.editorconfig
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Application.UnitTests", "tests\Application.UnitTests\Application.UnitTests.csproj", "{7228166F-51EE-9999-FD7F-10400C07267D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Contracts", "src\Contracts\Contracts.csproj", "{F098EBAE-F06A-4916-8C90-637CBA385F1E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure.Integrations", "src\Infrastructure.Integrations\Infrastructure.Integrations.csproj", "{4E1A5AE9-4B79-4B1A-B859-6975AAA4C3D7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure.Messaging", "src\Infrastructure.Messaging\Infrastructure.Messaging.csproj", "{B2DF4A97-8EDB-4991-A260-847D9C7CC673}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure.Jobs", "src\Infrastructure.Jobs\Infrastructure.Jobs.csproj", "{D10D8A0C-A514-4FA0-BEE7-4A7355E1DB99}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure.Storage", "src\Infrastructure.Storage\Infrastructure.Storage.csproj", "{759EEFAD-81FC-4504-B7A5-24D3FC911CE8}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{661C8064-0CBF-4B4B-A7B1-E034E74EB626}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{661C8064-0CBF-4B4B-A7B1-E034E74EB626}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{661C8064-0CBF-4B4B-A7B1-E034E74EB626}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{661C8064-0CBF-4B4B-A7B1-E034E74EB626}.Release|Any CPU.Build.0 = Release|Any CPU
		{001892B1-DF3D-4ADF-A336-E227C45C43CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{001892B1-DF3D-4ADF-A336-E227C45C43CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{001892B1-DF3D-4ADF-A336-E227C45C43CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{001892B1-DF3D-4ADF-A336-E227C45C43CF}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8DC3F20-D6CE-4FF2-B3D4-5F1693BA8B3D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8DC3F20-D6CE-4FF2-B3D4-5F1693BA8B3D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8DC3F20-D6CE-4FF2-B3D4-5F1693BA8B3D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8DC3F20-D6CE-4FF2-B3D4-5F1693BA8B3D}.Release|Any CPU.Build.0 = Release|Any CPU
		{4E037D97-8262-420D-9A7B-0F56A94B2D2C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4E037D97-8262-420D-9A7B-0F56A94B2D2C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4E037D97-8262-420D-9A7B-0F56A94B2D2C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4E037D97-8262-420D-9A7B-0F56A94B2D2C}.Release|Any CPU.Build.0 = Release|Any CPU
		{7228166F-51EE-9999-FD7F-10400C07267D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7228166F-51EE-9999-FD7F-10400C07267D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7228166F-51EE-9999-FD7F-10400C07267D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7228166F-51EE-9999-FD7F-10400C07267D}.Release|Any CPU.Build.0 = Release|Any CPU
		{F098EBAE-F06A-4916-8C90-637CBA385F1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F098EBAE-F06A-4916-8C90-637CBA385F1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F098EBAE-F06A-4916-8C90-637CBA385F1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F098EBAE-F06A-4916-8C90-637CBA385F1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{4E1A5AE9-4B79-4B1A-B859-6975AAA4C3D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4E1A5AE9-4B79-4B1A-B859-6975AAA4C3D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4E1A5AE9-4B79-4B1A-B859-6975AAA4C3D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4E1A5AE9-4B79-4B1A-B859-6975AAA4C3D7}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2DF4A97-8EDB-4991-A260-847D9C7CC673}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2DF4A97-8EDB-4991-A260-847D9C7CC673}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2DF4A97-8EDB-4991-A260-847D9C7CC673}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2DF4A97-8EDB-4991-A260-847D9C7CC673}.Release|Any CPU.Build.0 = Release|Any CPU
		{D10D8A0C-A514-4FA0-BEE7-4A7355E1DB99}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D10D8A0C-A514-4FA0-BEE7-4A7355E1DB99}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D10D8A0C-A514-4FA0-BEE7-4A7355E1DB99}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D10D8A0C-A514-4FA0-BEE7-4A7355E1DB99}.Release|Any CPU.Build.0 = Release|Any CPU
		{759EEFAD-81FC-4504-B7A5-24D3FC911CE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{759EEFAD-81FC-4504-B7A5-24D3FC911CE8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{759EEFAD-81FC-4504-B7A5-24D3FC911CE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{759EEFAD-81FC-4504-B7A5-24D3FC911CE8}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7228166F-51EE-9999-FD7F-10400C07267D} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {32B7316B-C2E7-4202-AE16-23DFC92F0204}
	EndGlobalSection
EndGlobal
