import { Config } from 'tailwindcss';

import dimensions from './generated/dimensions.json';
import primitive from './generated/primitive.json';
import semantic from './generated/semantic.json';
import typography from './generated/typography.json';

export const theme: Config['theme'] = {
  fontFamily: {
    'adnoc-sans': ["'ADNOC Sans'"]
  },
  screens: {
    sm: '376px',
    md: '701px',
    '2xl': '1440px',
    '3xl': '1600px',
    '4xl': '1920px',
    '5xl': '2880px'
  },
  containers: {
    sm: '376px',
    md: '701px',
    '2xl': '1440px',
    '3xl': '1600px',
    '4xl': '1920px',
    '5xl': '2880px'
  },
  boxShadow: {
    footer: '0px -8px 16px 0px rgba(0, 0, 0, 0.08);',
    tooltip: '0 6px 16px rgba(0, 0, 0, 0.12);',
    dropdown: '0 6px 16px rgba(0, 0, 0, 0.12);',
    button: '0 8px 16px 0 rgba(0, 0, 0, 0.25);'
  },
  colors: {
    ...primitive,
    ...semantic
  },
  spacing: {
    ...dimensions
  },
  typography: { ...typography }
};
