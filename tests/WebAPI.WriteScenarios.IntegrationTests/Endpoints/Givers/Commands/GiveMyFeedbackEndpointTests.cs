using Moq;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Application.Common.Constants;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Helpers;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.Endpoints.Givers.Commands;

public class GiveMyFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    private static readonly string[] _receiversIds = ["99905180"];

    [Fact]
    public async Task GiveMyFeedback_SuccessFlow_ReturnsSuccessAsync()
    {
        // Arrange
        var subject = nameof(GiveMyFeedback_SuccessFlow_ReturnsSuccessAsync);
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    ReceiversIds = _receiversIds,
                    Topic = FeedbackTopics.General,
                    Subject = subject,
                    FeedbackText = "Feedback",
                    Rating = 5
                })
                .ToUrl("/v1/givers/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.Created);
        });
        
        // Assert
        var result = DatabaseHelper.Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Subject] = '{subject}' ORDER BY [Id] DESC").Rows;

        await Verify(result)
            .IgnoreMember("Id")
            .IgnoreMember("CreatedDate")
            .IgnoreMember("LastModifiedDate")
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task GiveMyFeedback_WithEmptyReceiversList_ReturnsBadRequestAsync()
    {
        // Arrange
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    ReceiversIds = Array.Empty<ItemId>(),
                    Topic = FeedbackTopics.General,
                    Subject = "Subject",
                    FeedbackText = "Feedback",
                    Rating = 5
                })
                .ToUrl("/v1/givers/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });
    }

    [Fact]
    public async Task GiveMyFeedback_EnabledRatingFlag_ReturnsBadRequestAsync()
    {
        // Arrange
        fixture.FeatureManagerMock.Reset();

        fixture.FeatureManagerMock.Setup(client => client.IsEnabledAsync(
            FeatureFlags.EnableFeedbackRating))
            .ReturnsAsync(true);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    ReceiversIds = _receiversIds,
                    Topic = FeedbackTopics.General,
                    Subject = "Subject",
                    FeedbackText = "Feedback",
                    Rating = 6
                })
                .ToUrl("/v1/givers/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });
    }

    [Fact]
    public async Task GiveMyFeedback_DisabledRatingFlag_ReturnsSuccessAsync()
    {
        // Arrange
        fixture.FeatureManagerMock.Reset();

        fixture.FeatureManagerMock.Setup(client => client.IsEnabledAsync(
            FeatureFlags.EnableFeedbackRating))
            .ReturnsAsync(false);

        var subject = nameof(GiveMyFeedback_DisabledRatingFlag_ReturnsSuccessAsync);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    ReceiversIds = _receiversIds,
                    Topic = FeedbackTopics.General,
                    Subject = subject,
                    FeedbackText = "Feedback"
                })
                .ToUrl("/v1/givers/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.Created);
        });

        // Assert
        var result = DatabaseHelper.Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Subject] = '{subject}' ORDER BY [Id] DESC").Rows;

        await Verify(result)
            .IgnoreMember("Id")
            .IgnoreMember("CreatedDate")
            .IgnoreMember("LastModifiedDate")
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task GiveMyFeedback_UserDoesNotHaveFeedbackPhase_ReturnsForbiddenAsync()
    {
        // Arrange
        SetAuthToken(Auth.MJohnsonToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    ReceiversIds = _receiversIds,
                    Topic = FeedbackTopics.General,
                    Subject = "Subject",
                    FeedbackText = "Feedback",
                    Rating = 5
                })
                .ToUrl("/v1/givers/me/feedback");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.Forbidden);
        });
    }
    
    [Fact]
    public async Task GiveMyFeedback_LineManager_ReturnsSuccessAsync()
    {
        // Arrange
        SetAuthToken(Auth.ManagerToken);
        var subject = nameof(GiveMyFeedback_LineManager_ReturnsSuccessAsync);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    ReceiversIds = _receiversIds,
                    Topic = FeedbackTopics.General,
                    Subject = subject,
                    FeedbackText = "Feedback",
                    Rating = 5
                })
                .ToUrl("/v1/givers/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.Created);
        });
        
        // Assert
        var result = DatabaseHelper.Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Subject] = '{subject}' ORDER BY [Id] DESC").Rows;

        await Verify(result)
            .IgnoreMember("Id")
            .IgnoreMember("CreatedDate")
            .IgnoreMember("LastModifiedDate")
            .DontScrubDateTimes();
    }
}
