using Alba;
using Moq;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Helpers;
using OneTalent.FeedbackService.Infrastructure.Integrations.SurveysService.Models.Requests;
using ProductivityGain = OneTalent.FeedbackService.Application.Surveys.Commands.Models.ProductivityGain;
using Improvement = OneTalent.FeedbackService.Application.Surveys.Commands.Models.Improvement;
using OneTalent.FeedbackService.WebAPI.DTOs.Requests;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.Endpoints.Surveys.Commands;

public class SubmitFeedbackSurveyEndpointTests : WebAppContext
{
    const string Url = "/v1/employees/me/surveys/feedback-request";

    private readonly WebAppFixture _fixture;

    public SubmitFeedbackSurveyEndpointTests(WebAppFixture fixture) : base(fixture)
    {
        _fixture = fixture;

        _fixture.SurveyApiClientMock.Reset();
    }

    #region RatingIs5
    private readonly SubmitFeedbackSurveyRequest _ratingIs4Request = new(
        ProductivityGain.MoreThan1Hour,
        4);

    [Fact]
    public async Task SubmitFeedbackSurvey_RatingIs4_RespondsWith200Ok()
    {
        // Arrange
        const string employeeId = "99905178";

        await Host.Scenario(api =>
        {
            // Act
            api.Post.Json(_ratingIs4Request).ToUrl(Url);

            // Assert
            api.StatusCodeShouldBeOk();
        });

        var employeeSurveys = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Surveys] WHERE [EmployeeId] = {employeeId}").Rows;

        await Verify(employeeSurveys);
    }

    [Fact]
    public async Task SubmitFeedbackSurvey_RatingIs4_SubmitSatisfactionSurveyCalled()
    {
        // Arrange
        var taskCompletionSource = new TaskCompletionSource();

        SubmitSatisfactionSurveyRequest capturedRequest = null!;
        _fixture.SurveyApiClientMock.Setup(client => client.SubmitSatisfactionSurvey(
                It.IsAny<SubmitSatisfactionSurveyRequest>(),
                It.IsAny<CancellationToken>()))
            .Callback<SubmitSatisfactionSurveyRequest, CancellationToken>((request, token) =>
            {
                capturedRequest = request;
                taskCompletionSource.SetResult();
            })
            .Returns(Task.CompletedTask);

        // Act
        await Host.Scenario(api =>
        {
            api.Post.Json(_ratingIs4Request).ToUrl(Url);
            api.IgnoreStatusCode();
        });

        await taskCompletionSource.Task.WaitAsync(TestContext.Current.CancellationToken);

        // Assert
        _fixture.SurveyApiClientMock.Verify(client => client.SubmitSatisfactionSurvey(
                It.IsAny<SubmitSatisfactionSurveyRequest>(),
                It.IsAny<CancellationToken>()),
            Times.Once);

        await Verify(capturedRequest);

        _fixture.SurveyApiClientMock.VerifyNoOtherCalls();
    }

    #endregion RatingIs5

    #region RatingIs1

    private readonly SubmitFeedbackSurveyRequest _ratingIs3Request = new(
        ProductivityGain.NoImpact,
        3,
        [
            Improvement.ImproveEaseOfUseOfTheApplication,
            Improvement.EnhanceGuidelinesProcess,
            Improvement.ProvideBetterHcSupport
        ],
        "Test comment.");

    [Fact]
    public async Task SubmitFeedbackSurvey_RatingIs3_RespondsWith200Ok()
    {
        // Arrange
        const string employeeId = "99905178";

        await Host.Scenario(api =>
        {
            // Act
            api.Post.Json(_ratingIs3Request).ToUrl(Url);

            // Assert
            api.StatusCodeShouldBeOk();
        });

        var employeeSurveys = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Surveys] WHERE [EmployeeId] = {employeeId}").Rows;

        await Verify(employeeSurveys);
    }

    [Fact]
    public async Task SubmitFeedbackSurvey_RatingIs3_SubmitSatisfactionSurveyCalled()
    {
        // Arrange
        var taskCompletionSource = new TaskCompletionSource();

        SubmitSatisfactionSurveyRequest capturedRequest = null!;
        _fixture.SurveyApiClientMock.Setup(client => client.SubmitSatisfactionSurvey(
                It.IsAny<SubmitSatisfactionSurveyRequest>(),
                It.IsAny<CancellationToken>()))
            .Callback<SubmitSatisfactionSurveyRequest, CancellationToken>((request, token) =>
            {
                capturedRequest = request;
                taskCompletionSource.SetResult();
            })
            .Returns(Task.CompletedTask);

        // Act
        await Host.Scenario(api =>
        {
            api.Post.Json(_ratingIs3Request).ToUrl(Url);
            api.IgnoreStatusCode();
        });

        await taskCompletionSource.Task.WaitAsync(TestContext.Current.CancellationToken);

        // Assert
        _fixture.SurveyApiClientMock.Verify(client => client.SubmitSatisfactionSurvey(
               It.IsAny<SubmitSatisfactionSurveyRequest>(),
               It.IsAny<CancellationToken>()),
           Times.Once);

        await Verify(capturedRequest);

        _fixture.SurveyApiClientMock.VerifyNoOtherCalls();
    }

    #endregion RatingIs1
}
