using eServices.Workflow.Sdk.WorkflowBuilder;
using Microsoft.Extensions.DependencyInjection;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.Infrastructure.Integrations.WaaS.Models;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Helpers;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.Endpoints.Approvals.Commands;

public class PostApprovalActionEndpointTests : WebAppContext
{
    readonly TimeProvider _timeProvider;

    public PostApprovalActionEndpointTests(WebAppFixture fixture) : base(fixture)
    {
        _timeProvider = Host.Services.GetRequiredService<TimeProvider>();
    }

    [Fact]
    public async Task GiveFeedback_Success()
    {
        // Arrange
        SetAuthToken(Auth.EBrownToken);
        var approvalId = 3;
        var feedbackId = 65490283500050108;

        var before = DatabaseHelper
            .Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Id] = {feedbackId}")
            .Rows;

        Assert.Equal(FeedbackStatuses.Pending, (FeedbackStatuses)before[0].StatusId);
        Assert.Equal(DBNull.Value, before[0].CompletedDate);

        // Act
        await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    ActionId = ActionStatus.Submit,
                    Rating = 4,
                    Feedback = "Feedback Text"
                })
                .ToUrl($"/v1/approvals/{approvalId}/actions");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.NoContent);
        });

        var after = DatabaseHelper
            .Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Id] = {feedbackId}")
            .Rows;

        Assert.Equal(FeedbackStatuses.Completed, (FeedbackStatuses)after[0].StatusId);
        Assert.Equal(_timeProvider.GetUtcNow(), (DateTimeOffset)after[0].CompletedDate);

        await Verify()
            .AppendValue(nameof(before), before)
            .AppendValue(nameof(after), after)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task DeclineFeedback_Success()
    {
        // Arrange
        SetAuthToken(Auth.EBrownToken);
        var approvalId = 2;
        var feedbackId = 65490283500050107;

        var before = DatabaseHelper
            .Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Id] = {feedbackId}")
            .Rows;

        Assert.Equal(FeedbackStatuses.Pending, (FeedbackStatuses)before[0].StatusId);
        Assert.Equal(DBNull.Value, before[0].DeclineDate);

        // Act
        await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    ActionId = Actions.DeclineId,
                    DeclineReason = "unclearExpectations",
                    Comment = "Decline Reason Comment"
                })
                .ToUrl($"/v1/approvals/{approvalId}/actions");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.NoContent);
        });

        var after = DatabaseHelper
            .Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Id] = {feedbackId}")
            .Rows;

        Assert.Equal(FeedbackStatuses.Declined, (FeedbackStatuses)after[0].StatusId);
        Assert.Equal(_timeProvider.GetUtcNow(), (DateTimeOffset)after[0].DeclineDate);

        await Verify()
            .AppendValue(nameof(before), before)
            .AppendValue(nameof(after), after)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task CancelFeedback_Success()
    {
        // Arrange
        SetAuthToken(Auth.HWilsonToken);
        var approvalId = 1;
        var feedbackId = 65490283500050106;

        var before = DatabaseHelper
            .Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Id] = {feedbackId}")
            .Rows;

        Assert.Equal(FeedbackStatuses.Pending, (FeedbackStatuses)before[0].StatusId);
        Assert.Equal(DBNull.Value, before[0].WithdrawnDate);

        // Act
        await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    ActionId = ActionStatus.Cancel,
                })
                .ToUrl($"/v1/approvals/{approvalId}/actions");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.NoContent);
        });

        var after = DatabaseHelper
            .Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Id] = {feedbackId}")
            .Rows;

        Assert.Equal(FeedbackStatuses.Withdrawn, (FeedbackStatuses)after[0].StatusId);
        Assert.Equal(_timeProvider.GetUtcNow(), (DateTimeOffset)after[0].WithdrawnDate);

        await Verify()
            .AppendValue(nameof(before), before)
            .AppendValue(nameof(after), after)
            .DontScrubDateTimes();
    }
}
