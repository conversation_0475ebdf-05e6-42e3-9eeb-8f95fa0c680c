{
  before: [
    {
      AtrCycleId: 1,
      CompletedDate: null,
      CreatedById: 99905178,
      CreatedDate: 2025-04-01 +0,
      DeclineComment: null,
      DeclineDate: null,
      DeclineReasonId: null,
      FeedbackText: null,
      GiverId: 99905181,
      Id: 65490283500050108,
      IsImportant: true,
      IsProvidedByLineManager: false,
      IsRequestedByManager: false,
      IsVisibleToManager: true,
      IsVisibleToReceiver: true,
      LastModifiedById: 99905181,
      LastModifiedDate: 2025-04-01 +0,
      ObjectiveId: null,
      Rating: null,
      ReceiverId: 99905178,
      RequestDetails: I would appreciate feedback on how I handled customer objections.,
      RequestedDate: 2025-02-11 +0,
      RequestorId: 99905178,
      StatusId: 2,
      Subject: Requesting Feedback on Handling Customer Objections,
      TaskAndMilestoneId: null,
      TopicId: 5,
      WithdrawnDate: null
    }
  ],
  after: [
    {
      AtrCycleId: 1,
      CompletedDate: 2025-04-01 +0,
      CreatedById: 99905178,
      CreatedDate: 2025-04-01 +0,
      DeclineComment: null,
      DeclineDate: null,
      DeclineReasonId: null,
      FeedbackText: Feedback Text,
      GiverId: 99905181,
      Id: 65490283500050108,
      IsImportant: true,
      IsProvidedByLineManager: false,
      IsRequestedByManager: false,
      IsVisibleToManager: true,
      IsVisibleToReceiver: true,
      LastModifiedById: 99905181,
      LastModifiedDate: 2025-04-01 +0,
      ObjectiveId: null,
      Rating: 4,
      ReceiverId: 99905178,
      RequestDetails: I would appreciate feedback on how I handled customer objections.,
      RequestedDate: 2025-02-11 +0,
      RequestorId: 99905178,
      StatusId: 5,
      Subject: Requesting Feedback on Handling Customer Objections,
      TaskAndMilestoneId: null,
      TopicId: 5,
      WithdrawnDate: null
    }
  ]
}