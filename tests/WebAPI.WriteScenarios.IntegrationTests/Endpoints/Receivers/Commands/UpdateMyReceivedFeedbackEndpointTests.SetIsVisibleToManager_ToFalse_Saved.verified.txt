{
  before: [
    {
      AtrCycleId: 1,
      CompletedDate: 2025-01-22 +0,
      CreatedById: 99905178,
      CreatedDate: 2025-04-01 +0,
      DeclineComment: null,
      DeclineDate: null,
      DeclineReasonId: null,
      FeedbackText: Your sales numbers exceeded expectations this quarter. Keep up the consistent performance!,
      GiverId: 99905180,
      Id: 65490283500050002,
      IsImportant: true,
      IsProvidedByLineManager: true,
      IsRequestedByManager: false,
      IsVisibleToManager: true,
      IsVisibleToReceiver: true,
      LastModifiedById: 99905180,
      LastModifiedDate: 2025-04-01 +0,
      ObjectiveId: null,
      Rating: null,
      ReceiverId: 99905178,
      RequestDetails: Could you share how my coordination went for the feature release?,
      RequestedDate: 2025-01-01 +0,
      RequestorId: 99905178,
      StatusId: 5,
      Subject: Requesting Feedback on Feature Release Coordination Efforts,
      TaskAndMilestoneId: null,
      TopicId: 5,
      WithdrawnDate: null
    }
  ],
  after: [
    {
      AtrCycleId: 1,
      CompletedDate: 2025-01-22 +0,
      CreatedById: 99905178,
      CreatedDate: 2025-04-01 +0,
      DeclineComment: null,
      DeclineDate: null,
      DeclineReasonId: null,
      FeedbackText: Your sales numbers exceeded expectations this quarter. Keep up the consistent performance!,
      GiverId: 99905180,
      Id: 65490283500050002,
      IsImportant: true,
      IsProvidedByLineManager: true,
      IsRequestedByManager: false,
      IsVisibleToManager: false,
      IsVisibleToReceiver: true,
      LastModifiedById: 99905180,
      LastModifiedDate: 2025-04-01 +0,
      ObjectiveId: null,
      Rating: null,
      ReceiverId: 99905178,
      RequestDetails: Could you share how my coordination went for the feature release?,
      RequestedDate: 2025-01-01 +0,
      RequestorId: 99905178,
      StatusId: 5,
      Subject: Requesting Feedback on Feature Release Coordination Efforts,
      TaskAndMilestoneId: null,
      TopicId: 5,
      WithdrawnDate: null
    }
  ]
}