using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Helpers;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.Endpoints.Receivers.Commands;

public class UpdateMyReceivedFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    #region IsVisibleToManager

    [Fact]
    public async Task SetIsVisibleToManager_ToFalse_Saved()
    {
        // Arrange
        var id = "65490283500050002";

        var before = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Id] = '{id}'")
            .Rows;

        Assert.Equal(true, before[0].IsVisibleToManager);

        // Act
        await Host.Scenario(api =>
        {
            api.Patch
                .Json(new
                {
                    IsVisibleToManager = false
                })
                .ToUrl($"/v1/receivers/me/feedback/{id}");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        // Assert
        var after = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Id] = '{id}'")
            .Rows;

        Assert.Equal(false, after[0].IsVisibleToManager);

        await Verify()
            .AppendValue(nameof(before), before)
            .AppendValue(nameof(after), after)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task HideToManager_GivenByManager_ReturnsError()
    {
        // Arrange
        var id = "65490283500050004";

        var result = await Host.Scenario(api =>
        {
            // Act
            api.Patch
                .Json(new
                {
                    IsVisibleToManager = false
                })
                .ToUrl($"/v1/receivers/me/feedback/{id}");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        await VerifyJson(await result.ReadAsTextAsync())
            .IgnoreMembers("instance", "traceId");
    }
    
    [Fact]
    public async Task SetIsVisibleToManager_IfNotReceiver_ReturnsError()
    {
        // Arrange
        var id = "65490283500050112";

        var result = await Host.Scenario(api =>
        {
            // Act
            api.Patch
                .Json(new
                {
                    IsVisibleToManager = false
                })
                .ToUrl($"/v1/receivers/me/feedback/{id}");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        await VerifyJson(await result.ReadAsTextAsync())
            .IgnoreMembers("instance", "traceId");
    }
    
    [Fact]
    public async Task SetIsVisibleToManager_NotAllowedToChangeFeedbackVisibility_ReturnsError()
    {
        // Arrange
        SetAuthToken(Auth.IMartinezToken);
        var id = "65490283500050112";

        var result = await Host.Scenario(api =>
        {
            // Act
            api.Patch
                .Json(new
                {
                    IsVisibleToManager = false
                })
                .ToUrl($"/v1/receivers/me/feedback/{id}");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        await VerifyJson(await result.ReadAsTextAsync())
            .IgnoreMembers("instance", "traceId");
    }
    #endregion IsVisibleToManager
}
