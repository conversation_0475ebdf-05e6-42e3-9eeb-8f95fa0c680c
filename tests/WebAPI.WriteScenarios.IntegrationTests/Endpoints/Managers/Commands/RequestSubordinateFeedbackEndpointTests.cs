using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Helpers;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.Endpoints.Managers.Commands;

public class RequestSubordinateFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{

    private static readonly string[] _giversIds = ["99905179", "99905180"];

    [Fact]
    public async Task RequestSubordinateFeedback_SuccessFlow_ReturnsSuccessAsync()
    {
        // Arrange
        SetAuthToken(Auth.ManagerToken);

        var subject = nameof(RequestSubordinateFeedback_SuccessFlow_ReturnsSuccessAsync);
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = _giversIds,
                    Topic = FeedbackTopics.General,
                    Subject = subject,
                    Details = "Details"
                })
                .ToUrl("/v1/managers/me/subordinates/99905178/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.Created);
        });

        // Assert
        var result = DatabaseHelper.Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Subject] = '{subject}' ORDER BY [GiverId] DESC").Rows;

        await Verify(result)
            .IgnoreMember("Id")
            .IgnoreMember("CreatedDate")
            .IgnoreMember("LastModifiedDate")
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task RequestSubordinateFeedback_WithEmptyGiversList_ReturnsBadRequestAsync()
    {
        // Arrange
        SetAuthToken(Auth.ManagerToken);
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = Array.Empty<ItemId>(),
                    Topic = FeedbackTopics.General,
                    Subject = "Subject",
                    Details = "Details"
                })
                .ToUrl("/v1/managers/me/subordinates/99905178/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });
    }

    [Fact]
    public async Task RequestSubordinateFeedback_UserIsNotManagersSubordinate_ReturnsForbiddenAsync()
    {
        // Arrange
        SetAuthToken(Auth.ManagerToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = _giversIds,
                    Topic = FeedbackTopics.General,
                    Subject = "Subject",
                    Details = "Details"
                })
                .ToUrl("/v1/managers/me/subordinates/99905164/feedback");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.Forbidden);
        });
    }

    [Fact]
    public async Task RequestSubordinateFeedback_UserDoesNotHaveFeedbackPhase_ReturnsForbiddenAsync()
    {
        // Arrange
        SetAuthToken(Auth.FMillerToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = _giversIds,
                    Topic = FeedbackTopics.General,
                    Subject = "Subject",
                    Details = "Details"
                })
                .ToUrl("/v1/managers/me/subordinates/99905166/feedback");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.Forbidden);
        });
    }
}
