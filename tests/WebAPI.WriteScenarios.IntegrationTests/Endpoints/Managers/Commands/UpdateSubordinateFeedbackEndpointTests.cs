using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Helpers;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.Endpoints.Managers.Commands;

public class UpdateSubordinateFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    #region IsVisibleToReceiver

    [Fact]
    public async Task SetIsVisibleToReceiver_ToFalse_Saved()
    {
        // Arrange
        SetAuthToken(Auth.ManagerToken);

        var id = "65490283500050007";
        var subordinateId = "99905178";

        var before = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Id] = '{id}'")
            .Rows;

        Assert.Equal(true, before[0].IsVisibleToReceiver);

        // Act
        await Host.Scenario(api =>
        {
            api.Patch
                .Json(new
                {
                    IsVisibleToReceiver = false
                })
                .ToUrl($"/v1/managers/me/subordinates/{subordinateId}/feedback/{id}");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        // Assert
        var after = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Id] = '{id}'")
            .Rows;

        Assert.Equal(false, after[0].IsVisibleToReceiver);

        await Verify()
            .AppendValue(nameof(before), before)
            .AppendValue(nameof(after), after)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task SetIsVisibleToReceiver_IfNotReceiver_ReturnsError()
    {
        // Arrange
        SetAuthToken(Auth.ManagerToken);

        var id = "65490283500050104";
        var subordinateId = "99905178";

        var result = await Host.Scenario(api =>
        {
            // Act
            api.Patch
                .Json(new
                {
                    IsVisibleToReceiver = false
                })
                .ToUrl($"/v1/managers/me/subordinates/{subordinateId}/feedback/{id}");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        await VerifyJson(await result.ReadAsTextAsync())
            .IgnoreMembers("instance", "traceId");
    }

    #endregion IsVisibleToReceiver
}
