using Microsoft.Extensions.DependencyInjection;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Helpers;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.Endpoints.Requesters.Commands;

public class UpdateMyRequestedFeedbackEndpointTests : WebAppContext
{
    readonly TimeProvider _timeProvider;

    public UpdateMyRequestedFeedbackEndpointTests(WebAppFixture fixture) : base(fixture)
    {
        _timeProvider = Host.Services.GetRequiredService<TimeProvider>();
    }

    #region IsVisibleToManager

    [Fact]
    public async Task SetIsVisibleToManager_ToFalse_Saved()
    {
        // Arrange
        var id = "65490283500050005";

        var before = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Id] = '{id}'")
            .Rows;

        Assert.Equal(true, before[0].IsVisibleToManager);

        // Act
        await Host.Scenario(api =>
        {
            api.Patch
                .Json(new
                {
                    IsVisibleToManager = false
                })
                .ToUrl($"/v1/requesters/me/feedback/{id}");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        // Assert
        var after = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Id] = '{id}'")
            .Rows;

        Assert.Equal(false, after[0].IsVisibleToManager);

        await Verify()
            .AppendValue(nameof(before), before)
            .AppendValue(nameof(after), after)
            .DontScrubDateTimes();

    }

    [Fact]
    public async Task SetIsVisibleToManager_IfProvidedByManager_ReturnsError()
    {
        // Arrange
        var id = "65490283500050006";

        // Act
        var result = await Host.Scenario(api =>
        {
            api.Patch
                .Json(new
                {
                    IsVisibleToManager = false
                })
                .ToUrl($"/v1/requesters/me/feedback/{id}");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        await VerifyJson(await result.ReadAsTextAsync())
            .IgnoreMembers("instance", "traceId");
    }

    #endregion IsVisibleToManager

    #region Status

    [Fact]
    public async Task CancelFeedback_InPendingStatus_SavesWithdrawnDateAndStatus()
    {
        // Arrange
        SetAuthToken(Auth.HWilsonToken);
        var feedbackId = 65490283500050109;

        var before = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Id] = '{feedbackId}'")
            .Rows;

        Assert.Equal(FeedbackStatuses.Pending, (FeedbackStatuses)before[0].StatusId);
        Assert.Equal(DBNull.Value, before[0].WithdrawnDate);

        // Act
        await Host.Scenario(api =>
        {
            api.Patch
                .Json(new
                {
                    Status = FeedbackStatuses.Withdrawn
                })
                .ToUrl($"/v1/requesters/me/feedback/{feedbackId}");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        // Assert
        var after = DatabaseHelper
            .Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Id] = {feedbackId}")
            .Rows;

        Assert.Equal(FeedbackStatuses.Withdrawn, (FeedbackStatuses)after[0].StatusId);
        Assert.Equal(_timeProvider.GetUtcNow(), (DateTimeOffset)after[0].WithdrawnDate);

        await Verify()
            .AppendValue(nameof(before), before)
            .AppendValue(nameof(after), after)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task CancelFeedback_InWithdrawnStatus_NoChanges()
    {
        // Arrange
        SetAuthToken(Auth.HWilsonToken);
        var feedbackId = 65490283500050110;

        var before = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Id] = '{feedbackId}'")
            .Rows;

        Assert.Equal(FeedbackStatuses.Withdrawn, (FeedbackStatuses)before[0].StatusId);
        Assert.Equal(DBNull.Value, before[0].WithdrawnDate);

        // Act
        await Host.Scenario(api =>
        {
            api.Patch
                .Json(new
                {
                    Status = FeedbackStatuses.Withdrawn
                })
                .ToUrl($"/v1/requesters/me/feedback/{feedbackId}");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        // Assert
        var after = DatabaseHelper
            .Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Id] = {feedbackId}")
            .Rows;

        Assert.Equal(FeedbackStatuses.Withdrawn, (FeedbackStatuses)after[0].StatusId);
        Assert.Equal(DBNull.Value, after[0].WithdrawnDate);

        await Verify()
            .AppendValue(nameof(before), before)
            .AppendValue(nameof(after), after)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task CancelFeedback_InCompletedStatus_ReturnsError()
    {
        // Arrange
        var id = "65490283500050006";

        var before = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Id] = '{id}'")
            .Rows;

        Assert.Equal(FeedbackStatuses.Completed, (FeedbackStatuses)before[0].StatusId);

        // Act
        var result = await Host.Scenario(api =>
        {
            api.Patch
                .Json(new
                {
                    Status = FeedbackStatuses.Withdrawn
                })
                .ToUrl($"/v1/requesters/me/feedback/{id}");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        await VerifyJson(await result.ReadAsTextAsync())
            .IgnoreMembers("instance", "traceId");
    }

    #endregion Status
}
