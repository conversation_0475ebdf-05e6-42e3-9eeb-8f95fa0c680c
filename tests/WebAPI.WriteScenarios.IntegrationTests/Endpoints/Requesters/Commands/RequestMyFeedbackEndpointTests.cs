using Microsoft.Extensions.DependencyInjection;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Application.Approvals.Commands.Services;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Helpers;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.Endpoints.Requesters.Commands;

public class RequestMyFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    private static readonly string[] _giversIds = ["99905179", "99905182"];

    [Fact]
    public async Task RequestMyFeedback_SuccessFlow_ReturnsSuccessAsync()
    {
        // Arrange
        var subject = nameof(RequestMyFeedback_SuccessFlow_ReturnsSuccessAsync);
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = _giversIds,
                    Topic = FeedbackTopics.General,
                    Subject = subject,
                    Details = "Details"
                })
                .ToUrl("/v1/requesters/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.Created);
        });
        
        // Assert
        var result = DatabaseHelper.Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Subject] = '{subject}' ORDER BY [GiverId] DESC").Rows;
        
        await Verify(result)
            .IgnoreMember("Id")
            .IgnoreMember("CreatedDate")
            .IgnoreMember("LastModifiedDate")
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task RequestMyFeedback_WithEmptyGiversList_ReturnsBadRequestAsync()
    {
        // Arrange
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = Array.Empty<ItemId>(),
                    Topic = FeedbackTopics.General,
                    Subject = "Subject",
                    Details = "Details"
                })
                .ToUrl("/v1/requesters/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });
    }

    [Fact]
    public async Task RequestMyFeedback_UserDoesNotHaveFeedbackPhase_ReturnsForbiddenAsync()
    {
        // Arrange
        SetAuthToken(Auth.MJohnsonToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = _giversIds,
                    Topic = FeedbackTopics.General,
                    Subject = "Subject",
                    Details = "Details"
                })
                .ToUrl("/v1/requesters/me/feedback");

            // Assert
            api.StatusCodeShouldBe(HttpStatusCode.Forbidden);
        });
    }

    [Fact]
    public async Task RequestMyFeedback_WithObjectiveId_ReturnsSuccessAsync()
    {
        // Arrange
        var subject = nameof(RequestMyFeedback_WithObjectiveId_ReturnsSuccessAsync);
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = _giversIds,
                    Topic = FeedbackTopics.Objective,
                    ObjectiveId = new ItemId(43750282300060002),
                    Subject = subject,
                    Details = "Details"
                })
                .ToUrl("/v1/requesters/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.Created);
        });

        // Assert
        var result = DatabaseHelper.Execute($"SELECT * FROM [feedback].[Feedback] WHERE [Subject] = '{subject}' ORDER BY [GiverId] DESC").Rows;

        await Verify(result)
            .IgnoreMember("Id")
            .IgnoreMember("CreatedDate")
            .IgnoreMember("LastModifiedDate")
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task RequestMyFeedback_WithObjectiveTopicButWithoutObjectiveId_ReturnsBadRequestAsync()
    {
        // Arrange
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = Array.Empty<ItemId>(),
                    Topic = FeedbackTopics.Objective,
                    Details = "Details"
                })
                .ToUrl("/v1/requesters/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });
    }

    [Fact]
    public async Task RequestMyFeedback_WithTaskAndMilestoneId_ReturnsSuccessAsync()
    {
        // Arrange
        var subject = nameof(RequestMyFeedback_WithTaskAndMilestoneId_ReturnsSuccessAsync);
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = _giversIds,
                    Topic = FeedbackTopics.TaskAndMilestone,
                    TaskAndMilestoneId = new ItemId(33750282300060001),
                    Subject = subject,
                    Details = "Details"
                })
                .ToUrl("/v1/requesters/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.Created);
        });

        // Assert
        var result = DatabaseHelper.Execute($"SELECT TOP(1) * FROM [feedback].[Feedback] WHERE [Subject] = '{subject}' ORDER BY [Id] DESC").Rows;

        await Verify(result)
            .IgnoreMember("Id")
            .IgnoreMember("CreatedDate")
            .IgnoreMember("LastModifiedDate")
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task RequestMyFeedback_ResubmitWithdrawnRequest_ReturnsSuccessAsync()
    {
        // Arrange
        var subject = nameof(RequestMyFeedback_ResubmitWithdrawnRequest_ReturnsSuccessAsync);
        var feedbackId = new ItemId(65490283500050111);

        // Act
        await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    GiversIds = new string[] { "99905181", "99905179" },
                    Topic = FeedbackTopics.General,
                    Subject = subject,
                    Details = "Details",
                    SourceFeedbackId = feedbackId
                })
                .ToUrl("/v1/requesters/me/feedback");
            api.StatusCodeShouldBe(HttpStatusCode.Created);
        });

        // Assert
        var result = DatabaseHelper.Execute($"""
            SELECT * FROM [feedback].[Feedback]
            WHERE [Subject] = '{subject}' OR [Id] = {feedbackId}
            ORDER BY [Id] DESC
            """).Rows;

        await Verify(result)
            .IgnoreMember("Id")
            .IgnoreMember("CreatedDate")
            .IgnoreMember("LastModifiedDate")
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task RequestMyFeedback_SuccessFlow_WorkflowCreatedAsync()
    {
        const string FeedbackId = "65490283500050003";

        var approvalCommandService = Host.Services.GetRequiredService<IApprovalCommandService>();

        await approvalCommandService.CreateApprovalProcessAsync(new ItemId(FeedbackId), TestContext.Current.CancellationToken);

        var result = DatabaseHelper.Execute($"SELECT TOP(1) * FROM [feedback].[FeedbackToApprovals] WHERE [FeedbackId] = {FeedbackId}").Rows;

        // Assert
        await Verify(result).IgnoreMember("Version");
    }

    [Fact]
    public async Task RequestMyFeedback_WithCompletedFeedbackId_SkippedAsync()
    {
        const string FeedbackId = "65490283500050001";

        var approvalCommandService = Host.Services.GetRequiredService<IApprovalCommandService>();

        await approvalCommandService.CreateApprovalProcessAsync(new ItemId(FeedbackId), TestContext.Current.CancellationToken);

        var result = DatabaseHelper.Execute($"SELECT TOP(1) * FROM [feedback].[FeedbackToApprovals] WHERE [FeedbackId] = {FeedbackId}").Rows;

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task RequestMyFeedback_WithCreatedWorkflow_SkippedAsync()
    {
        const string FeedbackId = "65490283500050103";

        var approvalCommandService = Host.Services.GetRequiredService<IApprovalCommandService>();

        await approvalCommandService.CreateApprovalProcessAsync(new ItemId(FeedbackId), TestContext.Current.CancellationToken);
        await approvalCommandService.CreateApprovalProcessAsync(new ItemId(FeedbackId), TestContext.Current.CancellationToken);

        var result = DatabaseHelper.Execute($"SELECT TOP(1) * FROM [feedback].[FeedbackToApprovals] WHERE [FeedbackId] = {FeedbackId}").Rows;

        // Assert
        Assert.True(result.Count == 1);
    }
}
