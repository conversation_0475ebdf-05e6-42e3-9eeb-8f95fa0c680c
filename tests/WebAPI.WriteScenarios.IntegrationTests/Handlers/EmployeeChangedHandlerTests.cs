using MassTransit.Testing;
using OneTalent.FeedbackService.Application.Employees.Queries.Repositories;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Helpers;
using OneTalent.ServiceBus.Contracts.Topics.AtrCycleTopic;
using OneTalent.ServiceBus.Contracts.Topics.EmployeeTopic;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.Handlers;

public class EmployeeChangedHandlerTests : WebAppContext
{
    const int UpdatedEmployeeId = 1001;
    const int MovedToGroup0EmployeeId = 1002;

    public EmployeeChangedHandlerTests(WebAppFixture fixture) : base(fixture)
    {
        var employeeIndex = EmployeeMock.EmployeeList.FindIndex(x => x.EmployeeId == UpdatedEmployeeId);

        EmployeeMock.EmployeeList[employeeIndex] = EmployeeMock.EmployeeList[employeeIndex] with
        {
            Email = "<EMAIL>", FullNameEnglish = "New Name"
        };
        
        employeeIndex = EmployeeMock.Employee2GroupList.FindIndex(x => x.EmployeeId == MovedToGroup0EmployeeId);

        EmployeeMock.Employee2GroupList[employeeIndex] = EmployeeMock.Employee2GroupList[employeeIndex] with
        {
            GroupId = 0
        };
    }

    [Fact]
    public async Task EmployeeChangedHandler_SuccessFlow_UpdatesEmployeesTableAsync()
    {
        // Arrange, see constructor
        var harness = Host.Services.GetTestHarness();
        await harness.Start();
        // Act
        await harness.Bus.Publish(new EmployeeChanged { EmployeeId = $"{UpdatedEmployeeId}" }, TestContext.Current.CancellationToken);
        await harness.Bus.Publish(new AtrCycleUpdated(), TestContext.Current.CancellationToken);

        // Assert
        var consumed = false;
        var retries = 10;
        do
        {
            consumed = await harness.Consumed.Any<EmployeeChanged>(TestContext.Current.CancellationToken);
            await Task.Delay(200, TestContext.Current.CancellationToken);
            retries -= 1;
        } while (!consumed && retries > 0);

        Assert.True(consumed);
        var result = DatabaseHelper.Execute($"SELECT TOP(1) * FROM [feedback].[Employees] WHERE [Id] = {UpdatedEmployeeId}").Rows;
    
        await Verify(result)
            .UseTextForParameters("EmployeeUpdated")
            .DontScrubDateTimes();
        
        // Assert
        Assert.True(await harness.Consumed.Any<AtrCycleUpdated>(TestContext.Current.CancellationToken));
        result = DatabaseHelper
            .Execute($"SELECT * FROM [feedback].[Employees]").Rows;
    
        await Verify(result)
            .UseTextForParameters("EmployeeExcluded")
            .DontScrubDateTimes();
    }
}
