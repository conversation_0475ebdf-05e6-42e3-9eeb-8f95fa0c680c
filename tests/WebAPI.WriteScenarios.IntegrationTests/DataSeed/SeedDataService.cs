using Microsoft.Extensions.Hosting;
using OneTalent.FeedbackService.Infrastructure.DataAccessLayer;
using OneTalent.FeedbackService.Infrastructure.DataAccessLayer.SeedData;
using CommonTestsSeedingDataHelper = OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.DataSeed.TestsSeedingDataHelper;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.DataSeed;

internal sealed class SeedDataService : ISeedDataService
{
    public void SeedData(FeedbackDbContext dbContext, IHostEnvironment environment)
    {
        CommonTestsSeedingDataHelper.SeedLookups(dbContext);
        TestsSeedingDataHelper.SeedFeedbackData(dbContext);
        TestsSeedingDataHelper.SeedEmployeesData(dbContext);

        dbContext.SaveChanges();
    }
}
