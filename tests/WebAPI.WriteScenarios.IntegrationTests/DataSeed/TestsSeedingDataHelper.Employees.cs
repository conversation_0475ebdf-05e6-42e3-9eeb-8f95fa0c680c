using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Application.Access.Models;
using OneTalent.FeedbackService.Application.Employees.Queries.Repositories;
using OneTalent.FeedbackService.Infrastructure.DataAccessLayer;
using OneTalent.FeedbackService.Infrastructure.DataAccessLayer.Entities.Employee;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.DataSeed;

internal static partial class TestsSeedingDataHelper
{
    internal static void SeedEmployeesData(FeedbackDbContext dbContext)
    {
        dbContext.Employees.AddRange(EmployeeMock.EmployeeList.Select(x => 
            new EmployeeEntity
            {
                Id = x.EmployeeId, 
                GroupId = new ItemId((int)PhaseCodes.FeedbackPhase), 
                Email = x.Email, 
                FullNameEnglish = x.FullNameEnglish
            }
        ));

        dbContext.SaveChanges();
    }
}
