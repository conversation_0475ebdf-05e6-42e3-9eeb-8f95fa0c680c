using Microsoft.Extensions.DependencyInjection;
using OneTalent.FeedbackService.Application.Common.Constants;
using OneTalent.FeedbackService.Infrastructure.DataAccessLayer.SeedData;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.DataSeed;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests;

public class WebAppFixture : WebAppFixtureBase
{
    protected override int DbExternalPortNumber => 1438;

    protected override string EnvironmentName => EnvironmentNames.IntegrationWriteScenariosEnvironmentName;

    protected override void AddSeedService(IServiceCollection services)
    {
        services.Remove(services.First(s => s.ServiceType == typeof(ISeedDataService)));
        services.AddSingleton<ISeedDataService, SeedDataService>();
    }
}

[CollectionDefinition(nameof(WebAppCollection))]
public sealed class WebAppCollection : ICollectionFixture<WebAppFixture>;

[Collection(nameof(WebAppCollection))]
public class WebAppContext(WebAppFixture fixture, string? token = null) : WebAppContextBase(fixture, token);
