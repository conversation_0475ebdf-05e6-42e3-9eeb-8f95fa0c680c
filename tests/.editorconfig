root = true
# To learn more about .editorconfig see https://aka.ms/editorconfigdocs
###############################
# Core EditorConfig Options   #
###############################
# All files
[*]
indent_style = space
# Microsoft .NET properties
csharp_prefer_braces = true:warning
# ReSharper properties
resharper_braces_redundant = true
# Verify settings
[*.{received,verified}.{txt,xml,json}]
# charset = "utf-8-bom"
end_of_line = lf
insert_final_newline = false
trim_trailing_whitespace = false
# Xml files
[*.xml]
indent_size = 2
# XML project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_size = 2
# XML config files
[*.{props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct}]
indent_size = 2
# Code files
[*.{cs,csx,vb,vbx}]
indent_size = 4
insert_final_newline = true
charset = utf-8-bom
###############################
# .NET Coding Conventions     #
###############################
dotnet_diagnostic.CA1852.severity = none
###############################
# C# Coding Conventions       #
###############################
# C# files
[*.cs]
# ReSharper properties
resharper_align_multiline_array_and_object_initializer = false
#### .NET Coding Conventions ####
# Field preferences
dotnet_style_readonly_field = true
# Parameter preferences
dotnet_code_quality_unused_parameters = all
# Suppression preferences
dotnet_diagnostic.ide0079.severity = none
# New line preferences
dotnet_style_allow_multiple_blank_lines_experimental = false:error
dotnet_style_allow_statement_immediately_after_block_experimental = false:error
#### C# Coding Conventions ####
# var preferences
csharp_style_var_for_built_in_types = true:error
csharp_style_var_when_type_is_apparent = true:error
csharp_style_var_elsewhere = true:warning
# Expression-bodied members
csharp_style_expression_bodied_methods = when_on_single_line:suggestion
csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_operators = false:silent
csharp_style_expression_bodied_properties = when_on_single_line:suggestion
csharp_style_expression_bodied_indexers = when_on_single_line:suggestion
csharp_style_expression_bodied_accessors = when_on_single_line:suggestion
csharp_style_expression_bodied_lambdas = when_on_single_line:suggestion
csharp_style_expression_bodied_local_functions = when_on_single_line:suggestion
# Pattern matching preferences
csharp_style_pattern_matching_over_is_with_cast_check = true:error
csharp_style_pattern_matching_over_as_with_null_check = true:error
csharp_style_prefer_not_pattern = true:error
csharp_style_prefer_pattern_matching = true:warning
csharp_style_prefer_switch_expression = true:error
# Null-checking preferences
csharp_style_throw_expression = true:suggestion
csharp_style_conditional_delegate_call = true:error
# Modifier preferences
csharp_preferred_modifier_order = public, private, protected, internal, static, extern, new, virtual, abstract, sealed, override, readonly, unsafe, volatile, async
csharp_prefer_static_local_function = true:suggestion
# Expression-level preferences
csharp_prefer_simple_default_expression = true:error
csharp_style_deconstructed_variable_declaration = true:warning
csharp_style_implicit_object_creation_when_type_is_apparent = true:error
csharp_style_inlined_variable_declaration = true:error
csharp_style_pattern_local_over_anonymous_function = true:warning
csharp_style_prefer_index_operator = true:error
csharp_style_prefer_range_operator = true:error
csharp_style_unused_value_assignment_preference = discard_variable:suggestion
csharp_style_unused_value_expression_statement_preference = discard_variable:suggestion
# New line preferences
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_between_query_expression_clauses = true
csharp_style_allow_blank_line_after_colon_in_constructor_initializer_experimental = false:error
csharp_style_allow_blank_lines_between_consecutive_braces_experimental = false:error
csharp_style_allow_embedded_statements_on_same_line_experimental = false:error
# Indentation preferences
csharp_indent_case_contents = true
csharp_indent_switch_labels = true
csharp_indent_block_contents = true
csharp_indent_braces = false
csharp_indent_case_contents_when_block = false
csharp_indent_labels = flush_left
# Space preferences
csharp_space_after_cast = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_around_binary_operators = before_and_after
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false
csharp_space_between_method_declaration_name_and_open_parenthesis = false
csharp_space_after_comma = true
csharp_space_after_dot = false
csharp_space_after_semicolon_in_for_statement = true
csharp_space_around_declaration_statements = false
csharp_space_before_comma = false
csharp_space_before_dot = false
csharp_space_before_open_square_brackets = false
csharp_space_before_semicolon_in_for_statement = false
csharp_space_between_empty_square_brackets = false
csharp_space_between_square_brackets = false
# Wrapping preferences
csharp_preserve_single_line_statements = false
csharp_preserve_single_line_blocks = true
# 'using' directive preferences
csharp_using_directive_placement = outside_namespace:error
# Code-block preferences
csharp_prefer_simple_using_statement = true:warning
csharp_style_namespace_declarations = file_scoped:error
#### Naming styles ####
# Naming rules
dotnet_naming_rule.interface_should_be_begins_with_i.severity = suggestion
dotnet_naming_rule.interface_should_be_begins_with_i.symbols = interface
dotnet_naming_rule.interface_should_be_begins_with_i.style = begins_with_i
dotnet_naming_rule.types_should_be_pascal_case.severity = error
dotnet_naming_rule.types_should_be_pascal_case.symbols = types
dotnet_naming_rule.types_should_be_pascal_case.style = pascal_case_style
dotnet_naming_rule.non_field_members_should_be_pascal_case.severity = error
dotnet_naming_rule.non_field_members_should_be_pascal_case.symbols = non_field_members
dotnet_naming_rule.non_field_members_should_be_pascal_case.style = pascal_case_style
dotnet_naming_rule.async_methods_should_be_ends_with_async.severity = error
dotnet_naming_rule.async_methods_should_be_ends_with_async.symbols = async_method
dotnet_naming_rule.async_methods_should_be_ends_with_async.style = ends_with_async
dotnet_naming_rule.private_field_should_be_begins_with_underscore.severity = error
dotnet_naming_rule.private_field_should_be_begins_with_underscore.symbols = private_field
dotnet_naming_rule.private_field_should_be_begins_with_underscore.style = begins_with_underscore
dotnet_naming_rule.private_readonly_field_should_be_begins_with_underscore.severity = error
dotnet_naming_rule.private_readonly_field_should_be_begins_with_underscore.symbols = private_readonly_field
dotnet_naming_rule.private_readonly_field_should_be_begins_with_underscore.style = begins_with_underscore
dotnet_naming_rule.method_parameter_should_be_camel_case.severity = error
dotnet_naming_rule.method_parameter_should_be_camel_case.symbols = method_parameter
dotnet_naming_rule.method_parameter_should_be_camel_case.style = camel_case
# Symbol specifications
dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.interface.required_modifiers =
dotnet_naming_symbols.types.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.types.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.types.required_modifiers =
dotnet_naming_symbols.non_field_members.applicable_kinds = property, event, method
dotnet_naming_symbols.non_field_members.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.non_field_members.required_modifiers =
dotnet_naming_symbols.async_method.applicable_kinds = method
dotnet_naming_symbols.async_method.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected, local
dotnet_naming_symbols.async_method.required_modifiers = async
dotnet_naming_symbols.private_field.applicable_kinds = field
dotnet_naming_symbols.private_field.applicable_accessibilities = private
dotnet_naming_symbols.private_field.required_modifiers =
dotnet_naming_symbols.private_readonly_field.applicable_kinds = field
dotnet_naming_symbols.private_readonly_field.applicable_accessibilities = private
dotnet_naming_symbols.private_readonly_field.required_modifiers = readonly
dotnet_naming_symbols.method_parameter.applicable_kinds = parameter
dotnet_naming_symbols.method_parameter.applicable_accessibilities =
dotnet_naming_symbols.method_parameter.required_modifiers =
# Naming styles
### Naming rules
dotnet_naming_style.pascal_case.required_prefix =
dotnet_naming_style.pascal_case.required_suffix =
dotnet_naming_style.pascal_case.word_separator =
dotnet_naming_style.pascal_case.capitalization = pascal_case
dotnet_naming_rule.async_methods_should_be_ends_with_async.severity = none
dotnet_naming_style.camel_case.required_prefix =
dotnet_naming_style.camel_case.required_suffix =
dotnet_naming_style.camel_case.word_separator =
dotnet_naming_style.camel_case.capitalization = camel_case
dotnet_naming_style.begins_with_i.required_prefix = I
dotnet_naming_style.begins_with_i.required_suffix =
dotnet_naming_style.begins_with_i.word_separator =
dotnet_naming_style.begins_with_i.capitalization = pascal_case
dotnet_naming_style.ends_with_async.required_prefix =
dotnet_naming_style.ends_with_async.required_suffix = Async
dotnet_naming_style.ends_with_async.word_separator =
dotnet_naming_style.ends_with_async.capitalization = pascal_case
dotnet_naming_style.begins_with_underscore.required_prefix = _
dotnet_naming_style.begins_with_underscore.required_suffix =
dotnet_naming_style.begins_with_underscore.word_separator =
dotnet_naming_style.begins_with_underscore.capitalization = camel_case
csharp_style_prefer_method_group_conversion = true:suggestion
csharp_style_prefer_null_check_over_type_check = true:suggestion
csharp_style_prefer_local_over_anonymous_function = true:suggestion
csharp_style_prefer_tuple_swap = true:suggestion
csharp_style_prefer_parameter_null_checking = true:suggestion
csharp_style_prefer_extended_property_pattern = true:warning
csharp_style_prefer_top_level_statements = true:warning
csharp_style_prefer_primary_constructors = true:suggestion
[*.{cs,vb}]
dotnet_style_operator_placement_when_wrapping = beginning_of_line
tab_width = 4
end_of_line = crlf
dotnet_style_coalesce_expression = true:error
dotnet_style_null_propagation = true:error
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:error
dotnet_style_prefer_auto_properties = true:warning
dotnet_style_object_initializer = true:error
dotnet_style_collection_initializer = true:warning
dotnet_style_prefer_simplified_boolean_expressions = true:error
dotnet_style_allow_multiple_blank_lines_experimental = false:error
dotnet_style_allow_statement_immediately_after_block_experimental = false:error
dotnet_sort_system_directives_first = false
dotnet_separate_import_directive_groups = false
dotnet_style_qualification_for_field = false:error
dotnet_style_qualification_for_property = false:error
dotnet_style_qualification_for_method = false:error
dotnet_style_qualification_for_event = false:error
dotnet_style_predefined_type_for_locals_parameters_members = true:warning
dotnet_style_predefined_type_for_member_access = true:warning
dotnet_style_parentheses_in_arithmetic_binary_operators = always_for_clarity:silent
dotnet_style_parentheses_in_relational_binary_operators = always_for_clarity:warning
dotnet_style_parentheses_in_other_binary_operators = always_for_clarity:warning
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:silent
dotnet_style_require_accessibility_modifiers = for_non_interface_members:silent
dotnet_style_explicit_tuple_names = true:error
dotnet_style_namespace_match_folder = true:error
dotnet_style_prefer_inferred_tuple_names = true:warning
dotnet_style_prefer_compound_assignment = true:error
dotnet_style_prefer_conditional_expression_over_assignment = true:warning
dotnet_style_prefer_conditional_expression_over_return = true:warning
dotnet_style_prefer_inferred_anonymous_type_member_names = true:warning
dotnet_style_prefer_simplified_interpolation = true:suggestion
dotnet_diagnostic.ide0046.severity = none
dotnet_naming_style.pascal_case_style.capitalization = pascal_case
dotnet_naming_rule.constant_fields_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.constant_fields_should_be_pascal_case.symbols = constant_fields
dotnet_naming_rule.constant_fields_should_be_pascal_case.style = pascal_case_style
dotnet_naming_symbols.constant_fields.applicable_kinds = field
dotnet_naming_symbols.constant_fields.applicable_accessibilities = *
dotnet_naming_symbols.constant_fields.required_modifiers = const
