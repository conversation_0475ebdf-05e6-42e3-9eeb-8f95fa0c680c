{
  "Logging": {{ .Values.logging | toPrettyJson | indent 2 }},
  "PagingOptions": {
    "DefaultPageSize": {{ .Values.pagingOptions.defaultPageSize }},
    "MaxPageSize": {{ .Values.pagingOptions.maxPageSize }}
  },
  "Swagger": {
      "Enabled": "{{ .Values.swagger.enabled }}",
      "BaseUrl": "/api/proxy",
      "UseSsl": "{{ .Values.swagger.useSsl }}",
      "ServerRelativePath": "{{ .Values.swagger.serverRelativePath }}"
  },
  "AzureAd": {
      "Instance": "{{ .Values.azureAd.instance }}",
      "TenantId": "{{ .Values.azureAd.tenantId }}",
      "ClientId": "{{ .Values.azureAd.clientId }}",
      "ClientSecret": "{{ .Values.azureAd.clientSecret }}",
      "Audience": "{{ .Values.azureAd.audience }}",
      "AllowWebApiToBeAuthorizedByACL": true
  },
  "ApplicationInsights": {
    "ConnectionString": "{{ .Values.applicationInsights.connectionString }}",
  },
  "ConnectionStrings" : {
    "AtrAdminDb": "Server={{ .Values.database.server }};User Id={{ .Values.database.login }};Password={{ .Values.database.password }};{{ .Values.database.additionalParams }}",
    "AzureAppConfiguration": "{{ .Values.appConfig.connectionString }}",
    "RedisCaching": "{{ .Values.caching.connectionString }}",
    "BlobStorage": "{{ .Values.blobStorage.connectionString }}"
},
  "Audit": {
    "Enabled": true,
    "UseSeqDataProvider": true
  },
  "Caching": {
    "ExpirationInMinutes": "{{ .Values.caching.expirationInMinutes }}",
    "LocalCacheExpirationInMinutes": "{{ .Values.caching.localCacheExpirationInMinutes }}",
    "MaximumKeyLength": "{{ .Values.caching.maximumKeyLength }}",
    "Prefix": "{{ .Values.caching.prefix }}",
    "Namespace": "{{ .Values.caching.namespace }}",
  },
  "UserMapping": {
    "Enabled": {{ .Values.userMapping.enabled }},
    "BaseUrl": "{{ .Values.userMapping.baseUrl }}",
    "TargetScope": "{{ .Values.userMapping.targetScope }}",
    "ImpersonateAsUserFromHeader": {{ .Values.userMapping.impersonateAsUserFromHeader }}
  },
  "DataSyncServiceClient": {
    "BaseUrl": "{{ .Values.dataSyncService.baseUrl }}",
    "Scope": "{{ .Values.dataSyncService.scope }}"
  },
  "AtrServiceClient": {
    "BaseUrl": "{{ .Values.atrService.baseUrl }}",
    "Scope": "{{ .Values.atrService.scope }}"
  },
  "HttpLogging": {{ .Values.httpLogging | toPrettyJson | indent 4 }},
  "FeatureManagement": {{ .Values.featureManagement | toPrettyJson | indent 4 }},
  "MassTransit": {
    "ServiceBusConfiguration": {
      "IsEnabled": "{{ .Values.massTransit.isEnabled }}",
      "ConnectionString": "Endpoint={{ .Values.massTransit.endpoint }};SharedAccessKeyName={{ .Values.massTransit.sharedAccessKeyName }};SharedAccessKey={{ .Values.massTransit.sharedAccessKey }}",
      "EntityNamingConfiguration": {
        "QueueNamePrefix": "{{ .Values.massTransit.queueTopicPrefix }}",
        "TopicNamePrefix": "{{ .Values.massTransit.queueTopicPrefix }}",
        "SubscriptionNamePrefix": "{{ .Values.massTransit.subscriptionPrefix }}"
      },
      "Topics": {
        "AtrCycleTopic": {
          "IsEnabled": "{{ .Values.massTransit.topics.atrCycleTopic.isEnabled }}",
          "Subscriptions": {
            "AtrCycleChangedSubscription": {
              "IsEnabled": "{{ .Values.massTransit.topics.atrCycleTopic.subscriptions.atrCycleChangedSubscription.isEnabled }}"
            }
          }
        },
        "EmployeeTopic": {
          "IsEnabled": "{{ .Values.massTransit.topics.employeeTopic.isEnabled }}",
          "Subscriptions": {
            "EmployeeChangedSubscription": {
              "IsEnabled": "{{ .Values.massTransit.topics.employeeTopic.subscriptions.employeeChangedSubscription.isEnabled }}"
            }
          }
        },
        "FeedbackTopic": {
          "IsEnabled": "{{ .Values.massTransit.topics.feedbackTopic.isEnabled }}",
          "Subscriptions": {
            "FeedbackChangedSubscription": {
              "IsEnabled": "{{ .Values.massTransit.topics.feedbackTopic.subscriptions.feedbackChangedSubscription.isEnabled }}"
            }
          }
        }
      }
    }
  },
  "Hangfire": {
    "Setup": {
      "DashboardEnabled": "{{ .Values.hangfire.dashboard.enabled }}",
      "Login": "{{ .Values.hangfire.dashboard.login }}",
      "Password": "{{ .Values.hangfire.dashboard.password }}",
      "PrefixPath": "{{ .Values.hangfire.dashboard.prefixPath }}"
    },
    "InMemoryStorage": "{{ .Values.hangfire.inMemoryStorage }}",
    "BackgroundJobServerName": "{{ .Values.hangfire.backgroundJobServerName }}",
    "HangfireScheme": "{{ .Values.hangfire.hangfireScheme }}",
    "DbConnectionStringName": "{{ .Values.hangfire.dbConnectionStringName }}"
  },
  "BlobStorage": {
    "ContainerName": "{{ .Values.blobStorage.containerName }}",
    "AllowedFileFormats": [
    {{- range  $index, $format := .Values.blobStorage.allowedFileFormats }}
      {{- if $index }},{{ end }}
      {
        "Extension": "{{ $format.extension }}",
        "Signatures": {{ $format.signatures | toJson }},
        "SkipSignatureCheck": {{ $format.skipSignatureCheck }}
      }
    {{- end }}
    ]
  },
  {{ if empty .Values.seq }}
  "Seq": {
    "Enabled": false,
  }
  {{ else }}
  "Seq": {
      "Enabled": true,
      "Host": "{{ .Values.seq.host }}",
      "ApiKey": "{{ .Values.seq.apiKey }}"
  }
  {{ end }}
}