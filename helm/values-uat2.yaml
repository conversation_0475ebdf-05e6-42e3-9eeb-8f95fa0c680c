replicas: 2
name: onetalent-frontend-admin-uat2

# hpaConf:
#   reqCpu: 100m
#   reqMem: 1Gi
#   # limCpu: 500m # remove limit to avoid throttling
#   limMem: 1Gi
#   minRep: 1
#   maxRep: 2
#   avuCpu: 95
#   avuMem: 80

image:
  repository: acrshduatotl01.azurecr.io/fe-admin-web
  port: 80

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

applicationInsights:
  connectionString: mock
  enableAdaptiveSampling: false
  instrumentationKey: mock
  ingestionEndpoint: mock
  liveEndpoint: mock
  appId: mock

azureAd:
  instance: https://login.microsoftonline.com/
  tenantId: 74892fe7-b6cb-43e7-912b-52194d3fd7c8
  clientId: 1b634400-d854-40ab-be8e-06308b11437c
  clientApiId: 82cd72ca-0c69-4d5c-980b-20856c1d62fe
  clientSecret: mock
  audience: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/API.FullAccess"

admin:
  appUrl: "https://uat.onetalent.adnoc.ae/uat2/query"
  appPort: 80
  env: "uat2"
  apiUrl: "https://api.uat.onetalent.adnoc.ae/uat2/admin"
  appId: 5ca35830-e7b7-487c-85dc-435769317e66

seq:
  environmentName: "uat2"
  host: "http://seq-forwarder-uat.onetalent-uat.svc.cluster.local"
  apiKey: mock

ingress:
  host: uat.onetalent.adnoc.ae
  path: /uat2/admin
