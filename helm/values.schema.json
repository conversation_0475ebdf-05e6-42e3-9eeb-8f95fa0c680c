{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "definitions": {"LogLevelValue": {"type": "string", "enum": ["Trace", "Debug", "Information", "Warning", "Error", "Critical"]}}, "required": ["replicas", "name", "image", "imageVersion", "logging", "pagingOptions", "azureAd"], "additionalProperties": true, "properties": {"environment": {"type": "string"}, "replicas": {"type": "integer", "minimum": 1}, "name": {"type": "string"}, "hpaConf": {"type": "object", "additionalProperties": false, "required": ["reqCpu", "reqMem", "limMem", "minRep", "maxRep", "avuCpu", "avuMem"], "properties": {"reqCpu": {"type": "string", "minLength": 1}, "reqMem": {"type": "string", "minLength": 1}, "limCpu": {"type": "string", "minLength": 1}, "limMem": {"type": "string", "minLength": 1}, "minRep": {"type": "integer", "minLength": 1}, "maxRep": {"type": "integer", "minLength": 1}, "avuCpu": {"type": "integer", "minLength": 1}, "avuMem": {"type": "integer", "minLength": 1}}}, "image": {"type": "object", "additionalProperties": false, "required": ["repository"], "properties": {"repository": {"type": "string", "minLength": 1}, "port": {"type": "number", "minLength": 1}, "args": {"type": "array"}}}, "imageVersion": {"type": ["string", "number"]}, "initMigration": {"type": "boolean"}, "logging": {"type": "object", "required": ["MinimumLevel"], "properties": {"MinimumLevel": {"type": "object"}}}, "pagingOptions": {"type": "object", "required": ["defaultPageSize", "maxPageSize"], "properties": {"defaultPageSize": {"type": "number"}, "maxPageSize": {"type": "number"}}}, "applicationInsights": {"type": "object", "required": [], "properties": {"connectionString": {"type": "string", "minLength": 2}, "enableAdaptiveSampling": {"type": "boolean"}}}, "appConfig": {"type": "object", "required": [], "properties": {"connectionString": {"type": "string"}}}, "azureAd": {"type": "object", "additionalProperties": true, "required": ["instance", "tenantId", "clientId", "clientSecret"], "properties": {"instance": {"type": "string", "minLength": 2}, "tenantId": {"type": "string", "minLength": 2}, "clientId": {"type": "string", "minLength": 2}, "clientSecret": {"type": "string", "minLength": 2}, "audience": {"type": "string", "minLength": 2}}}, "swagger": {"type": "object", "additionalProperties": true, "required": [], "properties": {"enabled": {"type": "boolean"}, "useSsl": {"type": "boolean"}, "serverRelativePath": {"type": "string", "minLength": 2}}}, "seq": {"type": "object", "additionalProperties": false, "required": ["enabled", "<PERSON><PERSON><PERSON><PERSON>", "host"], "properties": {"enabled": {"type": "boolean"}, "apiKey": {"type": "string", "minLength": 2}, "host": {"type": "string", "minLength": 2}}}, "caching": {"type": "object", "required": ["expirationInMinutes", "localCacheExpirationInMinutes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix", "namespace"], "properties": {"connectionString": {"type": "string"}, "expirationInMinutes": {"type": "integer"}, "localCacheExpirationInMinutes": {"type": "integer"}, "maximumKeyLength": {"type": "integer"}, "prefix": {"type": "string"}, "namespace": {"type": "string"}}}, "ingress": {"type": "object", "additionalProperties": false, "required": ["host", "path"], "properties": {"host": {"type": "string", "minLength": 2}, "path": {"type": "string", "minLength": 2}, "corsAllowOrigin": {"type": "string", "minLength": 2}, "corsAllowHeaders": {"type": "string", "minLength": 2}}}, "dataSyncService": {"type": "object", "description": "Configuration settings for the DataSync Service", "properties": {"BaseUrl": {"type": "string", "description": "The base URL for the DataSync Service.", "default": "https://localhost:7009", "examples": ["https://api.example.com"]}, "Scope": {"type": "string", "description": "The OAuth2 scope required for accessing the DataSync Service.", "default": "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default", "examples": ["api://your-client-id/.default"]}}, "required": ["baseUrl", "scope"]}, "atrService": {"type": "object", "description": "Configuration settings for the Atr Service", "properties": {"BaseUrl": {"type": "string", "description": "The base URL for the Atr Service.", "default": "https://localhost:7024", "examples": ["https://api.example.com"]}, "Scope": {"type": "string", "description": "The OAuth2 scope required for accessing the Atr Service.", "default": "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default", "examples": ["api://your-client-id/.default"]}}, "required": ["baseUrl", "scope"]}, "database": {"type": "object", "additionalProperties": false, "required": [], "properties": {"login": {"type": "string", "minLength": 2}, "password": {"type": "string", "minLength": 2}, "server": {"type": "string", "minLength": 2}, "additionalParams": {"type": "string", "minLength": 2}}}, "userMapping": {"type": "object", "additionalProperties": false, "required": ["enabled"], "properties": {"enabled": {"scope": "boolean", "minLength": 2}, "baseUrl": {"type": "string"}, "targetScope": {"type": "string"}, "impersonateAsUserFromHeader": {"scope": "boolean", "minLength": 2}}}, "httpLogging": {"type": "object", "additionalProperties": true, "required": ["Disabled"], "properties": {"Disabled": {"scope": "boolean"}}}, "featureManagement": {"type": "object", "additionalProperties": true}, "massTransit": {"type": "object", "additionalProperties": false, "required": [], "properties": {"isEnabled": {"type": "boolean"}, "sharedAccessKey": {"type": "string"}, "sharedAccessKeyName": {"type": "string"}, "endpoint": {"type": "string"}, "queueTopicPrefix": {"type": "string"}, "subscriptionPrefix": {"type": "string"}, "topics": {"type": "object", "additionalProperties": false, "required": [], "properties": {"atrCycleTopic": {"type": "object", "additionalProperties": false, "required": [], "properties": {"isEnabled": {"type": "boolean"}, "subscriptions": {"type": "object", "additionalProperties": false, "required": [], "properties": {"atrCycleChangedSubscription": {"type": "object", "additionalProperties": false, "required": [], "properties": {"isEnabled": {"type": "boolean"}}}}}}}, "employeeTopic": {"type": "object", "additionalProperties": false, "required": [], "properties": {"isEnabled": {"type": "boolean"}, "subscriptions": {"type": "object", "additionalProperties": false, "required": [], "properties": {"employeeChangedSubscription": {"type": "object", "additionalProperties": false, "required": [], "properties": {"isEnabled": {"type": "boolean"}}}}}}}, "feedbackTopic": {"type": "object", "additionalProperties": false, "required": [], "properties": {"isEnabled": {"type": "boolean"}, "subscriptions": {"type": "object", "additionalProperties": false, "required": [], "properties": {"feedbackChangedSubscription": {"type": "object", "additionalProperties": false, "required": [], "properties": {"isEnabled": {"type": "boolean"}}}}}}}}}}}, "hangfire": {"type": "object", "additionalProperties": false, "required": ["dashboard", "inMemoryStorage", "backgroundJobServerName", "dbConnectionStringName", "hangfireScheme"], "properties": {"dashboard": {"type": "object", "additionalProperties": false, "required": ["enabled", "login", "prefixPath"], "properties": {"enabled": {"type": "boolean"}, "login": {"type": "string"}, "password": {"type": "string"}, "prefixPath": {"type": "string"}}}, "inMemoryStorage": {"type": "boolean"}, "backgroundJobServerName": {"type": "string"}, "dbConnectionStringName": {"type": "string"}, "hangfireScheme": {"type": "string"}}}, "blobStorage": {"type": "object", "additionalProperties": false, "required": ["containerName", "allowedFileFormats"], "properties": {"connectionString": {"type": "string", "minLength": 2}, "containerName": {"type": "string", "minLength": 2}, "maxFileSizeBytes": {"type": "number"}, "allowedFileFormats": {"type": "array", "items": {"type": "object", "additionalProperties": false, "required": ["extension", "signatures"], "properties": {"extension": {"type": "string", "minLength": 2}, "signatures": {"type": "array", "items": {"type": "string", "minLength": 0}, "default": []}, "skipSignatureCheck": {"type": "boolean", "default": false}}}}}}}}