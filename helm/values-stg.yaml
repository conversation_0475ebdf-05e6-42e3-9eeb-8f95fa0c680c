replicas: 2
name: onetalent-frontend-admin-stg

# hpaConf:
#   reqCpu: 100m
#   reqMem: 1Gi
#   # limCpu: 500m # remove limit to avoid throttling
#   limMem: 1Gi
#   minRep: 1
#   maxRep: 2
#   avuCpu: 95
#   avuMem: 80

image:
  repository: acrshduatotl01.azurecr.io/fe-admin-web
  port: 80

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

applicationInsights:
  connectionString: mock
  enableAdaptiveSampling: false
  instrumentationKey: mock
  ingestionEndpoint: mock
  liveEndpoint: mock
  appId: mock

azureAd:
  instance: https://login.microsoftonline.com/
  tenantId: 74892fe7-b6cb-43e7-912b-52194d3fd7c8
  clientId: 96a22b08-e20a-432d-8f86-097885a6cad4
  clientApiId: 82cd72ca-0c69-4d5c-980b-20856c1d62fe
  clientSecret: mock
  audience: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/API.FullAccess"

admin:
  env: stg
  appUrl: "https://stg.onetalent.adnoc.ae"
  appPort: 80
  apiUrl: "https://api.stg.onetalent.adnoc.ae/admin"
  appId: 682cc42d-ed3a-4433-a724-f0da00b45a26

seq:
  environmentName: "stg"
  host: "http://seq-forwarder-stg.onetalent-stg.svc.cluster.local"
  apiKey: mock

ingress:
  host: stg.onetalent.adnoc.ae
  path: /admin
