replicas: 2
name: onetalent-frontend-admin-preview

# hpaConf:
#   reqCpu: 100m
#   reqMem: 1Gi
#   # limCpu: 500m # remove limit to avoid throttling
#   limMem: 1Gi
#   minRep: 1
#   maxRep: 2
#   avuCpu: 95
#   avuMem: 80

image:
  repository: acrshduatotl01.azurecr.io/fe-admin-web
  port: 80

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

applicationInsights:
  connectionString: mock
  enableAdaptiveSampling: false
  instrumentationKey: mock
  ingestionEndpoint: mock
  liveEndpoint: mock
  appId: mock

azureAd:
  instance: https://login.microsoftonline.com/
  tenantId: 74892fe7-b6cb-43e7-912b-52194d3fd7c8
  clientId: d646f1e0-1ff6-4649-90b7-180c1c3fce7c
  clientApiId: 890c7a52-d30e-4d62-80ae-19f90750a0ef
  clientSecret: mock
  audience: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef"
  scope: "api://890c7a52-d30e-4d62-80ae-19f90750a0ef/API.FullAccess"

admin:
  appUrl: "https://onetalent.adnoc.ae"
  appPort: 80
  env: "preview"
  apiUrl: "https://api.preview.onetalent.adnoc.ae/admin"
  appId: 32b7389b-0398-492e-bc15-51d1db36b811

seq:
  environmentName: "preview"
  host: "http://seq-forwarder-preview.onetalent-preview.svc.cluster.local"
  apiKey: mock

ingress:
  host: preview.onetalent.adnoc.ae
  path: /admin
