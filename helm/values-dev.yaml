replicas: 2
name: onetalent-frontend-admin-dev

# hpaConf:
#   reqCpu: 100m
#   reqMem: 1Gi
#   # limCpu: 500m # remove limit to avoid throttling
#   limMem: 1Gi
#   minRep: 1
#   maxRep: 2
#   avuCpu: 95
#   avuMem: 80

image:
  repository: acrshduatotl01.azurecr.io/fe-admin-web
  port: 80

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

applicationInsights:
  connectionString: mock
  enableAdaptiveSampling: false
  instrumentationKey: mock
  ingestionEndpoint: mock
  liveEndpoint: mock
  appId: mock

azureAd:
  instance: https://login.microsoftonline.com/
  tenantId: 74892fe7-b6cb-43e7-912b-52194d3fd7c8
  clientId: 5c5046db-ac3a-4869-a755-6754efa110a2
  clientApiId: 82cd72ca-0c69-4d5c-980b-20856c1d62fe
  clientSecret: mock
  audience: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/API.FullAccess"

admin:
  appUrl: "https://dev.onetalent.adnoc.ae"
  appPort: 80
  env: "dev"
  apiUrl: "https://api.dev.onetalent.adnoc.ae/admin"
  appId: 175cdfdc-a2d1-4618-9780-f1d18c924a21


seq:
  environmentName: "dev"
  host: "http://seq-forwarder-dev.onetalent-dev.svc.cluster.local"
  apiKey: mock

ingress:
  host: dev.onetalent.adnoc.ae
  path: /admin
